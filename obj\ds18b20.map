Archive member included to satisfy reference by file (symbol)

d:/mounriver/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/rv32imacxw/ilp32\libgcc.a(gesf2.o)
                              ./Driver/brewing_control.o (__gesf2)
d:/mounriver/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/rv32imacxw/ilp32\libgcc.a(lesf2.o)
                              ./Driver/ds18b20.o (__lesf2)
d:/mounriver/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/rv32imacxw/ilp32\libgcc.a(mulsf3.o)
                              ./Driver/ds18b20.o (__mulsf3)
d:/mounriver/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/rv32imacxw/ilp32\libgcc.a(subsf3.o)
                              ./Driver/brewing_control.o (__subsf3)
d:/mounriver/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/rv32imacxw/ilp32\libgcc.a(fixunssfsi.o)
                              ./Driver/lcd.o (__fixunssfsi)
d:/mounriver/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/rv32imacxw/ilp32\libgcc.a(floatsisf.o)
                              ./Driver/ds18b20.o (__floatsisf)
d:/mounriver/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/rv32imacxw/ilp32\libgcc.a(extendsfdf2.o)
                              ./User/main.o (__extendsfdf2)
d:/mounriver/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/rv32imacxw/ilp32\libgcc.a(save-restore.o)
                              ./Driver/lcd.o (__riscv_save_12)
d:/mounriver/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/rv32imacxw/ilp32\libgcc.a(_clzsi2.o)
                              d:/mounriver/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/rv32imacxw/ilp32\libgcc.a(mulsf3.o) (__clzsi2)
d:/mounriver/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/rv32imacxw/ilp32\libgcc.a(_clz.o)
                              d:/mounriver/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/rv32imacxw/ilp32\libgcc.a(_clzsi2.o) (__clz_tab)
d:/mounriver/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libg_nano.a(lib_a-printf.o)
                              ./User/main.o (printf)
d:/mounriver/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libg_nano.a(lib_a-puts.o)
                              ./User/main.o (puts)
d:/mounriver/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libg_nano.a(lib_a-wbuf.o)
                              d:/mounriver/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libg_nano.a(lib_a-puts.o) (__swbuf_r)
d:/mounriver/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libg_nano.a(lib_a-wsetup.o)
                              d:/mounriver/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libg_nano.a(lib_a-puts.o) (__swsetup_r)
d:/mounriver/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libg_nano.a(lib_a-fflush.o)
                              d:/mounriver/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libg_nano.a(lib_a-wbuf.o) (_fflush_r)
d:/mounriver/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libg_nano.a(lib_a-findfp.o)
                              d:/mounriver/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libg_nano.a(lib_a-printf.o) (__sinit)
d:/mounriver/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libg_nano.a(lib_a-fwalk.o)
                              d:/mounriver/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libg_nano.a(lib_a-findfp.o) (_fwalk)
d:/mounriver/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libg_nano.a(lib_a-impure.o)
                              d:/mounriver/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libg_nano.a(lib_a-fflush.o) (_global_impure_ptr)
d:/mounriver/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libg_nano.a(lib_a-makebuf.o)
                              d:/mounriver/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libg_nano.a(lib_a-wsetup.o) (__smakebuf_r)
d:/mounriver/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libg_nano.a(lib_a-memset.o)
                              d:/mounriver/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libg_nano.a(lib_a-findfp.o) (memset)
d:/mounriver/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libg_nano.a(lib_a-nano-freer.o)
                              d:/mounriver/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libg_nano.a(lib_a-wsetup.o) (_free_r)
d:/mounriver/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libg_nano.a(lib_a-nano-mallocr.o)
                              d:/mounriver/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libg_nano.a(lib_a-findfp.o) (_malloc_r)
d:/mounriver/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libg_nano.a(lib_a-nano-vfprintf.o)
                              d:/mounriver/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libg_nano.a(lib_a-printf.o) (_vfprintf_r)
d:/mounriver/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libg_nano.a(lib_a-nano-vfprintf_i.o)
                              d:/mounriver/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libg_nano.a(lib_a-nano-vfprintf.o) (_printf_i)
d:/mounriver/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libg_nano.a(lib_a-sbrkr.o)
                              d:/mounriver/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libg_nano.a(lib_a-nano-mallocr.o) (_sbrk_r)
d:/mounriver/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libg_nano.a(lib_a-stdio.o)
                              d:/mounriver/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libg_nano.a(lib_a-findfp.o) (__sread)
d:/mounriver/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libg_nano.a(lib_a-writer.o)
                              d:/mounriver/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libg_nano.a(lib_a-stdio.o) (_write_r)
d:/mounriver/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libg_nano.a(lib_a-closer.o)
                              d:/mounriver/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libg_nano.a(lib_a-stdio.o) (_close_r)
d:/mounriver/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libg_nano.a(lib_a-fstatr.o)
                              d:/mounriver/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libg_nano.a(lib_a-makebuf.o) (_fstat_r)
d:/mounriver/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libg_nano.a(lib_a-fvwrite.o)
                              d:/mounriver/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libg_nano.a(lib_a-nano-vfprintf.o) (__sfvwrite_r)
d:/mounriver/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libg_nano.a(lib_a-isattyr.o)
                              d:/mounriver/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libg_nano.a(lib_a-makebuf.o) (_isatty_r)
d:/mounriver/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libg_nano.a(lib_a-lseekr.o)
                              d:/mounriver/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libg_nano.a(lib_a-stdio.o) (_lseek_r)
d:/mounriver/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libg_nano.a(lib_a-memchr.o)
                              d:/mounriver/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libg_nano.a(lib_a-nano-vfprintf.o) (memchr)
d:/mounriver/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libg_nano.a(lib_a-memcpy.o)
                              d:/mounriver/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libg_nano.a(lib_a-fvwrite.o) (memcpy)
d:/mounriver/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libg_nano.a(lib_a-memmove.o)
                              d:/mounriver/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libg_nano.a(lib_a-fvwrite.o) (memmove)
d:/mounriver/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libg_nano.a(lib_a-mlock.o)
                              d:/mounriver/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libg_nano.a(lib_a-nano-freer.o) (__malloc_lock)
d:/mounriver/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libg_nano.a(lib_a-nano-reallocr.o)
                              d:/mounriver/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libg_nano.a(lib_a-fvwrite.o) (_realloc_r)
d:/mounriver/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libg_nano.a(lib_a-readr.o)
                              d:/mounriver/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libg_nano.a(lib_a-stdio.o) (_read_r)
d:/mounriver/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libg_nano.a(lib_a-reent.o)
                              d:/mounriver/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libg_nano.a(lib_a-sbrkr.o) (errno)
d:/mounriver/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libg_nano.a(lib_a-nano-msizer.o)
                              d:/mounriver/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libg_nano.a(lib_a-nano-reallocr.o) (_malloc_usable_size_r)
d:/mounriver/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libnosys.a(close.o)
                              d:/mounriver/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libg_nano.a(lib_a-closer.o) (_close)
d:/mounriver/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libnosys.a(fstat.o)
                              d:/mounriver/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libg_nano.a(lib_a-fstatr.o) (_fstat)
d:/mounriver/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libnosys.a(isatty.o)
                              d:/mounriver/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libg_nano.a(lib_a-isattyr.o) (_isatty)
d:/mounriver/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libnosys.a(lseek.o)
                              d:/mounriver/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libg_nano.a(lib_a-lseekr.o) (_lseek)
d:/mounriver/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libnosys.a(read.o)
                              d:/mounriver/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libg_nano.a(lib_a-readr.o) (_read)

Allocating common symbols
Common symbol       size              file

errno               0x4               d:/mounriver/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libg_nano.a(lib_a-reent.o)

Discarded input sections

 .text          0x***********00000        0x0 ./User/ch32v30x_it.o
 .data          0x***********00000        0x0 ./User/ch32v30x_it.o
 .bss           0x***********00000        0x0 ./User/ch32v30x_it.o
 .text          0x***********00000        0x0 ./User/main.o
 .data          0x***********00000        0x0 ./User/main.o
 .bss           0x***********00000        0x0 ./User/main.o
 .text.temp_proc
                0x***********00000       0x94 ./User/main.o
 .text.water_level_proc
                0x***********00000       0x34 ./User/main.o
 .text.scheduler_init
                0x***********00000        0xc ./User/main.o
 .rodata.gImage_1
                0x***********00000     0x8008 ./User/main.o
 .rodata.gImage_2
                0x***********00000     0x8000 ./User/main.o
 .rodata.temp_proc.str1.4
                0x***********00000       0x2f ./User/main.o
 .rodata.water_level_proc.str1.4
                0x***********00000       0x16 ./User/main.o
 .sbss.water_lvel_flag
                0x***********00000        0x1 ./User/main.o
 .sdata.threshold
                0x***********00000        0x4 ./User/main.o
 .text          0x***********00000        0x0 ./User/system_ch32v30x.o
 .data          0x***********00000        0x0 ./User/system_ch32v30x.o
 .bss           0x***********00000        0x0 ./User/system_ch32v30x.o
 .text          0x***********00000        0x0 ./Startup/startup_ch32v30x_D8C.o
 .data          0x***********00000        0x0 ./Startup/startup_ch32v30x_D8C.o
 .bss           0x***********00000        0x0 ./Startup/startup_ch32v30x_D8C.o
 .text          0x***********00000        0x0 ./Peripheral/src/ch32v30x_adc.o
 .data          0x***********00000        0x0 ./Peripheral/src/ch32v30x_adc.o
 .bss           0x***********00000        0x0 ./Peripheral/src/ch32v30x_adc.o
 .text.ADC_DeInit
                0x***********00000       0x5a ./Peripheral/src/ch32v30x_adc.o
 .text.ADC_Init
                0x***********00000       0x56 ./Peripheral/src/ch32v30x_adc.o
 .text.ADC_StructInit
                0x***********00000       0x1a ./Peripheral/src/ch32v30x_adc.o
 .text.ADC_Cmd  0x***********00000       0x10 ./Peripheral/src/ch32v30x_adc.o
 .text.ADC_DMACmd
                0x***********00000       0x12 ./Peripheral/src/ch32v30x_adc.o
 .text.ADC_ITConfig
                0x***********00000       0x16 ./Peripheral/src/ch32v30x_adc.o
 .text.ADC_ResetCalibration
                0x***********00000        0xa ./Peripheral/src/ch32v30x_adc.o
 .text.ADC_GetResetCalibrationStatus
                0x***********00000        0x8 ./Peripheral/src/ch32v30x_adc.o
 .text.ADC_StartCalibration
                0x***********00000        0xa ./Peripheral/src/ch32v30x_adc.o
 .text.ADC_GetCalibrationStatus
                0x***********00000        0x8 ./Peripheral/src/ch32v30x_adc.o
 .text.ADC_SoftwareStartConvCmd
                0x***********00000       0x18 ./Peripheral/src/ch32v30x_adc.o
 .text.ADC_GetSoftwareStartConvStatus
                0x***********00000        0x8 ./Peripheral/src/ch32v30x_adc.o
 .text.ADC_DiscModeChannelCountConfig
                0x***********00000       0x12 ./Peripheral/src/ch32v30x_adc.o
 .text.ADC_DiscModeCmd
                0x***********00000       0x1a ./Peripheral/src/ch32v30x_adc.o
 .text.ADC_RegularChannelConfig
                0x***********00000       0xb8 ./Peripheral/src/ch32v30x_adc.o
 .text.ADC_ExternalTrigConvCmd
                0x***********00000       0x18 ./Peripheral/src/ch32v30x_adc.o
 .text.ADC_GetConversionValue
                0x***********00000        0x8 ./Peripheral/src/ch32v30x_adc.o
 .text.ADC_GetDualModeConversionValue
                0x***********00000        0xa ./Peripheral/src/ch32v30x_adc.o
 .text.ADC_AutoInjectedConvCmd
                0x***********00000       0x12 ./Peripheral/src/ch32v30x_adc.o
 .text.ADC_InjectedDiscModeCmd
                0x***********00000       0x14 ./Peripheral/src/ch32v30x_adc.o
 .text.ADC_ExternalTrigInjectedConvConfig
                0x***********00000        0xe ./Peripheral/src/ch32v30x_adc.o
 .text.ADC_ExternalTrigInjectedConvCmd
                0x***********00000       0x14 ./Peripheral/src/ch32v30x_adc.o
 .text.ADC_SoftwareStartInjectedConvCmd
                0x***********00000       0x18 ./Peripheral/src/ch32v30x_adc.o
 .text.ADC_GetSoftwareStartInjectedConvCmdStatus
                0x***********00000        0x8 ./Peripheral/src/ch32v30x_adc.o
 .text.ADC_InjectedChannelConfig
                0x***********00000       0x7a ./Peripheral/src/ch32v30x_adc.o
 .text.ADC_InjectedSequencerLengthConfig
                0x***********00000       0x14 ./Peripheral/src/ch32v30x_adc.o
 .text.ADC_SetInjectedOffset
                0x***********00000       0x14 ./Peripheral/src/ch32v30x_adc.o
 .text.ADC_GetInjectedConversionValue
                0x***********00000       0x1c ./Peripheral/src/ch32v30x_adc.o
 .text.ADC_AnalogWatchdogCmd
                0x***********00000       0x12 ./Peripheral/src/ch32v30x_adc.o
 .text.ADC_AnalogWatchdogThresholdsConfig
                0x***********00000        0x6 ./Peripheral/src/ch32v30x_adc.o
 .text.ADC_AnalogWatchdogSingleChannelConfig
                0x***********00000        0xa ./Peripheral/src/ch32v30x_adc.o
 .text.ADC_TempSensorVrefintCmd
                0x***********00000       0x20 ./Peripheral/src/ch32v30x_adc.o
 .text.ADC_GetFlagStatus
                0x***********00000        0xa ./Peripheral/src/ch32v30x_adc.o
 .text.ADC_ClearFlag
                0x***********00000        0x8 ./Peripheral/src/ch32v30x_adc.o
 .text.ADC_GetITStatus
                0x***********00000       0x1c ./Peripheral/src/ch32v30x_adc.o
 .text.ADC_ClearITPendingBit
                0x***********00000        0xa ./Peripheral/src/ch32v30x_adc.o
 .text.TempSensor_Volt_To_Temper
                0x***********00000       0x28 ./Peripheral/src/ch32v30x_adc.o
 .text.ADC_BufferCmd
                0x***********00000       0x18 ./Peripheral/src/ch32v30x_adc.o
 .text.Get_CalibrationValue
                0x***********00000      0x144 ./Peripheral/src/ch32v30x_adc.o
 .debug_info    0x***********00000     0x1703 ./Peripheral/src/ch32v30x_adc.o
 .debug_abbrev  0x***********00000      0x3a9 ./Peripheral/src/ch32v30x_adc.o
 .debug_loc     0x***********00000      0xab6 ./Peripheral/src/ch32v30x_adc.o
 .debug_aranges
                0x***********00000      0x150 ./Peripheral/src/ch32v30x_adc.o
 .debug_ranges  0x***********00000      0x140 ./Peripheral/src/ch32v30x_adc.o
 .debug_line    0x***********00000     0x180b ./Peripheral/src/ch32v30x_adc.o
 .debug_str     0x***********00000      0xbfd ./Peripheral/src/ch32v30x_adc.o
 .comment       0x***********00000       0x34 ./Peripheral/src/ch32v30x_adc.o
 .debug_frame   0x***********00000      0x2b4 ./Peripheral/src/ch32v30x_adc.o
 .text          0x***********00000        0x0 ./Peripheral/src/ch32v30x_bkp.o
 .data          0x***********00000        0x0 ./Peripheral/src/ch32v30x_bkp.o
 .bss           0x***********00000        0x0 ./Peripheral/src/ch32v30x_bkp.o
 .text.BKP_DeInit
                0x***********00000       0x24 ./Peripheral/src/ch32v30x_bkp.o
 .text.BKP_TamperPinLevelConfig
                0x***********00000       0x20 ./Peripheral/src/ch32v30x_bkp.o
 .text.BKP_TamperPinCmd
                0x***********00000       0x20 ./Peripheral/src/ch32v30x_bkp.o
 .text.BKP_ITConfig
                0x***********00000       0x20 ./Peripheral/src/ch32v30x_bkp.o
 .text.BKP_RTCOutputConfig
                0x***********00000       0x18 ./Peripheral/src/ch32v30x_bkp.o
 .text.BKP_SetRTCCalibrationValue
                0x***********00000       0x18 ./Peripheral/src/ch32v30x_bkp.o
 .text.BKP_WriteBackupRegister
                0x***********00000       0x1c ./Peripheral/src/ch32v30x_bkp.o
 .text.BKP_ReadBackupRegister
                0x***********00000       0x1c ./Peripheral/src/ch32v30x_bkp.o
 .text.BKP_GetFlagStatus
                0x***********00000        0xe ./Peripheral/src/ch32v30x_bkp.o
 .text.BKP_ClearFlag
                0x***********00000       0x12 ./Peripheral/src/ch32v30x_bkp.o
 .text.BKP_GetITStatus
                0x***********00000        0xe ./Peripheral/src/ch32v30x_bkp.o
 .text.BKP_ClearITPendingBit
                0x***********00000       0x12 ./Peripheral/src/ch32v30x_bkp.o
 .debug_info    0x***********00000     0x10d3 ./Peripheral/src/ch32v30x_bkp.o
 .debug_abbrev  0x***********00000      0x2cc ./Peripheral/src/ch32v30x_bkp.o
 .debug_loc     0x***********00000       0xd8 ./Peripheral/src/ch32v30x_bkp.o
 .debug_aranges
                0x***********00000       0x78 ./Peripheral/src/ch32v30x_bkp.o
 .debug_ranges  0x***********00000       0x68 ./Peripheral/src/ch32v30x_bkp.o
 .debug_line    0x***********00000      0x608 ./Peripheral/src/ch32v30x_bkp.o
 .debug_str     0x***********00000      0xa81 ./Peripheral/src/ch32v30x_bkp.o
 .comment       0x***********00000       0x34 ./Peripheral/src/ch32v30x_bkp.o
 .debug_frame   0x***********00000       0xec ./Peripheral/src/ch32v30x_bkp.o
 .text          0x***********00000        0x0 ./Peripheral/src/ch32v30x_can.o
 .data          0x***********00000        0x0 ./Peripheral/src/ch32v30x_can.o
 .bss           0x***********00000        0x0 ./Peripheral/src/ch32v30x_can.o
 .text.CAN_DeInit
                0x***********00000       0x4c ./Peripheral/src/ch32v30x_can.o
 .text.CAN_Init
                0x***********00000       0xe8 ./Peripheral/src/ch32v30x_can.o
 .text.CAN_FilterInit
                0x***********00000       0xee ./Peripheral/src/ch32v30x_can.o
 .text.CAN_StructInit
                0x***********00000       0x24 ./Peripheral/src/ch32v30x_can.o
 .text.CAN_SlaveStartBank
                0x***********00000       0x38 ./Peripheral/src/ch32v30x_can.o
 .text.CAN_DBGFreeze
                0x***********00000       0x14 ./Peripheral/src/ch32v30x_can.o
 .text.CAN_TTComModeCmd
                0x***********00000       0x58 ./Peripheral/src/ch32v30x_can.o
 .text.CAN_Transmit
                0x***********00000       0xbc ./Peripheral/src/ch32v30x_can.o
 .text.CAN_TransmitStatus
                0x***********00000       0x62 ./Peripheral/src/ch32v30x_can.o
 .text.CAN_CancelTransmit
                0x***********00000       0x2a ./Peripheral/src/ch32v30x_can.o
 .text.CAN_Receive
                0x***********00000       0x8c ./Peripheral/src/ch32v30x_can.o
 .text.CAN_FIFORelease
                0x***********00000       0x16 ./Peripheral/src/ch32v30x_can.o
 .text.CAN_MessagePending
                0x***********00000       0x16 ./Peripheral/src/ch32v30x_can.o
 .text.CAN_OperatingModeRequest
                0x***********00000       0x72 ./Peripheral/src/ch32v30x_can.o
 .text.CAN_Sleep
                0x***********00000       0x16 ./Peripheral/src/ch32v30x_can.o
 .text.CAN_WakeUp
                0x***********00000       0x1e ./Peripheral/src/ch32v30x_can.o
 .text.CAN_GetLastErrorCode
                0x***********00000        0x8 ./Peripheral/src/ch32v30x_can.o
 .text.CAN_GetReceiveErrorCounter
                0x***********00000        0x6 ./Peripheral/src/ch32v30x_can.o
 .text.CAN_GetLSBTransmitErrorCounter
                0x***********00000        0xa ./Peripheral/src/ch32v30x_can.o
 .text.CAN_ITConfig
                0x***********00000       0x12 ./Peripheral/src/ch32v30x_can.o
 .text.CAN_GetFlagStatus
                0x***********00000       0x56 ./Peripheral/src/ch32v30x_can.o
 .text.CAN_ClearFlag
                0x***********00000       0x40 ./Peripheral/src/ch32v30x_can.o
 .text.CAN_GetITStatus
                0x***********00000       0xd0 ./Peripheral/src/ch32v30x_can.o
 .text.CAN_ClearITPendingBit
                0x***********00000       0x94 ./Peripheral/src/ch32v30x_can.o
 .debug_info    0x***********00000     0x1807 ./Peripheral/src/ch32v30x_can.o
 .debug_abbrev  0x***********00000      0x3eb ./Peripheral/src/ch32v30x_can.o
 .debug_loc     0x***********00000      0x999 ./Peripheral/src/ch32v30x_can.o
 .debug_aranges
                0x***********00000       0xd8 ./Peripheral/src/ch32v30x_can.o
 .debug_ranges  0x***********00000       0xf8 ./Peripheral/src/ch32v30x_can.o
 .debug_line    0x***********00000     0x19cb ./Peripheral/src/ch32v30x_can.o
 .debug_str     0x***********00000      0xb97 ./Peripheral/src/ch32v30x_can.o
 .comment       0x***********00000       0x34 ./Peripheral/src/ch32v30x_can.o
 .debug_frame   0x***********00000      0x19c ./Peripheral/src/ch32v30x_can.o
 .text          0x***********00000        0x0 ./Peripheral/src/ch32v30x_crc.o
 .data          0x***********00000        0x0 ./Peripheral/src/ch32v30x_crc.o
 .bss           0x***********00000        0x0 ./Peripheral/src/ch32v30x_crc.o
 .text.CRC_ResetDR
                0x***********00000        0xa ./Peripheral/src/ch32v30x_crc.o
 .text.CRC_CalcCRC
                0x***********00000        0xa ./Peripheral/src/ch32v30x_crc.o
 .text.CRC_CalcBlockCRC
                0x***********00000       0x20 ./Peripheral/src/ch32v30x_crc.o
 .text.CRC_GetCRC
                0x***********00000        0x8 ./Peripheral/src/ch32v30x_crc.o
 .text.CRC_SetIDRegister
                0x***********00000        0x8 ./Peripheral/src/ch32v30x_crc.o
 .text.CRC_GetIDRegister
                0x***********00000        0x8 ./Peripheral/src/ch32v30x_crc.o
 .debug_info    0x***********00000      0xab3 ./Peripheral/src/ch32v30x_crc.o
 .debug_abbrev  0x***********00000      0x25f ./Peripheral/src/ch32v30x_crc.o
 .debug_loc     0x***********00000       0x75 ./Peripheral/src/ch32v30x_crc.o
 .debug_aranges
                0x***********00000       0x48 ./Peripheral/src/ch32v30x_crc.o
 .debug_ranges  0x***********00000       0x38 ./Peripheral/src/ch32v30x_crc.o
 .debug_line    0x***********00000      0x37a ./Peripheral/src/ch32v30x_crc.o
 .debug_str     0x***********00000      0x657 ./Peripheral/src/ch32v30x_crc.o
 .comment       0x***********00000       0x34 ./Peripheral/src/ch32v30x_crc.o
 .debug_frame   0x***********00000       0x70 ./Peripheral/src/ch32v30x_crc.o
 .text          0x***********00000        0x0 ./Peripheral/src/ch32v30x_dac.o
 .data          0x***********00000        0x0 ./Peripheral/src/ch32v30x_dac.o
 .bss           0x***********00000        0x0 ./Peripheral/src/ch32v30x_dac.o
 .text.DAC_DeInit
                0x***********00000       0x2c ./Peripheral/src/ch32v30x_dac.o
 .text.DAC_Init
                0x***********00000       0x30 ./Peripheral/src/ch32v30x_dac.o
 .text.DAC_StructInit
                0x***********00000       0x12 ./Peripheral/src/ch32v30x_dac.o
 .text.DAC_Cmd  0x***********00000       0x2c ./Peripheral/src/ch32v30x_dac.o
 .text.DAC_DMACmd
                0x***********00000       0x2c ./Peripheral/src/ch32v30x_dac.o
 .text.DAC_SoftwareTriggerCmd
                0x***********00000       0x22 ./Peripheral/src/ch32v30x_dac.o
 .text.DAC_DualSoftwareTriggerCmd
                0x***********00000       0x18 ./Peripheral/src/ch32v30x_dac.o
 .text.DAC_WaveGenerationCmd
                0x***********00000       0x1e ./Peripheral/src/ch32v30x_dac.o
 .text.DAC_SetChannel1Data
                0x***********00000       0x1e ./Peripheral/src/ch32v30x_dac.o
 .text.DAC_SetChannel2Data
                0x***********00000       0x1e ./Peripheral/src/ch32v30x_dac.o
 .text.DAC_SetDualChannelData
                0x***********00000       0x1c ./Peripheral/src/ch32v30x_dac.o
 .text.DAC_GetDataOutputValue
                0x***********00000       0x26 ./Peripheral/src/ch32v30x_dac.o
 .debug_info    0x***********00000      0xdbd ./Peripheral/src/ch32v30x_dac.o
 .debug_abbrev  0x***********00000      0x2fe ./Peripheral/src/ch32v30x_dac.o
 .debug_loc     0x***********00000      0x238 ./Peripheral/src/ch32v30x_dac.o
 .debug_aranges
                0x***********00000       0x78 ./Peripheral/src/ch32v30x_dac.o
 .debug_ranges  0x***********00000       0x68 ./Peripheral/src/ch32v30x_dac.o
 .debug_line    0x***********00000      0x788 ./Peripheral/src/ch32v30x_dac.o
 .debug_str     0x***********00000      0x7db ./Peripheral/src/ch32v30x_dac.o
 .comment       0x***********00000       0x34 ./Peripheral/src/ch32v30x_dac.o
 .debug_frame   0x***********00000       0xf4 ./Peripheral/src/ch32v30x_dac.o
 .text          0x***********00000        0x0 ./Peripheral/src/ch32v30x_dbgmcu.o
 .data          0x***********00000        0x0 ./Peripheral/src/ch32v30x_dbgmcu.o
 .bss           0x***********00000        0x0 ./Peripheral/src/ch32v30x_dbgmcu.o
 .text.DBGMCU_GetREVID
                0x***********00000        0xa ./Peripheral/src/ch32v30x_dbgmcu.o
 .text.DBGMCU_GetDEVID
                0x***********00000        0xa ./Peripheral/src/ch32v30x_dbgmcu.o
 .text.__get_DEBUG_CR
                0x***********00000        0x6 ./Peripheral/src/ch32v30x_dbgmcu.o
 .text.__set_DEBUG_CR
                0x***********00000        0x6 ./Peripheral/src/ch32v30x_dbgmcu.o
 .text.DBGMCU_Config
                0x***********00000       0x14 ./Peripheral/src/ch32v30x_dbgmcu.o
 .text.DBGMCU_GetCHIPID
                0x***********00000        0xa ./Peripheral/src/ch32v30x_dbgmcu.o
 .debug_info    0x***********00000      0xa94 ./Peripheral/src/ch32v30x_dbgmcu.o
 .debug_abbrev  0x***********00000      0x2f7 ./Peripheral/src/ch32v30x_dbgmcu.o
 .debug_loc     0x***********00000       0x7c ./Peripheral/src/ch32v30x_dbgmcu.o
 .debug_aranges
                0x***********00000       0x48 ./Peripheral/src/ch32v30x_dbgmcu.o
 .debug_ranges  0x***********00000       0x50 ./Peripheral/src/ch32v30x_dbgmcu.o
 .debug_line    0x***********00000      0x33d ./Peripheral/src/ch32v30x_dbgmcu.o
 .debug_str     0x***********00000      0x622 ./Peripheral/src/ch32v30x_dbgmcu.o
 .comment       0x***********00000       0x34 ./Peripheral/src/ch32v30x_dbgmcu.o
 .debug_frame   0x***********00000       0x70 ./Peripheral/src/ch32v30x_dbgmcu.o
 .text          0x***********00000        0x0 ./Peripheral/src/ch32v30x_dma.o
 .data          0x***********00000        0x0 ./Peripheral/src/ch32v30x_dma.o
 .bss           0x***********00000        0x0 ./Peripheral/src/ch32v30x_dma.o
 .text.DMA_DeInit
                0x***********00000      0x16c ./Peripheral/src/ch32v30x_dma.o
 .text.DMA_Init
                0x***********00000       0x38 ./Peripheral/src/ch32v30x_dma.o
 .text.DMA_StructInit
                0x***********00000       0x2e ./Peripheral/src/ch32v30x_dma.o
 .text.DMA_Cmd  0x***********00000       0x14 ./Peripheral/src/ch32v30x_dma.o
 .text.DMA_ITConfig
                0x***********00000       0x12 ./Peripheral/src/ch32v30x_dma.o
 .text.DMA_SetCurrDataCounter
                0x***********00000        0x4 ./Peripheral/src/ch32v30x_dma.o
 .text.DMA_GetCurrDataCounter
                0x***********00000        0x8 ./Peripheral/src/ch32v30x_dma.o
 .text.DMA_GetFlagStatus
                0x***********00000       0x32 ./Peripheral/src/ch32v30x_dma.o
 .text.DMA_ClearFlag
                0x***********00000       0x2c ./Peripheral/src/ch32v30x_dma.o
 .text.DMA_GetITStatus
                0x***********00000       0x18 ./Peripheral/src/ch32v30x_dma.o
 .text.DMA_ClearITPendingBit
                0x***********00000       0x18 ./Peripheral/src/ch32v30x_dma.o
 .debug_info    0x***********00000      0xd16 ./Peripheral/src/ch32v30x_dma.o
 .debug_abbrev  0x***********00000      0x2fd ./Peripheral/src/ch32v30x_dma.o
 .debug_loc     0x***********00000      0x19a ./Peripheral/src/ch32v30x_dma.o
 .debug_aranges
                0x***********00000       0x60 ./Peripheral/src/ch32v30x_dma.o
 .debug_ranges  0x***********00000       0x50 ./Peripheral/src/ch32v30x_dma.o
 .debug_line    0x***********00000      0x96f ./Peripheral/src/ch32v30x_dma.o
 .debug_str     0x***********00000      0x7cf ./Peripheral/src/ch32v30x_dma.o
 .comment       0x***********00000       0x34 ./Peripheral/src/ch32v30x_dma.o
 .debug_frame   0x***********00000       0xd8 ./Peripheral/src/ch32v30x_dma.o
 .text          0x***********00000        0x0 ./Peripheral/src/ch32v30x_dvp.o
 .data          0x***********00000        0x0 ./Peripheral/src/ch32v30x_dvp.o
 .bss           0x***********00000        0x0 ./Peripheral/src/ch32v30x_dvp.o
 .text.DVP_INTCfg
                0x***********00000       0x16 ./Peripheral/src/ch32v30x_dvp.o
 .text.DVP_Mode
                0x***********00000       0x32 ./Peripheral/src/ch32v30x_dvp.o
 .text.DVP_Cfg  0x***********00000       0x62 ./Peripheral/src/ch32v30x_dvp.o
 .debug_info    0x***********00000      0xbc5 ./Peripheral/src/ch32v30x_dvp.o
 .debug_abbrev  0x***********00000      0x241 ./Peripheral/src/ch32v30x_dvp.o
 .debug_loc     0x***********00000       0x74 ./Peripheral/src/ch32v30x_dvp.o
 .debug_aranges
                0x***********00000       0x30 ./Peripheral/src/ch32v30x_dvp.o
 .debug_ranges  0x***********00000       0x20 ./Peripheral/src/ch32v30x_dvp.o
 .debug_line    0x***********00000      0x3ed ./Peripheral/src/ch32v30x_dvp.o
 .debug_str     0x***********00000      0x718 ./Peripheral/src/ch32v30x_dvp.o
 .comment       0x***********00000       0x34 ./Peripheral/src/ch32v30x_dvp.o
 .debug_frame   0x***********00000       0x40 ./Peripheral/src/ch32v30x_dvp.o
 .text          0x***********00000        0x0 ./Peripheral/src/ch32v30x_eth.o
 .data          0x***********00000        0x0 ./Peripheral/src/ch32v30x_eth.o
 .bss           0x***********00000        0x0 ./Peripheral/src/ch32v30x_eth.o
 .text.ETH_DeInit
                0x***********00000       0x28 ./Peripheral/src/ch32v30x_eth.o
 .text.ETH_StructInit
                0x***********00000       0xd8 ./Peripheral/src/ch32v30x_eth.o
 .text.ETH_HandleTxPkt
                0x***********00000       0x8c ./Peripheral/src/ch32v30x_eth.o
 .text.ETH_HandleRxPkt
                0x***********00000       0x9e ./Peripheral/src/ch32v30x_eth.o
 .text.ETH_GetRxPktSize
                0x***********00000       0x32 ./Peripheral/src/ch32v30x_eth.o
 .text.ETH_DropRxPkt
                0x***********00000       0x3c ./Peripheral/src/ch32v30x_eth.o
 .text.ETH_ReadPHYRegister
                0x***********00000       0x58 ./Peripheral/src/ch32v30x_eth.o
 .text.ETH_WritePHYRegister
                0x***********00000       0x52 ./Peripheral/src/ch32v30x_eth.o
 .text.ETH_PHYLoopBackCmd
                0x***********00000       0x40 ./Peripheral/src/ch32v30x_eth.o
 .text.ETH_MACTransmissionCmd
                0x***********00000       0x14 ./Peripheral/src/ch32v30x_eth.o
 .text.ETH_MACReceptionCmd
                0x***********00000       0x14 ./Peripheral/src/ch32v30x_eth.o
 .text.ETH_GetFlowControlBusyStatus
                0x***********00000        0xa ./Peripheral/src/ch32v30x_eth.o
 .text.ETH_InitiatePauseControlFrame
                0x***********00000        0xe ./Peripheral/src/ch32v30x_eth.o
 .text.ETH_BackPressureActivationCmd
                0x***********00000       0x14 ./Peripheral/src/ch32v30x_eth.o
 .text.ETH_GetMACFlagStatus
                0x***********00000        0xe ./Peripheral/src/ch32v30x_eth.o
 .text.ETH_GetMACITStatus
                0x***********00000       0x18 ./Peripheral/src/ch32v30x_eth.o
 .text.ETH_MACITConfig
                0x***********00000       0x1e ./Peripheral/src/ch32v30x_eth.o
 .text.ETH_MACAddressConfig
                0x***********00000       0x32 ./Peripheral/src/ch32v30x_eth.o
 .text.ETH_GetMACAddress
                0x***********00000       0x32 ./Peripheral/src/ch32v30x_eth.o
 .text.ETH_MACAddressPerfectFilterCmd
                0x***********00000       0x1e ./Peripheral/src/ch32v30x_eth.o
 .text.ETH_MACAddressFilterConfig
                0x***********00000       0x28 ./Peripheral/src/ch32v30x_eth.o
 .text.ETH_MACAddressMaskBytesFilterConfig
                0x***********00000       0x1e ./Peripheral/src/ch32v30x_eth.o
 .text.ETH_DMATxDescChainInit
                0x***********00000       0x46 ./Peripheral/src/ch32v30x_eth.o
 .text.ETH_DMATxDescRingInit
                0x***********00000       0x44 ./Peripheral/src/ch32v30x_eth.o
 .text.ETH_GetDMATxDescFlagStatus
                0x***********00000        0xa ./Peripheral/src/ch32v30x_eth.o
 .text.ETH_GetDMATxDescCollisionCount
                0x***********00000        0x8 ./Peripheral/src/ch32v30x_eth.o
 .text.ETH_SetDMATxDescOwnBit
                0x***********00000        0xc ./Peripheral/src/ch32v30x_eth.o
 .text.ETH_DMATxDescTransmitITConfig
                0x***********00000       0x18 ./Peripheral/src/ch32v30x_eth.o
 .text.ETH_DMATxDescFrameSegmentConfig
                0x***********00000        0x8 ./Peripheral/src/ch32v30x_eth.o
 .text.ETH_DMATxDescChecksumInsertionConfig
                0x***********00000        0x8 ./Peripheral/src/ch32v30x_eth.o
 .text.ETH_DMATxDescCRCCmd
                0x***********00000       0x18 ./Peripheral/src/ch32v30x_eth.o
 .text.ETH_DMATxDescEndOfRingCmd
                0x***********00000       0x18 ./Peripheral/src/ch32v30x_eth.o
 .text.ETH_DMATxDescSecondAddressChainedCmd
                0x***********00000       0x18 ./Peripheral/src/ch32v30x_eth.o
 .text.ETH_DMATxDescShortFramePaddingCmd
                0x***********00000       0x18 ./Peripheral/src/ch32v30x_eth.o
 .text.ETH_DMATxDescTimeStampCmd
                0x***********00000       0x18 ./Peripheral/src/ch32v30x_eth.o
 .text.ETH_DMATxDescBufferSizeConfig
                0x***********00000        0xc ./Peripheral/src/ch32v30x_eth.o
 .text.ETH_DMARxDescChainInit
                0x***********00000       0x50 ./Peripheral/src/ch32v30x_eth.o
 .text.ETH_DMARxDescRingInit
                0x***********00000       0x52 ./Peripheral/src/ch32v30x_eth.o
 .text.ETH_GetDMARxDescFlagStatus
                0x***********00000        0xa ./Peripheral/src/ch32v30x_eth.o
 .text.ETH_SetDMARxDescOwnBit
                0x***********00000        0xc ./Peripheral/src/ch32v30x_eth.o
 .text.ETH_GetDMARxDescFrameLength
                0x***********00000        0x8 ./Peripheral/src/ch32v30x_eth.o
 .text.ETH_DMARxDescReceiveITConfig
                0x***********00000       0x14 ./Peripheral/src/ch32v30x_eth.o
 .text.ETH_DMARxDescEndOfRingCmd
                0x***********00000       0x14 ./Peripheral/src/ch32v30x_eth.o
 .text.ETH_DMARxDescSecondAddressChainedCmd
                0x***********00000       0x14 ./Peripheral/src/ch32v30x_eth.o
 .text.ETH_GetDMARxDescBufferSize
                0x***********00000        0xe ./Peripheral/src/ch32v30x_eth.o
 .text.ETH_SoftwareReset
                0x***********00000        0xe ./Peripheral/src/ch32v30x_eth.o
 .text.ETH_GetSoftwareResetStatus
                0x***********00000        0xa ./Peripheral/src/ch32v30x_eth.o
 .text.ETH_GetlinkStaus
                0x***********00000        0xa ./Peripheral/src/ch32v30x_eth.o
 .text.ETH_GetDMAFlagStatus
                0x***********00000        0xe ./Peripheral/src/ch32v30x_eth.o
 .text.ETH_DMAClearFlag
                0x***********00000        0x8 ./Peripheral/src/ch32v30x_eth.o
 .text.ETH_GetDMAITStatus
                0x***********00000       0x18 ./Peripheral/src/ch32v30x_eth.o
 .text.ETH_DMAClearITPendingBit
                0x***********00000        0x8 ./Peripheral/src/ch32v30x_eth.o
 .text.ETH_GetTransmitProcessState
                0x***********00000        0xa ./Peripheral/src/ch32v30x_eth.o
 .text.ETH_GetReceiveProcessState
                0x***********00000        0xc ./Peripheral/src/ch32v30x_eth.o
 .text.ETH_FlushTransmitFIFO
                0x***********00000       0x10 ./Peripheral/src/ch32v30x_eth.o
 .text.ETH_Start
                0x***********00000       0x40 ./Peripheral/src/ch32v30x_eth.o
 .text.ETH_GetFlushTransmitFIFOStatus
                0x***********00000        0xc ./Peripheral/src/ch32v30x_eth.o
 .text.ETH_DMATransmissionCmd
                0x***********00000       0x20 ./Peripheral/src/ch32v30x_eth.o
 .text.ETH_DMAReceptionCmd
                0x***********00000       0x14 ./Peripheral/src/ch32v30x_eth.o
 .text.ETH_DMAITConfig
                0x***********00000       0x1e ./Peripheral/src/ch32v30x_eth.o
 .text.ETH_GetDMAOverflowStatus
                0x***********00000        0xe ./Peripheral/src/ch32v30x_eth.o
 .text.ETH_GetRxOverflowMissedFrameCounter
                0x***********00000        0xe ./Peripheral/src/ch32v30x_eth.o
 .text.ETH_GetBufferUnavailableMissedFrameCounter
                0x***********00000        0xc ./Peripheral/src/ch32v30x_eth.o
 .text.ETH_GetCurrentTxDescStartAddress
                0x***********00000        0x8 ./Peripheral/src/ch32v30x_eth.o
 .text.ETH_GetCurrentRxDescStartAddress
                0x***********00000        0x8 ./Peripheral/src/ch32v30x_eth.o
 .text.ETH_GetCurrentTxBufferAddress
                0x***********00000        0xc ./Peripheral/src/ch32v30x_eth.o
 .text.ETH_GetCurrentRxBufferAddress
                0x***********00000        0x8 ./Peripheral/src/ch32v30x_eth.o
 .text.ETH_ResumeDMATransmission
                0x***********00000        0xa ./Peripheral/src/ch32v30x_eth.o
 .text.ETH_ResumeDMAReception
                0x***********00000        0xa ./Peripheral/src/ch32v30x_eth.o
 .text.ETH_ResetWakeUpFrameFilterRegisterPointer
                0x***********00000       0x10 ./Peripheral/src/ch32v30x_eth.o
 .text.ETH_SetWakeUpFrameFilterRegister
                0x***********00000       0x14 ./Peripheral/src/ch32v30x_eth.o
 .text.ETH_GlobalUnicastWakeUpCmd
                0x***********00000       0x16 ./Peripheral/src/ch32v30x_eth.o
 .text.ETH_GetPMTFlagStatus
                0x***********00000        0xe ./Peripheral/src/ch32v30x_eth.o
 .text.ETH_WakeUpFrameDetectionCmd
                0x***********00000       0x14 ./Peripheral/src/ch32v30x_eth.o
 .text.ETH_MagicPacketDetectionCmd
                0x***********00000       0x14 ./Peripheral/src/ch32v30x_eth.o
 .text.ETH_PowerDownCmd
                0x***********00000       0x14 ./Peripheral/src/ch32v30x_eth.o
 .text.ETH_MMCCounterFreezeCmd
                0x***********00000       0x18 ./Peripheral/src/ch32v30x_eth.o
 .text.ETH_MMCResetOnReadCmd
                0x***********00000       0x18 ./Peripheral/src/ch32v30x_eth.o
 .text.ETH_MMCCounterRolloverCmd
                0x***********00000       0x18 ./Peripheral/src/ch32v30x_eth.o
 .text.ETH_MMCCountersReset
                0x***********00000       0x12 ./Peripheral/src/ch32v30x_eth.o
 .text.ETH_MMCITConfig
                0x***********00000       0x5c ./Peripheral/src/ch32v30x_eth.o
 .text.ETH_GetMMCITStatus
                0x***********00000       0x3a ./Peripheral/src/ch32v30x_eth.o
 .text.ETH_GetMMCRegister
                0x***********00000        0xa ./Peripheral/src/ch32v30x_eth.o
 .text.ETH_EnablePTPTimeStampAddend
                0x***********00000       0x12 ./Peripheral/src/ch32v30x_eth.o
 .text.ETH_EnablePTPTimeStampInterruptTrigger
                0x***********00000       0x12 ./Peripheral/src/ch32v30x_eth.o
 .text.ETH_EnablePTPTimeStampUpdate
                0x***********00000       0x12 ./Peripheral/src/ch32v30x_eth.o
 .text.ETH_InitializePTPTimeStamp
                0x***********00000       0x12 ./Peripheral/src/ch32v30x_eth.o
 .text.ETH_PTPUpdateMethodConfig
                0x***********00000       0x18 ./Peripheral/src/ch32v30x_eth.o
 .text.ETH_PTPTimeStampCmd
                0x***********00000       0x18 ./Peripheral/src/ch32v30x_eth.o
 .text.ETH_GetPTPFlagStatus
                0x***********00000       0x10 ./Peripheral/src/ch32v30x_eth.o
 .text.ETH_SetPTPSubSecondIncrement
                0x***********00000        0xa ./Peripheral/src/ch32v30x_eth.o
 .text.ETH_SetPTPTimeStampUpdate
                0x***********00000       0x10 ./Peripheral/src/ch32v30x_eth.o
 .text.ETH_SetPTPTimeStampAddend
                0x***********00000        0xa ./Peripheral/src/ch32v30x_eth.o
 .text.ETH_SetPTPTargetTime
                0x***********00000        0xe ./Peripheral/src/ch32v30x_eth.o
 .text.ETH_GetPTPRegister
                0x***********00000        0xa ./Peripheral/src/ch32v30x_eth.o
 .text.ETH_DMAPTPTxDescChainInit
                0x***********00000       0x66 ./Peripheral/src/ch32v30x_eth.o
 .text.ETH_DMAPTPRxDescChainInit
                0x***********00000       0x70 ./Peripheral/src/ch32v30x_eth.o
 .text.ETH_HandlePTPTxPkt
                0x***********00000       0xe6 ./Peripheral/src/ch32v30x_eth.o
 .text.ETH_HandlePTPRxPkt
                0x***********00000       0xd0 ./Peripheral/src/ch32v30x_eth.o
 .text.RGMII_TXC_Delay
                0x***********00000       0x26 ./Peripheral/src/ch32v30x_eth.o
 .sbss.DMAPTPRxDescToGet
                0x***********00000        0x4 ./Peripheral/src/ch32v30x_eth.o
 .sbss.DMAPTPTxDescToSet
                0x***********00000        0x4 ./Peripheral/src/ch32v30x_eth.o
 .sbss.DMARxDescToGet
                0x***********00000        0x4 ./Peripheral/src/ch32v30x_eth.o
 .sbss.DMATxDescToSet
                0x***********00000        0x4 ./Peripheral/src/ch32v30x_eth.o
 .debug_info    0x***********00000     0x270c ./Peripheral/src/ch32v30x_eth.o
 .debug_abbrev  0x***********00000      0x47b ./Peripheral/src/ch32v30x_eth.o
 .debug_loc     0x***********00000      0xef1 ./Peripheral/src/ch32v30x_eth.o
 .debug_aranges
                0x***********00000      0x300 ./Peripheral/src/ch32v30x_eth.o
 .debug_ranges  0x***********00000      0x368 ./Peripheral/src/ch32v30x_eth.o
 .debug_line    0x***********00000     0x31bc ./Peripheral/src/ch32v30x_eth.o
 .debug_str     0x***********00000     0x18ec ./Peripheral/src/ch32v30x_eth.o
 .comment       0x***********00000       0x34 ./Peripheral/src/ch32v30x_eth.o
 .debug_frame   0x***********00000      0x6a4 ./Peripheral/src/ch32v30x_eth.o
 .text          0x***********00000        0x0 ./Peripheral/src/ch32v30x_exti.o
 .data          0x***********00000        0x0 ./Peripheral/src/ch32v30x_exti.o
 .bss           0x***********00000        0x0 ./Peripheral/src/ch32v30x_exti.o
 .text.EXTI_DeInit
                0x***********00000       0x22 ./Peripheral/src/ch32v30x_exti.o
 .text.EXTI_Init
                0x***********00000       0x6a ./Peripheral/src/ch32v30x_exti.o
 .text.EXTI_StructInit
                0x***********00000       0x12 ./Peripheral/src/ch32v30x_exti.o
 .text.EXTI_GenerateSWInterrupt
                0x***********00000       0x10 ./Peripheral/src/ch32v30x_exti.o
 .text.EXTI_GetFlagStatus
                0x***********00000       0x12 ./Peripheral/src/ch32v30x_exti.o
 .text.EXTI_ClearFlag
                0x***********00000        0xa ./Peripheral/src/ch32v30x_exti.o
 .text.EXTI_GetITStatus
                0x***********00000       0x1e ./Peripheral/src/ch32v30x_exti.o
 .text.EXTI_ClearITPendingBit
                0x***********00000        0xa ./Peripheral/src/ch32v30x_exti.o
 .debug_info    0x***********00000      0xc0d ./Peripheral/src/ch32v30x_exti.o
 .debug_abbrev  0x***********00000      0x2da ./Peripheral/src/ch32v30x_exti.o
 .debug_loc     0x***********00000      0x181 ./Peripheral/src/ch32v30x_exti.o
 .debug_aranges
                0x***********00000       0x50 ./Peripheral/src/ch32v30x_exti.o
 .debug_ranges  0x***********00000       0x40 ./Peripheral/src/ch32v30x_exti.o
 .debug_line    0x***********00000      0x574 ./Peripheral/src/ch32v30x_exti.o
 .debug_str     0x***********00000      0x769 ./Peripheral/src/ch32v30x_exti.o
 .comment       0x***********00000       0x34 ./Peripheral/src/ch32v30x_exti.o
 .debug_frame   0x***********00000       0x90 ./Peripheral/src/ch32v30x_exti.o
 .text          0x***********00000        0x0 ./Peripheral/src/ch32v30x_flash.o
 .data          0x***********00000        0x0 ./Peripheral/src/ch32v30x_flash.o
 .bss           0x***********00000        0x0 ./Peripheral/src/ch32v30x_flash.o
 .text.ROM_ERASE
                0x***********00000       0xa0 ./Peripheral/src/ch32v30x_flash.o
 .text.FLASH_Unlock
                0x***********00000       0x1a ./Peripheral/src/ch32v30x_flash.o
 .text.FLASH_UnlockBank1
                0x***********00000       0x18 ./Peripheral/src/ch32v30x_flash.o
 .text.FLASH_Lock
                0x***********00000        0xe ./Peripheral/src/ch32v30x_flash.o
 .text.FLASH_LockBank1
                0x***********00000       0x18 ./Peripheral/src/ch32v30x_flash.o
 .text.FLASH_GetUserOptionByte
                0x***********00000        0xa ./Peripheral/src/ch32v30x_flash.o
 .text.FLASH_GetWriteProtectionOptionByte
                0x***********00000        0x8 ./Peripheral/src/ch32v30x_flash.o
 .text.FLASH_GetReadOutProtectionStatus
                0x***********00000        0xc ./Peripheral/src/ch32v30x_flash.o
 .text.FLASH_ITConfig
                0x***********00000       0x1e ./Peripheral/src/ch32v30x_flash.o
 .text.FLASH_GetFlagStatus
                0x***********00000       0x1e ./Peripheral/src/ch32v30x_flash.o
 .text.FLASH_ClearFlag
                0x***********00000        0x8 ./Peripheral/src/ch32v30x_flash.o
 .text.FLASH_GetStatus
                0x***********00000       0x18 ./Peripheral/src/ch32v30x_flash.o
 .text.FLASH_GetBank1Status
                0x***********00000       0x18 ./Peripheral/src/ch32v30x_flash.o
 .text.FLASH_WaitForLastOperation
                0x***********00000       0x34 ./Peripheral/src/ch32v30x_flash.o
 .text.FLASH_ErasePage
                0x***********00000       0x4c ./Peripheral/src/ch32v30x_flash.o
 .text.FLASH_EraseAllPages
                0x***********00000       0x48 ./Peripheral/src/ch32v30x_flash.o
 .text.FLASH_EraseAllBank1Pages
                0x***********00000       0x18 ./Peripheral/src/ch32v30x_flash.o
 .text.FLASH_EraseOptionBytes
                0x***********00000       0xe6 ./Peripheral/src/ch32v30x_flash.o
 .text.FLASH_ProgramWord
                0x***********00000       0x68 ./Peripheral/src/ch32v30x_flash.o
 .text.FLASH_ProgramHalfWord
                0x***********00000       0x48 ./Peripheral/src/ch32v30x_flash.o
 .text.FLASH_ProgramOptionByteData
                0x***********00000      0x11a ./Peripheral/src/ch32v30x_flash.o
 .text.FLASH_EnableWriteProtection
                0x***********00000      0x106 ./Peripheral/src/ch32v30x_flash.o
 .text.FLASH_ReadOutProtection
                0x***********00000       0xf4 ./Peripheral/src/ch32v30x_flash.o
 .text.FLASH_UserOptionByteConfig
                0x***********00000      0x110 ./Peripheral/src/ch32v30x_flash.o
 .text.FLASH_WaitForLastBank1Operation
                0x***********00000       0x18 ./Peripheral/src/ch32v30x_flash.o
 .text.FLASH_Unlock_Fast
                0x***********00000       0x1e ./Peripheral/src/ch32v30x_flash.o
 .text.FLASH_Lock_Fast
                0x***********00000        0xe ./Peripheral/src/ch32v30x_flash.o
 .text.FLASH_ErasePage_Fast
                0x***********00000       0x2e ./Peripheral/src/ch32v30x_flash.o
 .text.FLASH_EraseBlock_32K_Fast
                0x***********00000       0x30 ./Peripheral/src/ch32v30x_flash.o
 .text.FLASH_ProgramPage_Fast
                0x***********00000       0x64 ./Peripheral/src/ch32v30x_flash.o
 .text.FLASH_Access_Clock_Cfg
                0x***********00000       0x18 ./Peripheral/src/ch32v30x_flash.o
 .text.FLASH_Enhance_Mode
                0x***********00000       0x2e ./Peripheral/src/ch32v30x_flash.o
 .text.FLASH_ROM_ERASE
                0x***********00000      0x1a4 ./Peripheral/src/ch32v30x_flash.o
 .text.FLASH_ROM_WRITE
                0x***********00000       0xce ./Peripheral/src/ch32v30x_flash.o
 .debug_info    0x***********00000     0x159f ./Peripheral/src/ch32v30x_flash.o
 .debug_abbrev  0x***********00000      0x468 ./Peripheral/src/ch32v30x_flash.o
 .debug_loc     0x***********00000      0xc7d ./Peripheral/src/ch32v30x_flash.o
 .debug_aranges
                0x***********00000      0x108 ./Peripheral/src/ch32v30x_flash.o
 .debug_ranges  0x***********00000       0xf8 ./Peripheral/src/ch32v30x_flash.o
 .debug_line    0x***********00000     0x2405 ./Peripheral/src/ch32v30x_flash.o
 .debug_str     0x***********00000      0xada ./Peripheral/src/ch32v30x_flash.o
 .comment       0x***********00000       0x34 ./Peripheral/src/ch32v30x_flash.o
 .debug_frame   0x***********00000      0x370 ./Peripheral/src/ch32v30x_flash.o
 .text          0x***********00000        0x0 ./Peripheral/src/ch32v30x_fsmc.o
 .data          0x***********00000        0x0 ./Peripheral/src/ch32v30x_fsmc.o
 .bss           0x***********00000        0x0 ./Peripheral/src/ch32v30x_fsmc.o
 .text.FSMC_NORSRAMDeInit
                0x***********00000       0x3c ./Peripheral/src/ch32v30x_fsmc.o
 .text.FSMC_NANDDeInit
                0x***********00000       0x26 ./Peripheral/src/ch32v30x_fsmc.o
 .text.FSMC_NORSRAMInit
                0x***********00000       0xae ./Peripheral/src/ch32v30x_fsmc.o
 .text.FSMC_NANDInit
                0x***********00000       0x84 ./Peripheral/src/ch32v30x_fsmc.o
 .text.FSMC_NORSRAMStructInit
                0x***********00000       0x5c ./Peripheral/src/ch32v30x_fsmc.o
 .text.FSMC_NANDStructInit
                0x***********00000       0x36 ./Peripheral/src/ch32v30x_fsmc.o
 .text.FSMC_NORSRAMCmd
                0x***********00000       0x1e ./Peripheral/src/ch32v30x_fsmc.o
 .text.FSMC_NANDCmd
                0x***********00000       0x2c ./Peripheral/src/ch32v30x_fsmc.o
 .text.FSMC_NANDECCCmd
                0x***********00000       0x2e ./Peripheral/src/ch32v30x_fsmc.o
 .text.FSMC_GetECC
                0x***********00000       0x12 ./Peripheral/src/ch32v30x_fsmc.o
 .text.FSMC_GetFlagStatus
                0x***********00000       0x18 ./Peripheral/src/ch32v30x_fsmc.o
 .debug_info    0x***********00000      0xf08 ./Peripheral/src/ch32v30x_fsmc.o
 .debug_abbrev  0x***********00000      0x2d9 ./Peripheral/src/ch32v30x_fsmc.o
 .debug_loc     0x***********00000      0x2ae ./Peripheral/src/ch32v30x_fsmc.o
 .debug_aranges
                0x***********00000       0x70 ./Peripheral/src/ch32v30x_fsmc.o
 .debug_ranges  0x***********00000       0x60 ./Peripheral/src/ch32v30x_fsmc.o
 .debug_line    0x***********00000      0xbb4 ./Peripheral/src/ch32v30x_fsmc.o
 .debug_str     0x***********00000      0xa3c ./Peripheral/src/ch32v30x_fsmc.o
 .comment       0x***********00000       0x34 ./Peripheral/src/ch32v30x_fsmc.o
 .debug_frame   0x***********00000       0xc0 ./Peripheral/src/ch32v30x_fsmc.o
 .text          0x***********00000        0x0 ./Peripheral/src/ch32v30x_gpio.o
 .data          0x***********00000        0x0 ./Peripheral/src/ch32v30x_gpio.o
 .bss           0x***********00000        0x0 ./Peripheral/src/ch32v30x_gpio.o
 .text.GPIO_DeInit
                0x***********00000       0xa4 ./Peripheral/src/ch32v30x_gpio.o
 .text.GPIO_AFIODeInit
                0x***********00000       0x28 ./Peripheral/src/ch32v30x_gpio.o
 .text.GPIO_StructInit
                0x***********00000        0xe ./Peripheral/src/ch32v30x_gpio.o
 .text.GPIO_ReadInputData
                0x***********00000        0x8 ./Peripheral/src/ch32v30x_gpio.o
 .text.GPIO_ReadOutputDataBit
                0x***********00000        0xa ./Peripheral/src/ch32v30x_gpio.o
 .text.GPIO_ReadOutputData
                0x***********00000        0x8 ./Peripheral/src/ch32v30x_gpio.o
 .text.GPIO_WriteBit
                0x***********00000        0xa ./Peripheral/src/ch32v30x_gpio.o
 .text.GPIO_Write
                0x***********00000        0x4 ./Peripheral/src/ch32v30x_gpio.o
 .text.GPIO_PinLockConfig
                0x***********00000       0x10 ./Peripheral/src/ch32v30x_gpio.o
 .text.GPIO_EventOutputConfig
                0x***********00000       0x18 ./Peripheral/src/ch32v30x_gpio.o
 .text.GPIO_EventOutputCmd
                0x***********00000       0x16 ./Peripheral/src/ch32v30x_gpio.o
 .text.GPIO_PinRemapConfig
                0x***********00000       0xd6 ./Peripheral/src/ch32v30x_gpio.o
 .text.GPIO_EXTILineConfig
                0x***********00000       0x2c ./Peripheral/src/ch32v30x_gpio.o
 .text.GPIO_ETH_MediaInterfaceConfig
                0x***********00000       0x24 ./Peripheral/src/ch32v30x_gpio.o
 .text.GPIO_IPD_Unused
                0x***********00000      0x23c ./Peripheral/src/ch32v30x_gpio.o
 .text          0x***********00000        0x0 ./Peripheral/src/ch32v30x_i2c.o
 .data          0x***********00000        0x0 ./Peripheral/src/ch32v30x_i2c.o
 .bss           0x***********00000        0x0 ./Peripheral/src/ch32v30x_i2c.o
 .text.I2C_DeInit
                0x***********00000       0x4c ./Peripheral/src/ch32v30x_i2c.o
 .text.I2C_Init
                0x***********00000      0x10c ./Peripheral/src/ch32v30x_i2c.o
 .text.I2C_StructInit
                0x***********00000       0x18 ./Peripheral/src/ch32v30x_i2c.o
 .text.I2C_Cmd  0x***********00000       0x18 ./Peripheral/src/ch32v30x_i2c.o
 .text.I2C_DMACmd
                0x***********00000       0x1e ./Peripheral/src/ch32v30x_i2c.o
 .text.I2C_DMALastTransferCmd
                0x***********00000       0x16 ./Peripheral/src/ch32v30x_i2c.o
 .text.I2C_GenerateSTART
                0x***********00000       0x1a ./Peripheral/src/ch32v30x_i2c.o
 .text.I2C_GenerateSTOP
                0x***********00000       0x1a ./Peripheral/src/ch32v30x_i2c.o
 .text.I2C_AcknowledgeConfig
                0x***********00000       0x1a ./Peripheral/src/ch32v30x_i2c.o
 .text.I2C_OwnAddress2Config
                0x***********00000       0x14 ./Peripheral/src/ch32v30x_i2c.o
 .text.I2C_DualAddressCmd
                0x***********00000       0x18 ./Peripheral/src/ch32v30x_i2c.o
 .text.I2C_GeneralCallCmd
                0x***********00000       0x1a ./Peripheral/src/ch32v30x_i2c.o
 .text.I2C_ITConfig
                0x***********00000       0x12 ./Peripheral/src/ch32v30x_i2c.o
 .text.I2C_SendData
                0x***********00000        0x4 ./Peripheral/src/ch32v30x_i2c.o
 .text.I2C_ReceiveData
                0x***********00000        0x8 ./Peripheral/src/ch32v30x_i2c.o
 .text.I2C_Send7bitAddress
                0x***********00000       0x12 ./Peripheral/src/ch32v30x_i2c.o
 .text.I2C_ReadRegister
                0x***********00000       0x14 ./Peripheral/src/ch32v30x_i2c.o
 .text.I2C_SoftwareResetCmd
                0x***********00000       0x12 ./Peripheral/src/ch32v30x_i2c.o
 .text.I2C_NACKPositionConfig
                0x***********00000       0x22 ./Peripheral/src/ch32v30x_i2c.o
 .text.I2C_SMBusAlertConfig
                0x***********00000       0x1a ./Peripheral/src/ch32v30x_i2c.o
 .text.I2C_TransmitPEC
                0x***********00000       0x16 ./Peripheral/src/ch32v30x_i2c.o
 .text.I2C_PECPositionConfig
                0x***********00000       0x18 ./Peripheral/src/ch32v30x_i2c.o
 .text.I2C_CalculatePEC
                0x***********00000       0x1a ./Peripheral/src/ch32v30x_i2c.o
 .text.I2C_GetPEC
                0x***********00000        0x6 ./Peripheral/src/ch32v30x_i2c.o
 .text.I2C_ARPCmd
                0x***********00000       0x18 ./Peripheral/src/ch32v30x_i2c.o
 .text.I2C_StretchClockCmd
                0x***********00000       0x1a ./Peripheral/src/ch32v30x_i2c.o
 .text.I2C_FastModeDutyCycleConfig
                0x***********00000       0x1a ./Peripheral/src/ch32v30x_i2c.o
 .text.I2C_CheckEvent
                0x***********00000       0x16 ./Peripheral/src/ch32v30x_i2c.o
 .text.I2C_GetLastEvent
                0x***********00000        0xe ./Peripheral/src/ch32v30x_i2c.o
 .text.I2C_GetFlagStatus
                0x***********00000       0x32 ./Peripheral/src/ch32v30x_i2c.o
 .text.I2C_ClearFlag
                0x***********00000        0xc ./Peripheral/src/ch32v30x_i2c.o
 .text.I2C_GetITStatus
                0x***********00000       0x20 ./Peripheral/src/ch32v30x_i2c.o
 .text.I2C_ClearITPendingBit
                0x***********00000        0xc ./Peripheral/src/ch32v30x_i2c.o
 .debug_info    0x***********00000     0x1481 ./Peripheral/src/ch32v30x_i2c.o
 .debug_abbrev  0x***********00000      0x3a7 ./Peripheral/src/ch32v30x_i2c.o
 .debug_loc     0x***********00000      0x6cc ./Peripheral/src/ch32v30x_i2c.o
 .debug_aranges
                0x***********00000      0x110 ./Peripheral/src/ch32v30x_i2c.o
 .debug_ranges  0x***********00000      0x100 ./Peripheral/src/ch32v30x_i2c.o
 .debug_line    0x***********00000     0x10d7 ./Peripheral/src/ch32v30x_i2c.o
 .debug_str     0x***********00000      0xabc ./Peripheral/src/ch32v30x_i2c.o
 .comment       0x***********00000       0x34 ./Peripheral/src/ch32v30x_i2c.o
 .debug_frame   0x***********00000      0x264 ./Peripheral/src/ch32v30x_i2c.o
 .text          0x***********00000        0x0 ./Peripheral/src/ch32v30x_iwdg.o
 .data          0x***********00000        0x0 ./Peripheral/src/ch32v30x_iwdg.o
 .bss           0x***********00000        0x0 ./Peripheral/src/ch32v30x_iwdg.o
 .text.IWDG_WriteAccessCmd
                0x***********00000        0x8 ./Peripheral/src/ch32v30x_iwdg.o
 .text.IWDG_SetPrescaler
                0x***********00000        0x8 ./Peripheral/src/ch32v30x_iwdg.o
 .text.IWDG_SetReload
                0x***********00000        0x8 ./Peripheral/src/ch32v30x_iwdg.o
 .text.IWDG_ReloadCounter
                0x***********00000        0xe ./Peripheral/src/ch32v30x_iwdg.o
 .text.IWDG_Enable
                0x***********00000       0x18 ./Peripheral/src/ch32v30x_iwdg.o
 .text.IWDG_GetFlagStatus
                0x***********00000        0xe ./Peripheral/src/ch32v30x_iwdg.o
 .debug_info    0x***********00000      0xb74 ./Peripheral/src/ch32v30x_iwdg.o
 .debug_abbrev  0x***********00000      0x282 ./Peripheral/src/ch32v30x_iwdg.o
 .debug_loc     0x***********00000       0x68 ./Peripheral/src/ch32v30x_iwdg.o
 .debug_aranges
                0x***********00000       0x48 ./Peripheral/src/ch32v30x_iwdg.o
 .debug_ranges  0x***********00000       0x38 ./Peripheral/src/ch32v30x_iwdg.o
 .debug_line    0x***********00000      0x393 ./Peripheral/src/ch32v30x_iwdg.o
 .debug_str     0x***********00000      0x6e0 ./Peripheral/src/ch32v30x_iwdg.o
 .comment       0x***********00000       0x34 ./Peripheral/src/ch32v30x_iwdg.o
 .debug_frame   0x***********00000       0x70 ./Peripheral/src/ch32v30x_iwdg.o
 .text          0x***********00000        0x0 ./Peripheral/src/ch32v30x_misc.o
 .data          0x***********00000        0x0 ./Peripheral/src/ch32v30x_misc.o
 .bss           0x***********00000        0x0 ./Peripheral/src/ch32v30x_misc.o
 .text          0x***********00000        0x0 ./Peripheral/src/ch32v30x_opa.o
 .data          0x***********00000        0x0 ./Peripheral/src/ch32v30x_opa.o
 .bss           0x***********00000        0x0 ./Peripheral/src/ch32v30x_opa.o
 .text.OPA_DeInit
                0x***********00000        0xa ./Peripheral/src/ch32v30x_opa.o
 .text.OPA_Init
                0x***********00000       0x36 ./Peripheral/src/ch32v30x_opa.o
 .text.OPA_StructInit
                0x***********00000       0x12 ./Peripheral/src/ch32v30x_opa.o
 .text.OPA_Cmd  0x***********00000       0x2c ./Peripheral/src/ch32v30x_opa.o
 .debug_info    0x***********00000      0xb28 ./Peripheral/src/ch32v30x_opa.o
 .debug_abbrev  0x***********00000      0x252 ./Peripheral/src/ch32v30x_opa.o
 .debug_loc     0x***********00000       0x56 ./Peripheral/src/ch32v30x_opa.o
 .debug_aranges
                0x***********00000       0x38 ./Peripheral/src/ch32v30x_opa.o
 .debug_ranges  0x***********00000       0x28 ./Peripheral/src/ch32v30x_opa.o
 .debug_line    0x***********00000      0x3fc ./Peripheral/src/ch32v30x_opa.o
 .debug_str     0x***********00000      0x697 ./Peripheral/src/ch32v30x_opa.o
 .comment       0x***********00000       0x34 ./Peripheral/src/ch32v30x_opa.o
 .debug_frame   0x***********00000       0x50 ./Peripheral/src/ch32v30x_opa.o
 .text          0x***********00000        0x0 ./Peripheral/src/ch32v30x_pwr.o
 .data          0x***********00000        0x0 ./Peripheral/src/ch32v30x_pwr.o
 .bss           0x***********00000        0x0 ./Peripheral/src/ch32v30x_pwr.o
 .text.PWR_DeInit
                0x***********00000       0x2c ./Peripheral/src/ch32v30x_pwr.o
 .text.PWR_BackupAccessCmd
                0x***********00000       0x16 ./Peripheral/src/ch32v30x_pwr.o
 .text.PWR_PVDCmd
                0x***********00000       0x14 ./Peripheral/src/ch32v30x_pwr.o
 .text.PWR_PVDLevelConfig
                0x***********00000       0x10 ./Peripheral/src/ch32v30x_pwr.o
 .text.PWR_WakeUpPinCmd
                0x***********00000       0x16 ./Peripheral/src/ch32v30x_pwr.o
 .text.PWR_EnterSTOPMode
                0x***********00000       0x7c ./Peripheral/src/ch32v30x_pwr.o
 .text.PWR_EnterSTANDBYMode
                0x***********00000       0x34 ./Peripheral/src/ch32v30x_pwr.o
 .text.PWR_GetFlagStatus
                0x***********00000        0xe ./Peripheral/src/ch32v30x_pwr.o
 .text.PWR_ClearFlag
                0x***********00000        0xe ./Peripheral/src/ch32v30x_pwr.o
 .text.PWR_EnterSTANDBYMode_RAM
                0x***********00000       0x30 ./Peripheral/src/ch32v30x_pwr.o
 .text.PWR_EnterSTANDBYMode_RAM_LV
                0x***********00000       0x30 ./Peripheral/src/ch32v30x_pwr.o
 .text.PWR_EnterSTANDBYMode_RAM_VBAT_EN
                0x***********00000       0x30 ./Peripheral/src/ch32v30x_pwr.o
 .text.PWR_EnterSTANDBYMode_RAM_LV_VBAT_EN
                0x***********00000       0x30 ./Peripheral/src/ch32v30x_pwr.o
 .text.PWR_EnterSTOPMode_RAM_LV
                0x***********00000       0x82 ./Peripheral/src/ch32v30x_pwr.o
 .debug_info    0x***********00000     0x101d ./Peripheral/src/ch32v30x_pwr.o
 .debug_abbrev  0x***********00000      0x3aa ./Peripheral/src/ch32v30x_pwr.o
 .debug_loc     0x***********00000      0x278 ./Peripheral/src/ch32v30x_pwr.o
 .debug_aranges
                0x***********00000       0x88 ./Peripheral/src/ch32v30x_pwr.o
 .debug_ranges  0x***********00000       0xf8 ./Peripheral/src/ch32v30x_pwr.o
 .debug_line    0x***********00000      0xa5f ./Peripheral/src/ch32v30x_pwr.o
 .debug_str     0x***********00000      0x833 ./Peripheral/src/ch32v30x_pwr.o
 .comment       0x***********00000       0x34 ./Peripheral/src/ch32v30x_pwr.o
 .debug_frame   0x***********00000       0xfc ./Peripheral/src/ch32v30x_pwr.o
 .text          0x***********00000        0x0 ./Peripheral/src/ch32v30x_rcc.o
 .data          0x***********00000        0x0 ./Peripheral/src/ch32v30x_rcc.o
 .bss           0x***********00000        0x0 ./Peripheral/src/ch32v30x_rcc.o
 .text.RCC_DeInit
                0x***********00000       0x52 ./Peripheral/src/ch32v30x_rcc.o
 .text.RCC_HSEConfig
                0x***********00000       0x3c ./Peripheral/src/ch32v30x_rcc.o
 .text.RCC_AdjustHSICalibrationValue
                0x***********00000       0x12 ./Peripheral/src/ch32v30x_rcc.o
 .text.RCC_HSICmd
                0x***********00000       0x14 ./Peripheral/src/ch32v30x_rcc.o
 .text.RCC_PLLConfig
                0x***********00000       0x2e ./Peripheral/src/ch32v30x_rcc.o
 .text.RCC_PLLCmd
                0x***********00000       0x24 ./Peripheral/src/ch32v30x_rcc.o
 .text.RCC_SYSCLKConfig
                0x***********00000        0xe ./Peripheral/src/ch32v30x_rcc.o
 .text.RCC_GetSYSCLKSource
                0x***********00000        0xa ./Peripheral/src/ch32v30x_rcc.o
 .text.RCC_HCLKConfig
                0x***********00000       0x10 ./Peripheral/src/ch32v30x_rcc.o
 .text.RCC_PCLK1Config
                0x***********00000       0x10 ./Peripheral/src/ch32v30x_rcc.o
 .text.RCC_PCLK2Config
                0x***********00000       0x16 ./Peripheral/src/ch32v30x_rcc.o
 .text.RCC_ITConfig
                0x***********00000       0x16 ./Peripheral/src/ch32v30x_rcc.o
 .text.RCC_ADCCLKConfig
                0x***********00000       0x12 ./Peripheral/src/ch32v30x_rcc.o
 .text.RCC_LSEConfig
                0x***********00000       0x28 ./Peripheral/src/ch32v30x_rcc.o
 .text.RCC_LSICmd
                0x***********00000       0x14 ./Peripheral/src/ch32v30x_rcc.o
 .text.RCC_RTCCLKConfig
                0x***********00000        0xc ./Peripheral/src/ch32v30x_rcc.o
 .text.RCC_RTCCLKCmd
                0x***********00000       0x20 ./Peripheral/src/ch32v30x_rcc.o
 .text.RCC_AHBPeriphClockCmd
                0x***********00000       0x1e ./Peripheral/src/ch32v30x_rcc.o
 .text.RCC_APB2PeriphResetCmd
                0x***********00000       0x1e ./Peripheral/src/ch32v30x_rcc.o
 .text.RCC_APB1PeriphResetCmd
                0x***********00000       0x1e ./Peripheral/src/ch32v30x_rcc.o
 .text.RCC_BackupResetCmd
                0x***********00000       0x20 ./Peripheral/src/ch32v30x_rcc.o
 .text.RCC_ClockSecuritySystemCmd
                0x***********00000       0x24 ./Peripheral/src/ch32v30x_rcc.o
 .text.RCC_MCOConfig
                0x***********00000        0x8 ./Peripheral/src/ch32v30x_rcc.o
 .text.RCC_GetFlagStatus
                0x***********00000       0x2e ./Peripheral/src/ch32v30x_rcc.o
 .text.RCC_WaitForHSEStartUp
                0x***********00000       0x42 ./Peripheral/src/ch32v30x_rcc.o
 .text.RCC_ClearFlag
                0x***********00000       0x10 ./Peripheral/src/ch32v30x_rcc.o
 .text.RCC_GetITStatus
                0x***********00000        0xe ./Peripheral/src/ch32v30x_rcc.o
 .text.RCC_ClearITPendingBit
                0x***********00000        0x8 ./Peripheral/src/ch32v30x_rcc.o
 .text.RCC_PREDIV1Config
                0x***********00000       0x14 ./Peripheral/src/ch32v30x_rcc.o
 .text.RCC_PREDIV2Config
                0x***********00000       0x10 ./Peripheral/src/ch32v30x_rcc.o
 .text.RCC_PLL2Config
                0x***********00000       0x14 ./Peripheral/src/ch32v30x_rcc.o
 .text.RCC_PLL2Cmd
                0x***********00000       0x24 ./Peripheral/src/ch32v30x_rcc.o
 .text.RCC_PLL3Config
                0x***********00000       0x12 ./Peripheral/src/ch32v30x_rcc.o
 .text.RCC_PLL3Cmd
                0x***********00000       0x24 ./Peripheral/src/ch32v30x_rcc.o
 .text.RCC_USBFSCLKConfig
                0x***********00000       0x1a ./Peripheral/src/ch32v30x_rcc.o
 .text.RCC_I2S2CLKConfig
                0x***********00000       0x18 ./Peripheral/src/ch32v30x_rcc.o
 .text.RCC_I2S3CLKConfig
                0x***********00000       0x1a ./Peripheral/src/ch32v30x_rcc.o
 .text.RCC_AHBPeriphResetCmd
                0x***********00000       0x1e ./Peripheral/src/ch32v30x_rcc.o
 .text.RCC_ADCCLKADJcmd
                0x***********00000       0x18 ./Peripheral/src/ch32v30x_rcc.o
 .text.RCC_RNGCLKConfig
                0x***********00000       0x1a ./Peripheral/src/ch32v30x_rcc.o
 .text.RCC_ETH1GCLKConfig
                0x***********00000       0x1a ./Peripheral/src/ch32v30x_rcc.o
 .text.RCC_ETH1G_125Mcmd
                0x***********00000       0x24 ./Peripheral/src/ch32v30x_rcc.o
 .text.RCC_USBHSConfig
                0x***********00000       0x1a ./Peripheral/src/ch32v30x_rcc.o
 .text.RCC_USBHSPLLCLKConfig
                0x***********00000       0x1a ./Peripheral/src/ch32v30x_rcc.o
 .text.RCC_USBHSPLLCKREFCLKConfig
                0x***********00000       0x1a ./Peripheral/src/ch32v30x_rcc.o
 .text.RCC_USBHSPHYPLLALIVEcmd
                0x***********00000       0x24 ./Peripheral/src/ch32v30x_rcc.o
 .text.RCC_USBCLK48MConfig
                0x***********00000       0x16 ./Peripheral/src/ch32v30x_rcc.o
 .text          0x***********00000        0x0 ./Peripheral/src/ch32v30x_rng.o
 .data          0x***********00000        0x0 ./Peripheral/src/ch32v30x_rng.o
 .bss           0x***********00000        0x0 ./Peripheral/src/ch32v30x_rng.o
 .text.RNG_Cmd  0x***********00000       0x18 ./Peripheral/src/ch32v30x_rng.o
 .text.RNG_GetRandomNumber
                0x***********00000        0xa ./Peripheral/src/ch32v30x_rng.o
 .text.RNG_ITConfig
                0x***********00000       0x18 ./Peripheral/src/ch32v30x_rng.o
 .text.RNG_GetFlagStatus
                0x***********00000       0x12 ./Peripheral/src/ch32v30x_rng.o
 .text.RNG_ClearFlag
                0x***********00000       0x10 ./Peripheral/src/ch32v30x_rng.o
 .text.RNG_GetITStatus
                0x***********00000       0x18 ./Peripheral/src/ch32v30x_rng.o
 .text.RNG_ClearITPendingBit
                0x***********00000       0x12 ./Peripheral/src/ch32v30x_rng.o
 .debug_info    0x***********00000      0xb02 ./Peripheral/src/ch32v30x_rng.o
 .debug_abbrev  0x***********00000      0x2c8 ./Peripheral/src/ch32v30x_rng.o
 .debug_loc     0x***********00000       0xa8 ./Peripheral/src/ch32v30x_rng.o
 .debug_aranges
                0x***********00000       0x48 ./Peripheral/src/ch32v30x_rng.o
 .debug_ranges  0x***********00000       0x38 ./Peripheral/src/ch32v30x_rng.o
 .debug_line    0x***********00000      0x3c9 ./Peripheral/src/ch32v30x_rng.o
 .debug_str     0x***********00000      0x668 ./Peripheral/src/ch32v30x_rng.o
 .comment       0x***********00000       0x34 ./Peripheral/src/ch32v30x_rng.o
 .debug_frame   0x***********00000       0x8c ./Peripheral/src/ch32v30x_rng.o
 .text          0x***********00000        0x0 ./Peripheral/src/ch32v30x_rtc.o
 .data          0x***********00000        0x0 ./Peripheral/src/ch32v30x_rtc.o
 .bss           0x***********00000        0x0 ./Peripheral/src/ch32v30x_rtc.o
 .text.RTC_ITConfig
                0x***********00000       0x1a ./Peripheral/src/ch32v30x_rtc.o
 .text.RTC_EnterConfigMode
                0x***********00000       0x12 ./Peripheral/src/ch32v30x_rtc.o
 .text.RTC_ExitConfigMode
                0x***********00000       0x18 ./Peripheral/src/ch32v30x_rtc.o
 .text.RTC_GetCounter
                0x***********00000       0x5a ./Peripheral/src/ch32v30x_rtc.o
 .text.RTC_SetCounter
                0x***********00000       0x3c ./Peripheral/src/ch32v30x_rtc.o
 .text.RTC_SetPrescaler
                0x***********00000       0x3e ./Peripheral/src/ch32v30x_rtc.o
 .text.RTC_SetAlarm
                0x***********00000       0x3c ./Peripheral/src/ch32v30x_rtc.o
 .text.RTC_GetDivider
                0x***********00000       0x60 ./Peripheral/src/ch32v30x_rtc.o
 .text.RTC_WaitForLastTask
                0x***********00000       0x10 ./Peripheral/src/ch32v30x_rtc.o
 .text.RTC_WaitForSynchro
                0x***********00000       0x24 ./Peripheral/src/ch32v30x_rtc.o
 .text.RTC_GetFlagStatus
                0x***********00000       0x12 ./Peripheral/src/ch32v30x_rtc.o
 .text.RTC_ClearFlag
                0x***********00000       0x14 ./Peripheral/src/ch32v30x_rtc.o
 .text.RTC_GetITStatus
                0x***********00000       0x20 ./Peripheral/src/ch32v30x_rtc.o
 .text.RTC_ClearITPendingBit
                0x***********00000       0x18 ./Peripheral/src/ch32v30x_rtc.o
 .debug_info    0x***********00000      0xdd4 ./Peripheral/src/ch32v30x_rtc.o
 .debug_abbrev  0x***********00000      0x30b ./Peripheral/src/ch32v30x_rtc.o
 .debug_loc     0x***********00000      0x377 ./Peripheral/src/ch32v30x_rtc.o
 .debug_aranges
                0x***********00000       0x80 ./Peripheral/src/ch32v30x_rtc.o
 .debug_ranges  0x***********00000       0x70 ./Peripheral/src/ch32v30x_rtc.o
 .debug_line    0x***********00000      0x943 ./Peripheral/src/ch32v30x_rtc.o
 .debug_str     0x***********00000      0x7e2 ./Peripheral/src/ch32v30x_rtc.o
 .comment       0x***********00000       0x34 ./Peripheral/src/ch32v30x_rtc.o
 .debug_frame   0x***********00000      0x12c ./Peripheral/src/ch32v30x_rtc.o
 .text          0x***********00000        0x0 ./Peripheral/src/ch32v30x_sdio.o
 .data          0x***********00000        0x0 ./Peripheral/src/ch32v30x_sdio.o
 .bss           0x***********00000        0x0 ./Peripheral/src/ch32v30x_sdio.o
 .text.SDIO_DeInit
                0x***********00000       0x30 ./Peripheral/src/ch32v30x_sdio.o
 .text.SDIO_Init
                0x***********00000       0x2a ./Peripheral/src/ch32v30x_sdio.o
 .text.SDIO_StructInit
                0x***********00000       0x1a ./Peripheral/src/ch32v30x_sdio.o
 .text.SDIO_ClockCmd
                0x***********00000       0x16 ./Peripheral/src/ch32v30x_sdio.o
 .text.SDIO_SetPowerState
                0x***********00000       0x12 ./Peripheral/src/ch32v30x_sdio.o
 .text.SDIO_GetPowerState
                0x***********00000        0xa ./Peripheral/src/ch32v30x_sdio.o
 .text.SDIO_ITConfig
                0x***********00000       0x1e ./Peripheral/src/ch32v30x_sdio.o
 .text.SDIO_DMACmd
                0x***********00000       0x14 ./Peripheral/src/ch32v30x_sdio.o
 .text.SDIO_SendCommand
                0x***********00000       0x22 ./Peripheral/src/ch32v30x_sdio.o
 .text.SDIO_CmdStructInit
                0x***********00000       0x16 ./Peripheral/src/ch32v30x_sdio.o
 .text.SDIO_GetCommandResponse
                0x***********00000        0xc ./Peripheral/src/ch32v30x_sdio.o
 .text.SDIO_GetResponse
                0x***********00000       0x16 ./Peripheral/src/ch32v30x_sdio.o
 .text.SDIO_DataConfig
                0x***********00000       0x26 ./Peripheral/src/ch32v30x_sdio.o
 .text.SDIO_DataStructInit
                0x***********00000       0x1a ./Peripheral/src/ch32v30x_sdio.o
 .text.SDIO_GetDataCounter
                0x***********00000        0x8 ./Peripheral/src/ch32v30x_sdio.o
 .text.SDIO_ReadData
                0x***********00000        0xa ./Peripheral/src/ch32v30x_sdio.o
 .text.SDIO_WriteData
                0x***********00000        0xa ./Peripheral/src/ch32v30x_sdio.o
 .text.SDIO_GetFIFOCount
                0x***********00000        0x8 ./Peripheral/src/ch32v30x_sdio.o
 .text.SDIO_StartSDIOReadWait
                0x***********00000       0x16 ./Peripheral/src/ch32v30x_sdio.o
 .text.SDIO_StopSDIOReadWait
                0x***********00000       0x16 ./Peripheral/src/ch32v30x_sdio.o
 .text.SDIO_SetSDIOReadWaitMode
                0x***********00000       0x16 ./Peripheral/src/ch32v30x_sdio.o
 .text.SDIO_SetSDIOOperation
                0x***********00000       0x1e ./Peripheral/src/ch32v30x_sdio.o
 .text.SDIO_SendSDIOSuspendCmd
                0x***********00000       0x1e ./Peripheral/src/ch32v30x_sdio.o
 .text.SDIO_CommandCompletionCmd
                0x***********00000       0x20 ./Peripheral/src/ch32v30x_sdio.o
 .text.SDIO_CEATAITCmd
                0x***********00000       0x20 ./Peripheral/src/ch32v30x_sdio.o
 .text.SDIO_SendCEATACmd
                0x***********00000       0x20 ./Peripheral/src/ch32v30x_sdio.o
 .text.SDIO_GetFlagStatus
                0x***********00000        0xe ./Peripheral/src/ch32v30x_sdio.o
 .text.SDIO_ClearFlag
                0x***********00000        0x8 ./Peripheral/src/ch32v30x_sdio.o
 .text.SDIO_GetITStatus
                0x***********00000       0x18 ./Peripheral/src/ch32v30x_sdio.o
 .text.SDIO_ClearITPendingBit
                0x***********00000        0x8 ./Peripheral/src/ch32v30x_sdio.o
 .debug_info    0x***********00000     0x10fd ./Peripheral/src/ch32v30x_sdio.o
 .debug_abbrev  0x***********00000      0x385 ./Peripheral/src/ch32v30x_sdio.o
 .debug_loc     0x***********00000      0x1bb ./Peripheral/src/ch32v30x_sdio.o
 .debug_aranges
                0x***********00000       0xf8 ./Peripheral/src/ch32v30x_sdio.o
 .debug_ranges  0x***********00000       0xe8 ./Peripheral/src/ch32v30x_sdio.o
 .debug_line    0x***********00000      0xc39 ./Peripheral/src/ch32v30x_sdio.o
 .debug_str     0x***********00000      0xa4b ./Peripheral/src/ch32v30x_sdio.o
 .comment       0x***********00000       0x34 ./Peripheral/src/ch32v30x_sdio.o
 .debug_frame   0x***********00000      0x204 ./Peripheral/src/ch32v30x_sdio.o
 .text          0x***********00000        0x0 ./Peripheral/src/ch32v30x_spi.o
 .data          0x***********00000        0x0 ./Peripheral/src/ch32v30x_spi.o
 .bss           0x***********00000        0x0 ./Peripheral/src/ch32v30x_spi.o
 .text.SPI_I2S_DeInit
                0x***********00000       0x70 ./Peripheral/src/ch32v30x_spi.o
 .text.I2S_Init
                0x***********00000       0xc6 ./Peripheral/src/ch32v30x_spi.o
 .text.SPI_StructInit
                0x***********00000       0x22 ./Peripheral/src/ch32v30x_spi.o
 .text.I2S_StructInit
                0x***********00000       0x12 ./Peripheral/src/ch32v30x_spi.o
 .text.I2S_Cmd  0x***********00000       0x1a ./Peripheral/src/ch32v30x_spi.o
 .text.SPI_I2S_ITConfig
                0x***********00000       0x1e ./Peripheral/src/ch32v30x_spi.o
 .text.SPI_I2S_DMACmd
                0x***********00000       0x12 ./Peripheral/src/ch32v30x_spi.o
 .text.SPI_NSSInternalSoftwareConfig
                0x***********00000       0x24 ./Peripheral/src/ch32v30x_spi.o
 .text.SPI_SSOutputCmd
                0x***********00000       0x18 ./Peripheral/src/ch32v30x_spi.o
 .text.SPI_DataSizeConfig
                0x***********00000       0x14 ./Peripheral/src/ch32v30x_spi.o
 .text.SPI_TransmitCRC
                0x***********00000        0xa ./Peripheral/src/ch32v30x_spi.o
 .text.SPI_CalculateCRC
                0x***********00000       0x16 ./Peripheral/src/ch32v30x_spi.o
 .text.SPI_GetCRC
                0x***********00000       0x12 ./Peripheral/src/ch32v30x_spi.o
 .text.SPI_GetCRCPolynomial
                0x***********00000        0x4 ./Peripheral/src/ch32v30x_spi.o
 .text.SPI_BiDirectionalLineConfig
                0x***********00000       0x1a ./Peripheral/src/ch32v30x_spi.o
 .text.SPI_I2S_ClearFlag
                0x***********00000        0xc ./Peripheral/src/ch32v30x_spi.o
 .text.SPI_I2S_GetITStatus
                0x***********00000       0x28 ./Peripheral/src/ch32v30x_spi.o
 .text.SPI_I2S_ClearITPendingBit
                0x***********00000       0x14 ./Peripheral/src/ch32v30x_spi.o
 .text          0x***********00000        0x0 ./Peripheral/src/ch32v30x_tim.o
 .data          0x***********00000        0x0 ./Peripheral/src/ch32v30x_tim.o
 .bss           0x***********00000        0x0 ./Peripheral/src/ch32v30x_tim.o
 .text.TI1_Config
                0x***********00000       0x82 ./Peripheral/src/ch32v30x_tim.o
 .text.TI2_Config
                0x***********00000       0x9a ./Peripheral/src/ch32v30x_tim.o
 .text.TIM_DeInit
                0x***********00000      0x138 ./Peripheral/src/ch32v30x_tim.o
 .text.TIM_OC2Init
                0x***********00000       0xae ./Peripheral/src/ch32v30x_tim.o
 .text.TIM_OC3Init
                0x***********00000       0xac ./Peripheral/src/ch32v30x_tim.o
 .text.TIM_OC4Init
                0x***********00000       0x88 ./Peripheral/src/ch32v30x_tim.o
 .text.TIM_BDTRConfig
                0x***********00000       0x20 ./Peripheral/src/ch32v30x_tim.o
 .text.TIM_TimeBaseStructInit
                0x***********00000       0x16 ./Peripheral/src/ch32v30x_tim.o
 .text.TIM_OCStructInit
                0x***********00000       0x22 ./Peripheral/src/ch32v30x_tim.o
 .text.TIM_ICStructInit
                0x***********00000       0x16 ./Peripheral/src/ch32v30x_tim.o
 .text.TIM_BDTRStructInit
                0x***********00000       0x1e ./Peripheral/src/ch32v30x_tim.o
 .text.TIM_CtrlPWMOutputs
                0x***********00000       0x16 ./Peripheral/src/ch32v30x_tim.o
 .text.TIM_GenerateEvent
                0x***********00000        0x4 ./Peripheral/src/ch32v30x_tim.o
 .text.TIM_DMAConfig
                0x***********00000        0x8 ./Peripheral/src/ch32v30x_tim.o
 .text.TIM_DMACmd
                0x***********00000       0x18 ./Peripheral/src/ch32v30x_tim.o
 .text.TIM_InternalClockConfig
                0x***********00000       0x10 ./Peripheral/src/ch32v30x_tim.o
 .text.TIM_ITRxExternalClockConfig
                0x***********00000       0x18 ./Peripheral/src/ch32v30x_tim.o
 .text.TIM_TIxExternalClockConfig
                0x***********00000       0x48 ./Peripheral/src/ch32v30x_tim.o
 .text.TIM_ETRConfig
                0x***********00000       0x16 ./Peripheral/src/ch32v30x_tim.o
 .text.TIM_ETRClockMode1Config
                0x***********00000       0x2a ./Peripheral/src/ch32v30x_tim.o
 .text.TIM_ETRClockMode2Config
                0x***********00000       0x22 ./Peripheral/src/ch32v30x_tim.o
 .text.TIM_PrescalerConfig
                0x***********00000        0x6 ./Peripheral/src/ch32v30x_tim.o
 .text.TIM_CounterModeConfig
                0x***********00000       0x10 ./Peripheral/src/ch32v30x_tim.o
 .text.TIM_SelectInputTrigger
                0x***********00000       0x10 ./Peripheral/src/ch32v30x_tim.o
 .text.TIM_EncoderInterfaceConfig
                0x***********00000       0x3c ./Peripheral/src/ch32v30x_tim.o
 .text.TIM_ForcedOC1Config
                0x***********00000       0x10 ./Peripheral/src/ch32v30x_tim.o
 .text.TIM_ForcedOC2Config
                0x***********00000       0x14 ./Peripheral/src/ch32v30x_tim.o
 .text.TIM_ForcedOC3Config
                0x***********00000       0x10 ./Peripheral/src/ch32v30x_tim.o
 .text.TIM_ForcedOC4Config
                0x***********00000       0x14 ./Peripheral/src/ch32v30x_tim.o
 .text.TIM_SelectCOM
                0x***********00000       0x18 ./Peripheral/src/ch32v30x_tim.o
 .text.TIM_SelectCCDMA
                0x***********00000       0x18 ./Peripheral/src/ch32v30x_tim.o
 .text.TIM_CCPreloadControl
                0x***********00000       0x18 ./Peripheral/src/ch32v30x_tim.o
 .text.TIM_OC2PreloadConfig
                0x***********00000       0x16 ./Peripheral/src/ch32v30x_tim.o
 .text.TIM_OC3PreloadConfig
                0x***********00000        0xe ./Peripheral/src/ch32v30x_tim.o
 .text.TIM_OC4PreloadConfig
                0x***********00000       0x16 ./Peripheral/src/ch32v30x_tim.o
 .text.TIM_OC1FastConfig
                0x***********00000        0xe ./Peripheral/src/ch32v30x_tim.o
 .text.TIM_OC2FastConfig
                0x***********00000       0x16 ./Peripheral/src/ch32v30x_tim.o
 .text.TIM_OC3FastConfig
                0x***********00000        0xe ./Peripheral/src/ch32v30x_tim.o
 .text.TIM_OC4FastConfig
                0x***********00000       0x16 ./Peripheral/src/ch32v30x_tim.o
 .text.TIM_ClearOC1Ref
                0x***********00000       0x10 ./Peripheral/src/ch32v30x_tim.o
 .text.TIM_ClearOC2Ref
                0x***********00000       0x12 ./Peripheral/src/ch32v30x_tim.o
 .text.TIM_ClearOC3Ref
                0x***********00000       0x10 ./Peripheral/src/ch32v30x_tim.o
 .text.TIM_ClearOC4Ref
                0x***********00000       0x12 ./Peripheral/src/ch32v30x_tim.o
 .text.TIM_OC1PolarityConfig
                0x***********00000        0xe ./Peripheral/src/ch32v30x_tim.o
 .text.TIM_OC1NPolarityConfig
                0x***********00000        0xe ./Peripheral/src/ch32v30x_tim.o
 .text.TIM_OC2PolarityConfig
                0x***********00000       0x16 ./Peripheral/src/ch32v30x_tim.o
 .text.TIM_OC2NPolarityConfig
                0x***********00000       0x16 ./Peripheral/src/ch32v30x_tim.o
 .text.TIM_OC3PolarityConfig
                0x***********00000       0x16 ./Peripheral/src/ch32v30x_tim.o
 .text.TIM_OC3NPolarityConfig
                0x***********00000       0x16 ./Peripheral/src/ch32v30x_tim.o
 .text.TIM_OC4PolarityConfig
                0x***********00000       0x14 ./Peripheral/src/ch32v30x_tim.o
 .text.TIM_CCxCmd
                0x***********00000       0x20 ./Peripheral/src/ch32v30x_tim.o
 .text.TIM_CCxNCmd
                0x***********00000       0x20 ./Peripheral/src/ch32v30x_tim.o
 .text.TIM_SelectOCxM
                0x***********00000       0x4c ./Peripheral/src/ch32v30x_tim.o
 .text.TIM_UpdateDisableConfig
                0x***********00000       0x18 ./Peripheral/src/ch32v30x_tim.o
 .text.TIM_UpdateRequestConfig
                0x***********00000       0x18 ./Peripheral/src/ch32v30x_tim.o
 .text.TIM_SelectHallSensor
                0x***********00000       0x1a ./Peripheral/src/ch32v30x_tim.o
 .text.TIM_SelectOnePulseMode
                0x***********00000       0x16 ./Peripheral/src/ch32v30x_tim.o
 .text.TIM_SelectOutputTrigger
                0x***********00000       0x18 ./Peripheral/src/ch32v30x_tim.o
 .text.TIM_SelectSlaveMode
                0x***********00000       0x16 ./Peripheral/src/ch32v30x_tim.o
 .text.TIM_SelectMasterSlaveMode
                0x***********00000       0x18 ./Peripheral/src/ch32v30x_tim.o
 .text.TIM_SetCounter
                0x***********00000        0x4 ./Peripheral/src/ch32v30x_tim.o
 .text.TIM_SetAutoreload
                0x***********00000        0x4 ./Peripheral/src/ch32v30x_tim.o
 .text.TIM_SetCompare2
                0x***********00000        0x4 ./Peripheral/src/ch32v30x_tim.o
 .text.TIM_SetCompare3
                0x***********00000        0x4 ./Peripheral/src/ch32v30x_tim.o
 .text.TIM_SetCompare4
                0x***********00000        0x6 ./Peripheral/src/ch32v30x_tim.o
 .text.TIM_SetIC1Prescaler
                0x***********00000       0x16 ./Peripheral/src/ch32v30x_tim.o
 .text.TIM_SetIC2Prescaler
                0x***********00000       0x1a ./Peripheral/src/ch32v30x_tim.o
 .text.TIM_PWMIConfig
                0x***********00000       0x92 ./Peripheral/src/ch32v30x_tim.o
 .text.TIM_SetIC3Prescaler
                0x***********00000       0x16 ./Peripheral/src/ch32v30x_tim.o
 .text.TIM_SetIC4Prescaler
                0x***********00000       0x1a ./Peripheral/src/ch32v30x_tim.o
 .text.TIM_ICInit
                0x***********00000      0x1ba ./Peripheral/src/ch32v30x_tim.o
 .text.TIM_SetClockDivision
                0x***********00000       0x18 ./Peripheral/src/ch32v30x_tim.o
 .text.TIM_GetCapture1
                0x***********00000        0x4 ./Peripheral/src/ch32v30x_tim.o
 .text.TIM_GetCapture2
                0x***********00000        0x4 ./Peripheral/src/ch32v30x_tim.o
 .text.TIM_GetCapture3
                0x***********00000        0x4 ./Peripheral/src/ch32v30x_tim.o
 .text.TIM_GetCapture4
                0x***********00000        0x6 ./Peripheral/src/ch32v30x_tim.o
 .text.TIM_GetCounter
                0x***********00000        0x4 ./Peripheral/src/ch32v30x_tim.o
 .text.TIM_GetPrescaler
                0x***********00000        0x4 ./Peripheral/src/ch32v30x_tim.o
 .text.TIM_GetFlagStatus
                0x***********00000        0xa ./Peripheral/src/ch32v30x_tim.o
 .text.TIM_ClearFlag
                0x***********00000        0xc ./Peripheral/src/ch32v30x_tim.o
 .text          0x***********00000        0x0 ./Peripheral/src/ch32v30x_usart.o
 .data          0x***********00000        0x0 ./Peripheral/src/ch32v30x_usart.o
 .bss           0x***********00000        0x0 ./Peripheral/src/ch32v30x_usart.o
 .text.USART_DeInit
                0x***********00000      0x112 ./Peripheral/src/ch32v30x_usart.o
 .text.USART_StructInit
                0x***********00000       0x18 ./Peripheral/src/ch32v30x_usart.o
 .text.USART_ClockInit
                0x***********00000       0x1e ./Peripheral/src/ch32v30x_usart.o
 .text.USART_ClockStructInit
                0x***********00000       0x12 ./Peripheral/src/ch32v30x_usart.o
 .text.USART_ITConfig
                0x***********00000       0x36 ./Peripheral/src/ch32v30x_usart.o
 .text.USART_DMACmd
                0x***********00000       0x12 ./Peripheral/src/ch32v30x_usart.o
 .text.USART_SetAddress
                0x***********00000       0x16 ./Peripheral/src/ch32v30x_usart.o
 .text.USART_WakeUpConfig
                0x***********00000       0x14 ./Peripheral/src/ch32v30x_usart.o
 .text.USART_ReceiverWakeUpCmd
                0x***********00000       0x18 ./Peripheral/src/ch32v30x_usart.o
 .text.USART_LINBreakDetectLengthConfig
                0x***********00000       0x18 ./Peripheral/src/ch32v30x_usart.o
 .text.USART_LINCmd
                0x***********00000       0x16 ./Peripheral/src/ch32v30x_usart.o
 .text.USART_ReceiveData
                0x***********00000        0x8 ./Peripheral/src/ch32v30x_usart.o
 .text.USART_SendBreak
                0x***********00000        0xa ./Peripheral/src/ch32v30x_usart.o
 .text.USART_SetGuardTime
                0x***********00000       0x12 ./Peripheral/src/ch32v30x_usart.o
 .text.USART_SetPrescaler
                0x***********00000       0x18 ./Peripheral/src/ch32v30x_usart.o
 .text.USART_SmartCardCmd
                0x***********00000       0x1a ./Peripheral/src/ch32v30x_usart.o
 .text.USART_SmartCardNACKCmd
                0x***********00000       0x18 ./Peripheral/src/ch32v30x_usart.o
 .text.USART_HalfDuplexCmd
                0x***********00000       0x18 ./Peripheral/src/ch32v30x_usart.o
 .text.USART_IrDAConfig
                0x***********00000       0x16 ./Peripheral/src/ch32v30x_usart.o
 .text.USART_IrDACmd
                0x***********00000       0x18 ./Peripheral/src/ch32v30x_usart.o
 .text.USART_ClearFlag
                0x***********00000        0xc ./Peripheral/src/ch32v30x_usart.o
 .text.USART_GetITStatus
                0x***********00000       0x3c ./Peripheral/src/ch32v30x_usart.o
 .text.USART_ClearITPendingBit
                0x***********00000       0x14 ./Peripheral/src/ch32v30x_usart.o
 .text          0x***********00000        0x0 ./Peripheral/src/ch32v30x_wwdg.o
 .data          0x***********00000        0x0 ./Peripheral/src/ch32v30x_wwdg.o
 .bss           0x***********00000        0x0 ./Peripheral/src/ch32v30x_wwdg.o
 .text.WWDG_DeInit
                0x***********00000       0x2e ./Peripheral/src/ch32v30x_wwdg.o
 .text.WWDG_SetPrescaler
                0x***********00000       0x14 ./Peripheral/src/ch32v30x_wwdg.o
 .text.WWDG_SetWindowValue
                0x***********00000       0x26 ./Peripheral/src/ch32v30x_wwdg.o
 .text.WWDG_EnableIT
                0x***********00000       0x12 ./Peripheral/src/ch32v30x_wwdg.o
 .text.WWDG_SetCounter
                0x***********00000        0xe ./Peripheral/src/ch32v30x_wwdg.o
 .text.WWDG_Enable
                0x***********00000        0xe ./Peripheral/src/ch32v30x_wwdg.o
 .text.WWDG_GetFlagStatus
                0x***********00000        0xa ./Peripheral/src/ch32v30x_wwdg.o
 .text.WWDG_ClearFlag
                0x***********00000        0xa ./Peripheral/src/ch32v30x_wwdg.o
 .debug_info    0x***********00000      0xb28 ./Peripheral/src/ch32v30x_wwdg.o
 .debug_abbrev  0x***********00000      0x29d ./Peripheral/src/ch32v30x_wwdg.o
 .debug_loc     0x***********00000       0xae ./Peripheral/src/ch32v30x_wwdg.o
 .debug_aranges
                0x***********00000       0x58 ./Peripheral/src/ch32v30x_wwdg.o
 .debug_ranges  0x***********00000       0x48 ./Peripheral/src/ch32v30x_wwdg.o
 .debug_line    0x***********00000      0x437 ./Peripheral/src/ch32v30x_wwdg.o
 .debug_str     0x***********00000      0x68e ./Peripheral/src/ch32v30x_wwdg.o
 .comment       0x***********00000       0x34 ./Peripheral/src/ch32v30x_wwdg.o
 .debug_frame   0x***********00000       0xa4 ./Peripheral/src/ch32v30x_wwdg.o
 .text          0x***********00000        0x0 ./Driver/SysTickDelay.o
 .data          0x***********00000        0x0 ./Driver/SysTickDelay.o
 .bss           0x***********00000        0x0 ./Driver/SysTickDelay.o
 .text.SysTick_Init
                0x***********00000       0x32 ./Driver/SysTickDelay.o
 .text          0x***********00000        0x0 ./Driver/brewing_control.o
 .data          0x***********00000        0x0 ./Driver/brewing_control.o
 .bss           0x***********00000        0x0 ./Driver/brewing_control.o
 .text.BrewingControl_Stop
                0x***********00000       0x52 ./Driver/brewing_control.o
 .text.BrewingControl_GetState
                0x***********00000        0xa ./Driver/brewing_control.o
 .text.BrewingControl_TempCallback
                0x***********00000       0x3c ./Driver/brewing_control.o
 .text.BrewingControl_WaterLevelCallback
                0x***********00000        0xa ./Driver/brewing_control.o
 .rodata.BrewingControl_Stop.str1.4
                0x***********00000        0xf ./Driver/brewing_control.o
 .rodata.BrewingControl_TempCallback.str1.4
                0x***********00000       0x1b ./Driver/brewing_control.o
 .text          0x***********00000        0x0 ./Driver/ds18b20.o
 .data          0x***********00000        0x0 ./Driver/ds18b20.o
 .bss           0x***********00000        0x0 ./Driver/ds18b20.o
 .text.DS18B20_SetThreshold
                0x***********00000        0x4 ./Driver/ds18b20.o
 .text.DS18B20_GetAlarmFlag
                0x***********00000        0x4 ./Driver/ds18b20.o
 .text          0x***********00000        0x0 ./Driver/heater.o
 .data          0x***********00000        0x0 ./Driver/heater.o
 .bss           0x***********00000        0x0 ./Driver/heater.o
 .text          0x***********00000        0x0 ./Driver/key.o
 .data          0x***********00000        0x0 ./Driver/key.o
 .bss           0x***********00000        0x0 ./Driver/key.o
 .text          0x***********00000        0x0 ./Driver/lcd.o
 .data          0x***********00000        0x0 ./Driver/lcd.o
 .bss           0x***********00000        0x0 ./Driver/lcd.o
 .text.LCD_DrawPoint
                0x***********00000       0x28 ./Driver/lcd.o
 .text.LCD_DrawLine
                0x***********00000       0x90 ./Driver/lcd.o
 .text.LCD_DrawRectangle
                0x***********00000       0x5a ./Driver/lcd.o
 .text.Draw_Circle
                0x***********00000       0xfa ./Driver/lcd.o
 .text.LCD_ShowChinese16x16
                0x***********00000       0xfe ./Driver/lcd.o
 .text.LCD_ShowChinese24x24
                0x***********00000       0xfe ./Driver/lcd.o
 .text.LCD_ShowChinese32x32
                0x***********00000       0xfe ./Driver/lcd.o
 .text.LCD_ShowChinese
                0x***********00000       0x88 ./Driver/lcd.o
 .text.lcd_show_chinese
                0x***********00000       0x54 ./Driver/lcd.o
 .text.LCD_ShowChar
                0x***********00000       0xfe ./Driver/lcd.o
 .text.LCD_ShowString
                0x***********00000       0x44 ./Driver/lcd.o
 .text.mypow    0x***********00000       0x1a ./Driver/lcd.o
 .text.LCD_ShowIntNum
                0x***********00000       0xa0 ./Driver/lcd.o
 .text.LCD_ShowFloatNum1
                0x***********00000       0xd4 ./Driver/lcd.o
 .text.LCD_ShowPicture
                0x***********00000       0x76 ./Driver/lcd.o
 .rodata.LCD_ShowFloatNum1.cst4
                0x***********00000        0x4 ./Driver/lcd.o
 .rodata.ascii_1608
                0x***********00000      0x5f0 ./Driver/lcd.o
 .rodata.ascii_3216
                0x***********00000     0x17c0 ./Driver/lcd.o
 .rodata.tfont16
                0x***********00000      0x396 ./Driver/lcd.o
 .rodata.tfont24
                0x***********00000      0x172 ./Driver/lcd.o
 .rodata.tfont32
                0x***********00000      0x28a ./Driver/lcd.o
 .text          0x***********00000        0x0 ./Driver/timer.o
 .data          0x***********00000        0x0 ./Driver/timer.o
 .bss           0x***********00000        0x0 ./Driver/timer.o
 .text          0x***********00000        0x0 ./Driver/water_level.o
 .data          0x***********00000        0x0 ./Driver/water_level.o
 .bss           0x***********00000        0x0 ./Driver/water_level.o
 .text          0x***********00000        0x0 ./Driver/water_pump.o
 .data          0x***********00000        0x0 ./Driver/water_pump.o
 .bss           0x***********00000        0x0 ./Driver/water_pump.o
 .text.delay_us
                0x***********00000       0x10 ./Driver/water_pump.o
 .text.delay_ms
                0x***********00000       0x28 ./Driver/water_pump.o
 .text.WaterPump_SetEnable
                0x***********00000       0x4c ./Driver/water_pump.o
 .text.WaterPump_GetState
                0x***********00000       0x1c ./Driver/water_pump.o
 .text.WaterPump_AutoControl
                0x***********00000       0x18 ./Driver/water_pump.o
 .text.WaterPump_Task
                0x***********00000       0x72 ./Driver/water_pump.o
 .sbss.auto_timer.5309
                0x***********00000        0x8 ./Driver/water_pump.o
 .text          0x***********00000        0x0 ./Debug/debug.o
 .data          0x***********00000        0x0 ./Debug/debug.o
 .bss           0x***********00000        0x0 ./Debug/debug.o
 .text.Delay_Us
                0x***********00000       0x3a ./Debug/debug.o
 .text.SDI_Printf_Enable
                0x***********00000       0x2a ./Debug/debug.o
 .text          0x***********00000        0x0 ./Core/core_riscv.o
 .data          0x***********00000        0x0 ./Core/core_riscv.o
 .bss           0x***********00000        0x0 ./Core/core_riscv.o
 .text.__get_FFLAGS
                0x***********00000        0x6 ./Core/core_riscv.o
 .text.__set_FFLAGS
                0x***********00000        0x6 ./Core/core_riscv.o
 .text.__get_FRM
                0x***********00000        0x6 ./Core/core_riscv.o
 .text.__set_FRM
                0x***********00000        0x6 ./Core/core_riscv.o
 .text.__get_FCSR
                0x***********00000        0x6 ./Core/core_riscv.o
 .text.__set_FCSR
                0x***********00000        0x6 ./Core/core_riscv.o
 .text.__get_MSTATUS
                0x***********00000        0x6 ./Core/core_riscv.o
 .text.__set_MSTATUS
                0x***********00000        0x6 ./Core/core_riscv.o
 .text.__get_MISA
                0x***********00000        0x6 ./Core/core_riscv.o
 .text.__set_MISA
                0x***********00000        0x6 ./Core/core_riscv.o
 .text.__get_MTVEC
                0x***********00000        0x6 ./Core/core_riscv.o
 .text.__set_MTVEC
                0x***********00000        0x6 ./Core/core_riscv.o
 .text.__get_MSCRATCH
                0x***********00000        0x6 ./Core/core_riscv.o
 .text.__set_MSCRATCH
                0x***********00000        0x6 ./Core/core_riscv.o
 .text.__get_MEPC
                0x***********00000        0x6 ./Core/core_riscv.o
 .text.__set_MEPC
                0x***********00000        0x6 ./Core/core_riscv.o
 .text.__get_MCAUSE
                0x***********00000        0x6 ./Core/core_riscv.o
 .text.__set_MCAUSE
                0x***********00000        0x6 ./Core/core_riscv.o
 .text.__get_MTVAL
                0x***********00000        0x6 ./Core/core_riscv.o
 .text.__set_MTVAL
                0x***********00000        0x6 ./Core/core_riscv.o
 .text.__get_MVENDORID
                0x***********00000        0x6 ./Core/core_riscv.o
 .text.__get_MARCHID
                0x***********00000        0x6 ./Core/core_riscv.o
 .text.__get_MIMPID
                0x***********00000        0x6 ./Core/core_riscv.o
 .text.__get_MHARTID
                0x***********00000        0x6 ./Core/core_riscv.o
 .text.__get_SP
                0x***********00000        0x4 ./Core/core_riscv.o
 .debug_info    0x***********00000      0x46d ./Core/core_riscv.o
 .debug_abbrev  0x***********00000      0x10d ./Core/core_riscv.o
 .debug_aranges
                0x***********00000       0xe0 ./Core/core_riscv.o
 .debug_ranges  0x***********00000       0xd0 ./Core/core_riscv.o
 .debug_line    0x***********00000      0x542 ./Core/core_riscv.o
 .debug_str     0x***********00000      0x2bc ./Core/core_riscv.o
 .comment       0x***********00000       0x34 ./Core/core_riscv.o
 .debug_frame   0x***********00000      0x1a0 ./Core/core_riscv.o
 .text          0x***********00000        0x0 d:/mounriver/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/rv32imacxw/ilp32\libgcc.a(gesf2.o)
 .data          0x***********00000        0x0 d:/mounriver/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/rv32imacxw/ilp32\libgcc.a(gesf2.o)
 .bss           0x***********00000        0x0 d:/mounriver/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/rv32imacxw/ilp32\libgcc.a(gesf2.o)
 .text          0x***********00000        0x0 d:/mounriver/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/rv32imacxw/ilp32\libgcc.a(lesf2.o)
 .data          0x***********00000        0x0 d:/mounriver/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/rv32imacxw/ilp32\libgcc.a(lesf2.o)
 .bss           0x***********00000        0x0 d:/mounriver/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/rv32imacxw/ilp32\libgcc.a(lesf2.o)
 .text          0x***********00000        0x0 d:/mounriver/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/rv32imacxw/ilp32\libgcc.a(mulsf3.o)
 .data          0x***********00000        0x0 d:/mounriver/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/rv32imacxw/ilp32\libgcc.a(mulsf3.o)
 .bss           0x***********00000        0x0 d:/mounriver/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/rv32imacxw/ilp32\libgcc.a(mulsf3.o)
 .text          0x***********00000        0x0 d:/mounriver/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/rv32imacxw/ilp32\libgcc.a(subsf3.o)
 .data          0x***********00000        0x0 d:/mounriver/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/rv32imacxw/ilp32\libgcc.a(subsf3.o)
 .bss           0x***********00000        0x0 d:/mounriver/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/rv32imacxw/ilp32\libgcc.a(subsf3.o)
 .text          0x***********00000        0x0 d:/mounriver/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/rv32imacxw/ilp32\libgcc.a(fixunssfsi.o)
 .data          0x***********00000        0x0 d:/mounriver/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/rv32imacxw/ilp32\libgcc.a(fixunssfsi.o)
 .bss           0x***********00000        0x0 d:/mounriver/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/rv32imacxw/ilp32\libgcc.a(fixunssfsi.o)
 .text.__fixunssfsi
                0x***********00000       0x56 d:/mounriver/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/rv32imacxw/ilp32\libgcc.a(fixunssfsi.o)
 .debug_frame   0x***********00000       0x20 d:/mounriver/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/rv32imacxw/ilp32\libgcc.a(fixunssfsi.o)
 .text          0x***********00000        0x0 d:/mounriver/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/rv32imacxw/ilp32\libgcc.a(floatsisf.o)
 .data          0x***********00000        0x0 d:/mounriver/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/rv32imacxw/ilp32\libgcc.a(floatsisf.o)
 .bss           0x***********00000        0x0 d:/mounriver/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/rv32imacxw/ilp32\libgcc.a(floatsisf.o)
 .text          0x***********00000        0x0 d:/mounriver/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/rv32imacxw/ilp32\libgcc.a(extendsfdf2.o)
 .data          0x***********00000        0x0 d:/mounriver/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/rv32imacxw/ilp32\libgcc.a(extendsfdf2.o)
 .bss           0x***********00000        0x0 d:/mounriver/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/rv32imacxw/ilp32\libgcc.a(extendsfdf2.o)
 .data          0x***********00000        0x0 d:/mounriver/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/rv32imacxw/ilp32\libgcc.a(save-restore.o)
 .bss           0x***********00000        0x0 d:/mounriver/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/rv32imacxw/ilp32\libgcc.a(save-restore.o)
 .eh_frame      0x***********00000       0xf8 d:/mounriver/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/rv32imacxw/ilp32\libgcc.a(save-restore.o)
 .text          0x***********00000        0x0 d:/mounriver/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/rv32imacxw/ilp32\libgcc.a(_clzsi2.o)
 .data          0x***********00000        0x0 d:/mounriver/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/rv32imacxw/ilp32\libgcc.a(_clzsi2.o)
 .bss           0x***********00000        0x0 d:/mounriver/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/rv32imacxw/ilp32\libgcc.a(_clzsi2.o)
 .text          0x***********00000        0x0 d:/mounriver/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/rv32imacxw/ilp32\libgcc.a(_clz.o)
 .data          0x***********00000        0x0 d:/mounriver/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/rv32imacxw/ilp32\libgcc.a(_clz.o)
 .bss           0x***********00000        0x0 d:/mounriver/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/rv32imacxw/ilp32\libgcc.a(_clz.o)
 .text          0x***********00000        0x0 d:/mounriver/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libg_nano.a(lib_a-printf.o)
 .data          0x***********00000        0x0 d:/mounriver/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libg_nano.a(lib_a-printf.o)
 .bss           0x***********00000        0x0 d:/mounriver/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libg_nano.a(lib_a-printf.o)
 .text._printf_r
                0x***********00000       0x40 d:/mounriver/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libg_nano.a(lib_a-printf.o)
 .text          0x***********00000        0x0 d:/mounriver/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libg_nano.a(lib_a-puts.o)
 .data          0x***********00000        0x0 d:/mounriver/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libg_nano.a(lib_a-puts.o)
 .bss           0x***********00000        0x0 d:/mounriver/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libg_nano.a(lib_a-puts.o)
 .text          0x***********00000        0x0 d:/mounriver/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libg_nano.a(lib_a-wbuf.o)
 .data          0x***********00000        0x0 d:/mounriver/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libg_nano.a(lib_a-wbuf.o)
 .bss           0x***********00000        0x0 d:/mounriver/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libg_nano.a(lib_a-wbuf.o)
 .text.__swbuf  0x***********00000       0x16 d:/mounriver/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libg_nano.a(lib_a-wbuf.o)
 .text          0x***********00000        0x0 d:/mounriver/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libg_nano.a(lib_a-wsetup.o)
 .data          0x***********00000        0x0 d:/mounriver/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libg_nano.a(lib_a-wsetup.o)
 .bss           0x***********00000        0x0 d:/mounriver/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libg_nano.a(lib_a-wsetup.o)
 .text          0x***********00000        0x0 d:/mounriver/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libg_nano.a(lib_a-fflush.o)
 .data          0x***********00000        0x0 d:/mounriver/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libg_nano.a(lib_a-fflush.o)
 .bss           0x***********00000        0x0 d:/mounriver/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libg_nano.a(lib_a-fflush.o)
 .text.fflush   0x***********00000       0x30 d:/mounriver/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libg_nano.a(lib_a-fflush.o)
 .text          0x***********00000        0x0 d:/mounriver/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libg_nano.a(lib_a-findfp.o)
 .data          0x***********00000        0x0 d:/mounriver/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libg_nano.a(lib_a-findfp.o)
 .bss           0x***********00000        0x0 d:/mounriver/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libg_nano.a(lib_a-findfp.o)
 .text.__fp_lock
                0x***********00000        0x4 d:/mounriver/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libg_nano.a(lib_a-findfp.o)
 .text.__fp_unlock
                0x***********00000        0x4 d:/mounriver/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libg_nano.a(lib_a-findfp.o)
 .text._cleanup
                0x***********00000       0x12 d:/mounriver/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libg_nano.a(lib_a-findfp.o)
 .text.__sfp_lock_acquire
                0x***********00000        0x2 d:/mounriver/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libg_nano.a(lib_a-findfp.o)
 .text.__sfp_lock_release
                0x***********00000        0x2 d:/mounriver/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libg_nano.a(lib_a-findfp.o)
 .text.__sinit_lock_acquire
                0x***********00000        0x2 d:/mounriver/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libg_nano.a(lib_a-findfp.o)
 .text.__sinit_lock_release
                0x***********00000        0x2 d:/mounriver/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libg_nano.a(lib_a-findfp.o)
 .text.__fp_lock_all
                0x***********00000       0x1a d:/mounriver/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libg_nano.a(lib_a-findfp.o)
 .text.__fp_unlock_all
                0x***********00000       0x1a d:/mounriver/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libg_nano.a(lib_a-findfp.o)
 .text          0x***********00000        0x0 d:/mounriver/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libg_nano.a(lib_a-fwalk.o)
 .data          0x***********00000        0x0 d:/mounriver/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libg_nano.a(lib_a-fwalk.o)
 .bss           0x***********00000        0x0 d:/mounriver/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libg_nano.a(lib_a-fwalk.o)
 .text._fwalk   0x***********00000       0x60 d:/mounriver/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libg_nano.a(lib_a-fwalk.o)
 .text          0x***********00000        0x0 d:/mounriver/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libg_nano.a(lib_a-impure.o)
 .data          0x***********00000        0x0 d:/mounriver/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libg_nano.a(lib_a-impure.o)
 .bss           0x***********00000        0x0 d:/mounriver/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libg_nano.a(lib_a-impure.o)
 .text          0x***********00000        0x0 d:/mounriver/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libg_nano.a(lib_a-makebuf.o)
 .data          0x***********00000        0x0 d:/mounriver/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libg_nano.a(lib_a-makebuf.o)
 .bss           0x***********00000        0x0 d:/mounriver/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libg_nano.a(lib_a-makebuf.o)
 .data          0x***********00000        0x0 d:/mounriver/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libg_nano.a(lib_a-memset.o)
 .bss           0x***********00000        0x0 d:/mounriver/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libg_nano.a(lib_a-memset.o)
 .text          0x***********00000        0x0 d:/mounriver/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libg_nano.a(lib_a-nano-freer.o)
 .data          0x***********00000        0x0 d:/mounriver/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libg_nano.a(lib_a-nano-freer.o)
 .bss           0x***********00000        0x0 d:/mounriver/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libg_nano.a(lib_a-nano-freer.o)
 .text          0x***********00000        0x0 d:/mounriver/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libg_nano.a(lib_a-nano-mallocr.o)
 .data          0x***********00000        0x0 d:/mounriver/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libg_nano.a(lib_a-nano-mallocr.o)
 .bss           0x***********00000        0x0 d:/mounriver/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libg_nano.a(lib_a-nano-mallocr.o)
 .text          0x***********00000        0x0 d:/mounriver/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libg_nano.a(lib_a-nano-vfprintf.o)
 .data          0x***********00000        0x0 d:/mounriver/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libg_nano.a(lib_a-nano-vfprintf.o)
 .bss           0x***********00000        0x0 d:/mounriver/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libg_nano.a(lib_a-nano-vfprintf.o)
 .text.__sprint_r
                0x***********00000       0x2c d:/mounriver/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libg_nano.a(lib_a-nano-vfprintf.o)
 .text.vfprintf
                0x***********00000       0x18 d:/mounriver/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libg_nano.a(lib_a-nano-vfprintf.o)
 .text          0x***********00000        0x0 d:/mounriver/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libg_nano.a(lib_a-nano-vfprintf_i.o)
 .data          0x***********00000        0x0 d:/mounriver/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libg_nano.a(lib_a-nano-vfprintf_i.o)
 .bss           0x***********00000        0x0 d:/mounriver/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libg_nano.a(lib_a-nano-vfprintf_i.o)
 .text          0x***********00000        0x0 d:/mounriver/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libg_nano.a(lib_a-sbrkr.o)
 .data          0x***********00000        0x0 d:/mounriver/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libg_nano.a(lib_a-sbrkr.o)
 .bss           0x***********00000        0x0 d:/mounriver/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libg_nano.a(lib_a-sbrkr.o)
 .text          0x***********00000        0x0 d:/mounriver/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libg_nano.a(lib_a-stdio.o)
 .data          0x***********00000        0x0 d:/mounriver/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libg_nano.a(lib_a-stdio.o)
 .bss           0x***********00000        0x0 d:/mounriver/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libg_nano.a(lib_a-stdio.o)
 .text.__seofread
                0x***********00000        0x4 d:/mounriver/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libg_nano.a(lib_a-stdio.o)
 .text          0x***********00000        0x0 d:/mounriver/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libg_nano.a(lib_a-writer.o)
 .data          0x***********00000        0x0 d:/mounriver/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libg_nano.a(lib_a-writer.o)
 .bss           0x***********00000        0x0 d:/mounriver/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libg_nano.a(lib_a-writer.o)
 .text          0x***********00000        0x0 d:/mounriver/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libg_nano.a(lib_a-closer.o)
 .data          0x***********00000        0x0 d:/mounriver/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libg_nano.a(lib_a-closer.o)
 .bss           0x***********00000        0x0 d:/mounriver/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libg_nano.a(lib_a-closer.o)
 .text          0x***********00000        0x0 d:/mounriver/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libg_nano.a(lib_a-fstatr.o)
 .data          0x***********00000        0x0 d:/mounriver/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libg_nano.a(lib_a-fstatr.o)
 .bss           0x***********00000        0x0 d:/mounriver/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libg_nano.a(lib_a-fstatr.o)
 .text          0x***********00000        0x0 d:/mounriver/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libg_nano.a(lib_a-fvwrite.o)
 .data          0x***********00000        0x0 d:/mounriver/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libg_nano.a(lib_a-fvwrite.o)
 .bss           0x***********00000        0x0 d:/mounriver/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libg_nano.a(lib_a-fvwrite.o)
 .text.__sfvwrite_r
                0x***********00000      0x306 d:/mounriver/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libg_nano.a(lib_a-fvwrite.o)
 .debug_frame   0x***********00000       0x60 d:/mounriver/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libg_nano.a(lib_a-fvwrite.o)
 .text          0x***********00000        0x0 d:/mounriver/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libg_nano.a(lib_a-isattyr.o)
 .data          0x***********00000        0x0 d:/mounriver/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libg_nano.a(lib_a-isattyr.o)
 .bss           0x***********00000        0x0 d:/mounriver/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libg_nano.a(lib_a-isattyr.o)
 .text          0x***********00000        0x0 d:/mounriver/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libg_nano.a(lib_a-lseekr.o)
 .data          0x***********00000        0x0 d:/mounriver/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libg_nano.a(lib_a-lseekr.o)
 .bss           0x***********00000        0x0 d:/mounriver/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libg_nano.a(lib_a-lseekr.o)
 .text          0x***********00000        0x0 d:/mounriver/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libg_nano.a(lib_a-memchr.o)
 .data          0x***********00000        0x0 d:/mounriver/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libg_nano.a(lib_a-memchr.o)
 .bss           0x***********00000        0x0 d:/mounriver/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libg_nano.a(lib_a-memchr.o)
 .text          0x***********00000        0x0 d:/mounriver/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libg_nano.a(lib_a-memcpy.o)
 .data          0x***********00000        0x0 d:/mounriver/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libg_nano.a(lib_a-memcpy.o)
 .bss           0x***********00000        0x0 d:/mounriver/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libg_nano.a(lib_a-memcpy.o)
 .text.memcpy   0x***********00000       0xb2 d:/mounriver/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libg_nano.a(lib_a-memcpy.o)
 .debug_frame   0x***********00000       0x20 d:/mounriver/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libg_nano.a(lib_a-memcpy.o)
 .text          0x***********00000        0x0 d:/mounriver/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libg_nano.a(lib_a-memmove.o)
 .data          0x***********00000        0x0 d:/mounriver/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libg_nano.a(lib_a-memmove.o)
 .bss           0x***********00000        0x0 d:/mounriver/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libg_nano.a(lib_a-memmove.o)
 .text.memmove  0x***********00000       0x46 d:/mounriver/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libg_nano.a(lib_a-memmove.o)
 .debug_frame   0x***********00000       0x20 d:/mounriver/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libg_nano.a(lib_a-memmove.o)
 .text          0x***********00000        0x0 d:/mounriver/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libg_nano.a(lib_a-mlock.o)
 .data          0x***********00000        0x0 d:/mounriver/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libg_nano.a(lib_a-mlock.o)
 .bss           0x***********00000        0x0 d:/mounriver/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libg_nano.a(lib_a-mlock.o)
 .text          0x***********00000        0x0 d:/mounriver/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libg_nano.a(lib_a-nano-reallocr.o)
 .data          0x***********00000        0x0 d:/mounriver/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libg_nano.a(lib_a-nano-reallocr.o)
 .bss           0x***********00000        0x0 d:/mounriver/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libg_nano.a(lib_a-nano-reallocr.o)
 .text._realloc_r
                0x***********00000       0x72 d:/mounriver/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libg_nano.a(lib_a-nano-reallocr.o)
 .debug_frame   0x***********00000       0x40 d:/mounriver/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libg_nano.a(lib_a-nano-reallocr.o)
 .text          0x***********00000        0x0 d:/mounriver/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libg_nano.a(lib_a-readr.o)
 .data          0x***********00000        0x0 d:/mounriver/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libg_nano.a(lib_a-readr.o)
 .bss           0x***********00000        0x0 d:/mounriver/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libg_nano.a(lib_a-readr.o)
 .text          0x***********00000        0x0 d:/mounriver/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libg_nano.a(lib_a-reent.o)
 .data          0x***********00000        0x0 d:/mounriver/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libg_nano.a(lib_a-reent.o)
 .bss           0x***********00000        0x0 d:/mounriver/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libg_nano.a(lib_a-reent.o)
 .text.cleanup_glue
                0x***********00000       0x2c d:/mounriver/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libg_nano.a(lib_a-reent.o)
 .text._reclaim_reent
                0x***********00000      0x10c d:/mounriver/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libg_nano.a(lib_a-reent.o)
 .text          0x***********00000        0x0 d:/mounriver/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libg_nano.a(lib_a-nano-msizer.o)
 .data          0x***********00000        0x0 d:/mounriver/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libg_nano.a(lib_a-nano-msizer.o)
 .bss           0x***********00000        0x0 d:/mounriver/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libg_nano.a(lib_a-nano-msizer.o)
 .text._malloc_usable_size_r
                0x***********00000       0x14 d:/mounriver/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libg_nano.a(lib_a-nano-msizer.o)
 .debug_frame   0x***********00000       0x20 d:/mounriver/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libg_nano.a(lib_a-nano-msizer.o)
 .text          0x***********00000        0x0 d:/mounriver/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libnosys.a(close.o)
 .data          0x***********00000        0x0 d:/mounriver/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libnosys.a(close.o)
 .bss           0x***********00000        0x0 d:/mounriver/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libnosys.a(close.o)
 .text          0x***********00000        0x0 d:/mounriver/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libnosys.a(fstat.o)
 .data          0x***********00000        0x0 d:/mounriver/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libnosys.a(fstat.o)
 .bss           0x***********00000        0x0 d:/mounriver/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libnosys.a(fstat.o)
 .text          0x***********00000        0x0 d:/mounriver/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libnosys.a(isatty.o)
 .data          0x***********00000        0x0 d:/mounriver/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libnosys.a(isatty.o)
 .bss           0x***********00000        0x0 d:/mounriver/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libnosys.a(isatty.o)
 .text          0x***********00000        0x0 d:/mounriver/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libnosys.a(lseek.o)
 .data          0x***********00000        0x0 d:/mounriver/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libnosys.a(lseek.o)
 .bss           0x***********00000        0x0 d:/mounriver/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libnosys.a(lseek.o)
 .text          0x***********00000        0x0 d:/mounriver/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libnosys.a(read.o)
 .data          0x***********00000        0x0 d:/mounriver/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libnosys.a(read.o)
 .bss           0x***********00000        0x0 d:/mounriver/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libnosys.a(read.o)

Memory Configuration

Name             Origin             Length             Attributes
FLASH            0x***********00000 0x***********48000 xr
RAM              0x0000000020000000 0x***********08000 xrw
*default*        0x***********00000 0xffffffffffffffff

Linker script and memory map

LOAD ./User/ch32v30x_it.o
LOAD ./User/main.o
LOAD ./User/system_ch32v30x.o
LOAD ./Startup/startup_ch32v30x_D8C.o
LOAD ./Peripheral/src/ch32v30x_adc.o
LOAD ./Peripheral/src/ch32v30x_bkp.o
LOAD ./Peripheral/src/ch32v30x_can.o
LOAD ./Peripheral/src/ch32v30x_crc.o
LOAD ./Peripheral/src/ch32v30x_dac.o
LOAD ./Peripheral/src/ch32v30x_dbgmcu.o
LOAD ./Peripheral/src/ch32v30x_dma.o
LOAD ./Peripheral/src/ch32v30x_dvp.o
LOAD ./Peripheral/src/ch32v30x_eth.o
LOAD ./Peripheral/src/ch32v30x_exti.o
LOAD ./Peripheral/src/ch32v30x_flash.o
LOAD ./Peripheral/src/ch32v30x_fsmc.o
LOAD ./Peripheral/src/ch32v30x_gpio.o
LOAD ./Peripheral/src/ch32v30x_i2c.o
LOAD ./Peripheral/src/ch32v30x_iwdg.o
LOAD ./Peripheral/src/ch32v30x_misc.o
LOAD ./Peripheral/src/ch32v30x_opa.o
LOAD ./Peripheral/src/ch32v30x_pwr.o
LOAD ./Peripheral/src/ch32v30x_rcc.o
LOAD ./Peripheral/src/ch32v30x_rng.o
LOAD ./Peripheral/src/ch32v30x_rtc.o
LOAD ./Peripheral/src/ch32v30x_sdio.o
LOAD ./Peripheral/src/ch32v30x_spi.o
LOAD ./Peripheral/src/ch32v30x_tim.o
LOAD ./Peripheral/src/ch32v30x_usart.o
LOAD ./Peripheral/src/ch32v30x_wwdg.o
LOAD ./Driver/SysTickDelay.o
LOAD ./Driver/brewing_control.o
LOAD ./Driver/ds18b20.o
LOAD ./Driver/heater.o
LOAD ./Driver/key.o
LOAD ./Driver/lcd.o
LOAD ./Driver/timer.o
LOAD ./Driver/water_level.o
LOAD ./Driver/water_pump.o
LOAD ./Debug/debug.o
LOAD ./Core/core_riscv.o
LOAD d:/mounriver/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libm.a
LOAD d:/mounriver/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/rv32imacxw/ilp32\libgcc.a
LOAD d:/mounriver/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libg_nano.a
LOAD d:/mounriver/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libc_nano.a
LOAD d:/mounriver/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/rv32imacxw/ilp32\libgcc.a
START GROUP
LOAD d:/mounriver/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/rv32imacxw/ilp32\libgcc.a
LOAD d:/mounriver/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libc_nano.a
LOAD d:/mounriver/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libnosys.a
END GROUP
START GROUP
LOAD d:/mounriver/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/rv32imacxw/ilp32\libgcc.a
LOAD d:/mounriver/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libc_nano.a
LOAD d:/mounriver/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libnosys.a
END GROUP
                0x***********00800                __stack_size = 0x800
                [!provide]                        PROVIDE (_stack_size = __stack_size)

.init           0x***********00000        0x4
                0x***********00000                _sinit = .
                0x***********00000                . = ALIGN (0x4)
 *(SORT_NONE(.init))
 .init          0x***********00000        0x4 ./Startup/startup_ch32v30x_D8C.o
                0x***********00000                _start
                0x***********00004                . = ALIGN (0x4)
                0x***********00004                _einit = .

.vector         0x***********00004      0x1bc
 *(.vector)
 .vector        0x***********00004      0x1a0 ./Startup/startup_ch32v30x_D8C.o
                0x***********001c0                . = ALIGN (0x40)
 *fill*         0x***********001a4       0x1c 

.text           0x***********001c0     0x39c0
                0x***********001c0                . = ALIGN (0x4)
 *(.text)
 .text          0x***********001c0       0x60 d:/mounriver/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/rv32imacxw/ilp32\libgcc.a(save-restore.o)
                0x***********001c0                __riscv_save_12
                0x***********001c8                __riscv_save_9
                0x***********001c8                __riscv_save_11
                0x***********001c8                __riscv_save_10
                0x***********001c8                __riscv_save_8
                0x***********001d6                __riscv_save_4
                0x***********001d6                __riscv_save_6
                0x***********001d6                __riscv_save_5
                0x***********001d6                __riscv_save_7
                0x***********001f0                __riscv_save_3
                0x***********001f0                __riscv_save_2
                0x***********001f0                __riscv_save_1
                0x***********001f0                __riscv_save_0
                0x***********001fc                __riscv_restore_12
                0x***********00200                __riscv_restore_11
                0x***********00200                __riscv_restore_9
                0x***********00200                __riscv_restore_10
                0x***********00200                __riscv_restore_8
                0x***********0020a                __riscv_restore_5
                0x***********0020a                __riscv_restore_7
                0x***********0020a                __riscv_restore_6
                0x***********0020a                __riscv_restore_4
                0x***********00214                __riscv_restore_3
                0x***********00214                __riscv_restore_0
                0x***********00214                __riscv_restore_2
                0x***********00214                __riscv_restore_1
 .text          0x***********00220       0xa8 d:/mounriver/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libg_nano.a(lib_a-memset.o)
                0x***********00220                memset
 *(.text.*)
 .text.NMI_Handler
                0x***********002c8        0x2 ./User/ch32v30x_it.o
                0x***********002c8                NMI_Handler
 .text.HardFault_Handler
                0x***********002ca       0x10 ./User/ch32v30x_it.o
                0x***********002ca                HardFault_Handler
 .text.lcd_proc
                0x***********002da        0x2 ./User/main.o
                0x***********002da                lcd_proc
 .text.key_proc
                0x***********002dc       0x3a ./User/main.o
                0x***********002dc                key_proc
 .text.TempAlarmHandler
                0x***********00316       0x1a ./User/main.o
                0x***********00316                TempAlarmHandler
 .text.lock_proc
                0x***********00330        0x2 ./User/main.o
                0x***********00330                lock_proc
 .text.Tim3_Init
                0x***********00332       0x56 ./User/main.o
                0x***********00332                Tim3_Init
 .text.TIM3_IRQHandler
                0x***********00388       0x2c ./User/main.o
                0x***********00388                TIM3_IRQHandler
 .text.scheduler_run
                0x***********003b4       0x3c ./User/main.o
                0x***********003b4                scheduler_run
 .text.startup.main
                0x***********003f0       0xa8 ./User/main.o
                0x***********003f0                main
 .text.SystemInit
                0x***********00498       0xfa ./User/system_ch32v30x.o
                0x***********00498                SystemInit
 .text.SystemCoreClockUpdate
                0x***********00592      0x12e ./User/system_ch32v30x.o
                0x***********00592                SystemCoreClockUpdate
 .text.vector_handler
                0x***********006c0        0x2 ./Startup/startup_ch32v30x_D8C.o
                0x***********006c0                EXTI2_IRQHandler
                0x***********006c0                TIM8_TRG_COM_IRQHandler
                0x***********006c0                TIM8_CC_IRQHandler
                0x***********006c0                UART8_IRQHandler
                0x***********006c0                TIM1_CC_IRQHandler
                0x***********006c0                TIM6_IRQHandler
                0x***********006c0                SysTick_Handler
                0x***********006c0                PVD_IRQHandler
                0x***********006c0                SDIO_IRQHandler
                0x***********006c0                TIM9_BRK_IRQHandler
                0x***********006c0                DMA2_Channel8_IRQHandler
                0x***********006c0                CAN2_RX1_IRQHandler
                0x***********006c0                EXTI3_IRQHandler
                0x***********006c0                USBHS_IRQHandler
                0x***********006c0                DMA2_Channel9_IRQHandler
                0x***********006c0                TIM10_CC_IRQHandler
                0x***********006c0                USBFS_IRQHandler
                0x***********006c0                EXTI0_IRQHandler
                0x***********006c0                I2C2_EV_IRQHandler
                0x***********006c0                TIM10_TRG_COM_IRQHandler
                0x***********006c0                CAN2_SCE_IRQHandler
                0x***********006c0                ADC1_2_IRQHandler
                0x***********006c0                Break_Point_Handler
                0x***********006c0                SPI1_IRQHandler
                0x***********006c0                TAMPER_IRQHandler
                0x***********006c0                CAN2_RX0_IRQHandler
                0x***********006c0                TIM8_UP_IRQHandler
                0x***********006c0                Ecall_M_Mode_Handler
                0x***********006c0                DMA2_Channel2_IRQHandler
                0x***********006c0                DMA1_Channel4_IRQHandler
                0x***********006c0                TIM9_UP_IRQHandler
                0x***********006c0                USART3_IRQHandler
                0x***********006c0                RTC_IRQHandler
                0x***********006c0                DMA1_Channel7_IRQHandler
                0x***********006c0                CAN1_RX1_IRQHandler
                0x***********006c0                DVP_IRQHandler
                0x***********006c0                UART5_IRQHandler
                0x***********006c0                TIM4_IRQHandler
                0x***********006c0                DMA2_Channel1_IRQHandler
                0x***********006c0                I2C1_EV_IRQHandler
                0x***********006c0                DMA1_Channel6_IRQHandler
                0x***********006c0                UART4_IRQHandler
                0x***********006c0                DMA2_Channel4_IRQHandler
                0x***********006c0                RCC_IRQHandler
                0x***********006c0                TIM1_TRG_COM_IRQHandler
                0x***********006c0                DMA1_Channel1_IRQHandler
                0x***********006c0                DMA2_Channel7_IRQHandler
                0x***********006c0                EXTI15_10_IRQHandler
                0x***********006c0                TIM7_IRQHandler
                0x***********006c0                CAN2_TX_IRQHandler
                0x***********006c0                TIM5_IRQHandler
                0x***********006c0                EXTI9_5_IRQHandler
                0x***********006c0                ETH_WKUP_IRQHandler
                0x***********006c0                SPI2_IRQHandler
                0x***********006c0                TIM10_BRK_IRQHandler
                0x***********006c0                TIM9_CC_IRQHandler
                0x***********006c0                DMA2_Channel5_IRQHandler
                0x***********006c0                DMA1_Channel5_IRQHandler
                0x***********006c0                EXTI4_IRQHandler
                0x***********006c0                USB_LP_CAN1_RX0_IRQHandler
                0x***********006c0                RNG_IRQHandler
                0x***********006c0                USB_HP_CAN1_TX_IRQHandler
                0x***********006c0                DMA1_Channel3_IRQHandler
                0x***********006c0                ETH_IRQHandler
                0x***********006c0                TIM1_UP_IRQHandler
                0x***********006c0                WWDG_IRQHandler
                0x***********006c0                USBHSWakeup_IRQHandler
                0x***********006c0                DMA2_Channel11_IRQHandler
                0x***********006c0                Ecall_U_Mode_Handler
                0x***********006c0                DMA2_Channel6_IRQHandler
                0x***********006c0                TIM2_IRQHandler
                0x***********006c0                SW_Handler
                0x***********006c0                TIM1_BRK_IRQHandler
                0x***********006c0                DMA2_Channel10_IRQHandler
                0x***********006c0                EXTI1_IRQHandler
                0x***********006c0                RTCAlarm_IRQHandler
                0x***********006c0                TIM10_UP_IRQHandler
                0x***********006c0                TIM9_TRG_COM_IRQHandler
                0x***********006c0                UART7_IRQHandler
                0x***********006c0                USART2_IRQHandler
                0x***********006c0                UART6_IRQHandler
                0x***********006c0                I2C2_ER_IRQHandler
                0x***********006c0                DMA1_Channel2_IRQHandler
                0x***********006c0                TIM8_BRK_IRQHandler
                0x***********006c0                CAN1_SCE_IRQHandler
                0x***********006c0                FLASH_IRQHandler
                0x***********006c0                USART1_IRQHandler
                0x***********006c0                I2C1_ER_IRQHandler
                0x***********006c0                USBWakeUp_IRQHandler
                0x***********006c0                DMA2_Channel3_IRQHandler
 .text.handle_reset
                0x***********006c2       0x8a ./Startup/startup_ch32v30x_D8C.o
                0x***********006c2                handle_reset
 .text.GPIO_Init
                0x***********0074c       0xc0 ./Peripheral/src/ch32v30x_gpio.o
                0x***********0074c                GPIO_Init
 .text.GPIO_ReadInputDataBit
                0x***********0080c        0xa ./Peripheral/src/ch32v30x_gpio.o
                0x***********0080c                GPIO_ReadInputDataBit
 .text.GPIO_SetBits
                0x***********00816        0x4 ./Peripheral/src/ch32v30x_gpio.o
                0x***********00816                GPIO_SetBits
 .text.GPIO_ResetBits
                0x***********0081a        0x4 ./Peripheral/src/ch32v30x_gpio.o
                0x***********0081a                GPIO_ResetBits
 .text.NVIC_PriorityGroupConfig
                0x***********0081e        0x6 ./Peripheral/src/ch32v30x_misc.o
                0x***********0081e                NVIC_PriorityGroupConfig
 .text.NVIC_Init
                0x***********00824       0x4e ./Peripheral/src/ch32v30x_misc.o
                0x***********00824                NVIC_Init
 .text.RCC_GetClocksFreq
                0x***********00872      0x176 ./Peripheral/src/ch32v30x_rcc.o
                0x***********00872                RCC_GetClocksFreq
 .text.RCC_APB2PeriphClockCmd
                0x***********009e8       0x1e ./Peripheral/src/ch32v30x_rcc.o
                0x***********009e8                RCC_APB2PeriphClockCmd
 .text.RCC_APB1PeriphClockCmd
                0x***********00a06       0x1e ./Peripheral/src/ch32v30x_rcc.o
                0x***********00a06                RCC_APB1PeriphClockCmd
 .text.SPI_Init
                0x***********00a24       0x3e ./Peripheral/src/ch32v30x_spi.o
                0x***********00a24                SPI_Init
 .text.SPI_Cmd  0x***********00a62       0x1a ./Peripheral/src/ch32v30x_spi.o
                0x***********00a62                SPI_Cmd
 .text.SPI_I2S_SendData
                0x***********00a7c        0x4 ./Peripheral/src/ch32v30x_spi.o
                0x***********00a7c                SPI_I2S_SendData
 .text.SPI_I2S_ReceiveData
                0x***********00a80        0x4 ./Peripheral/src/ch32v30x_spi.o
                0x***********00a80                SPI_I2S_ReceiveData
 .text.SPI_I2S_GetFlagStatus
                0x***********00a84        0xa ./Peripheral/src/ch32v30x_spi.o
                0x***********00a84                SPI_I2S_GetFlagStatus
 .text.TIM_TimeBaseInit
                0x***********00a8e       0xaa ./Peripheral/src/ch32v30x_tim.o
                0x***********00a8e                TIM_TimeBaseInit
 .text.TIM_OC1Init
                0x***********00b38       0x82 ./Peripheral/src/ch32v30x_tim.o
                0x***********00b38                TIM_OC1Init
 .text.TIM_Cmd  0x***********00bba       0x18 ./Peripheral/src/ch32v30x_tim.o
                0x***********00bba                TIM_Cmd
 .text.TIM_ITConfig
                0x***********00bd2       0x12 ./Peripheral/src/ch32v30x_tim.o
                0x***********00bd2                TIM_ITConfig
 .text.TIM_ARRPreloadConfig
                0x***********00be4       0x1a ./Peripheral/src/ch32v30x_tim.o
                0x***********00be4                TIM_ARRPreloadConfig
 .text.TIM_OC1PreloadConfig
                0x***********00bfe        0xe ./Peripheral/src/ch32v30x_tim.o
                0x***********00bfe                TIM_OC1PreloadConfig
 .text.TIM_SetCompare1
                0x***********00c0c        0x4 ./Peripheral/src/ch32v30x_tim.o
                0x***********00c0c                TIM_SetCompare1
 .text.TIM_GetITStatus
                0x***********00c10       0x18 ./Peripheral/src/ch32v30x_tim.o
                0x***********00c10                TIM_GetITStatus
 .text.TIM_ClearITPendingBit
                0x***********00c28        0xc ./Peripheral/src/ch32v30x_tim.o
                0x***********00c28                TIM_ClearITPendingBit
 .text.USART_Init
                0x***********00c34       0x8e ./Peripheral/src/ch32v30x_usart.o
                0x***********00c34                USART_Init
 .text.USART_Cmd
                0x***********00cc2       0x16 ./Peripheral/src/ch32v30x_usart.o
                0x***********00cc2                USART_Cmd
 .text.USART_SendData
                0x***********00cd8        0x8 ./Peripheral/src/ch32v30x_usart.o
                0x***********00cd8                USART_SendData
 .text.USART_GetFlagStatus
                0x***********00ce0        0xa ./Peripheral/src/ch32v30x_usart.o
                0x***********00ce0                USART_GetFlagStatus
 .text.Delay_us
                0x***********00cea       0x32 ./Driver/SysTickDelay.o
                0x***********00cea                Delay_us
 .text.Delay_ms
                0x***********00d1c       0x3e ./Driver/SysTickDelay.o
                0x***********00d1c                Delay_ms
 .text.BrewingControl_Init
                0x***********00d5a       0x20 ./Driver/brewing_control.o
                0x***********00d5a                BrewingControl_Init
 .text.BrewingControl_Start
                0x***********00d7a       0x22 ./Driver/brewing_control.o
                0x***********00d7a                BrewingControl_Start
 .text.BrewingControl_KeyHandler
                0x***********00d9c       0xb4 ./Driver/brewing_control.o
                0x***********00d9c                BrewingControl_KeyHandler
 .text.BrewingControl_Task
                0x***********00e50      0x134 ./Driver/brewing_control.o
                0x***********00e50                BrewingControl_Task
 .text.DS18B20_IO_OUT
                0x***********00f84       0x26 ./Driver/ds18b20.o
                0x***********00f84                DS18B20_IO_OUT
 .text.DS18B20_IO_IN
                0x***********00faa       0x24 ./Driver/ds18b20.o
                0x***********00faa                DS18B20_IO_IN
 .text.DS18B20_Reset
                0x***********00fce       0x30 ./Driver/ds18b20.o
                0x***********00fce                DS18B20_Reset
 .text.DS18B20_Check
                0x***********00ffe       0x54 ./Driver/ds18b20.o
                0x***********00ffe                DS18B20_Check
 .text.DS18B20_WriteBit
                0x***********01052       0x44 ./Driver/ds18b20.o
                0x***********01052                DS18B20_WriteBit
 .text.DS18B20_ReadBit
                0x***********01096       0x40 ./Driver/ds18b20.o
                0x***********01096                DS18B20_ReadBit
 .text.DS18B20_WriteByte
                0x***********010d6       0x1c ./Driver/ds18b20.o
                0x***********010d6                DS18B20_WriteByte
 .text.DS18B20_ReadByte
                0x***********010f2       0x24 ./Driver/ds18b20.o
                0x***********010f2                DS18B20_ReadByte
 .text.DS18B20_Init
                0x***********01116       0x4a ./Driver/ds18b20.o
                0x***********01116                DS18B20_Init
 .text.DS18B20_StartConvert
                0x***********01160       0x20 ./Driver/ds18b20.o
                0x***********01160                DS18B20_StartConvert
 .text.DS18B20_ReadTempRaw
                0x***********01180       0x4c ./Driver/ds18b20.o
                0x***********01180                DS18B20_ReadTempRaw
 .text.DS18B20_SetCallback
                0x***********011cc        0x4 ./Driver/ds18b20.o
                0x***********011cc                DS18B20_SetCallback
 .text.DS18B20_ReadRealtimeTemp
                0x***********011d0       0x48 ./Driver/ds18b20.o
                0x***********011d0                DS18B20_ReadRealtimeTemp
 .text.Heater_Start
                0x***********01218       0x16 ./Driver/heater.o
                0x***********01218                Heater_Start
 .text.Heater_Stop
                0x***********0122e       0x16 ./Driver/heater.o
                0x***********0122e                Heater_Stop
 .text.Heater_Init
                0x***********01244       0x36 ./Driver/heater.o
                0x***********01244                Heater_Init
 .text.key_init
                0x***********0127a       0x92 ./Driver/key.o
                0x***********0127a                key_init
 .text.key_read
                0x***********0130c      0x200 ./Driver/key.o
                0x***********0130c                key_read
 .text.SPI_LCD_Init
                0x***********0150c       0xdc ./Driver/lcd.o
                0x***********0150c                SPI_LCD_Init
 .text.SPI3_IRQHandler
                0x***********015e8        0x4 ./Driver/lcd.o
                0x***********015e8                SPI3_IRQHandler
 .text.spi_readwrite
                0x***********015ec       0x62 ./Driver/lcd.o
                0x***********015ec                spi_readwrite
 .text.LCD_WR_DATA8
                0x***********0164e       0x30 ./Driver/lcd.o
                0x***********0164e                LCD_WR_DATA8
 .text.LCD_WR_DATA
                0x***********0167e       0x38 ./Driver/lcd.o
                0x***********0167e                LCD_WR_DATA
 .text.LCD_WR_REG
                0x***********016b6       0x30 ./Driver/lcd.o
                0x***********016b6                LCD_WR_REG
 .text.LCD_Address_Set
                0x***********016e6       0x54 ./Driver/lcd.o
                0x***********016e6                LCD_Address_Set
 .text.LCD_Init
                0x***********0173a      0x206 ./Driver/lcd.o
                0x***********0173a                LCD_Init
 .text.LCD_Fill
                0x***********01940       0x42 ./Driver/lcd.o
                0x***********01940                LCD_Fill
 .text.TIM2_PWM_Init
                0x***********01982       0x8a ./Driver/timer.o
                0x***********01982                TIM2_PWM_Init
 .text.stir_360
                0x***********01a0c       0x5e ./Driver/timer.o
                0x***********01a0c                stir_360
 .text.stir     0x***********01a6a       0x20 ./Driver/timer.o
                0x***********01a6a                stir
 .text.WaterLevel_Init
                0x***********01a8a       0x32 ./Driver/water_level.o
                0x***********01a8a                WaterLevel_Init
 .text.WaterLevel_Detect
                0x***********01abc       0x16 ./Driver/water_level.o
                0x***********01abc                WaterLevel_Detect
 .text.WaterPump_Init
                0x***********01ad2       0x82 ./Driver/water_pump.o
                0x***********01ad2                WaterPump_Init
 .text.WaterPump_Control
                0x***********01b54       0x58 ./Driver/water_pump.o
                0x***********01b54                WaterPump_Control
 .text.Delay_Init
                0x***********01bac       0x2a ./Debug/debug.o
                0x***********01bac                Delay_Init
 .text.Delay_Ms
                0x***********01bd6       0x36 ./Debug/debug.o
                0x***********01bd6                Delay_Ms
 .text.USART_Printf_Init
                0x***********01c0c       0x5a ./Debug/debug.o
                0x***********01c0c                USART_Printf_Init
 .text._write   0x***********01c66       0x3e ./Debug/debug.o
                0x***********01c66                _write
 .text._sbrk    0x***********01ca4       0x26 ./Debug/debug.o
                0x***********01ca4                _sbrk
 .text.__gesf2  0x***********01cca       0x86 d:/mounriver/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/rv32imacxw/ilp32\libgcc.a(gesf2.o)
                0x***********01cca                __gesf2
                0x***********01cca                __gtsf2
 .text.__lesf2  0x***********01d50       0x8c d:/mounriver/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/rv32imacxw/ilp32\libgcc.a(lesf2.o)
                0x***********01d50                __ltsf2
                0x***********01d50                __lesf2
 .text.__mulsf3
                0x***********01ddc      0x290 d:/mounriver/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/rv32imacxw/ilp32\libgcc.a(mulsf3.o)
                0x***********01ddc                __mulsf3
 .text.__subsf3
                0x***********0206c      0x388 d:/mounriver/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/rv32imacxw/ilp32\libgcc.a(subsf3.o)
                0x***********0206c                __subsf3
 .text.__floatsisf
                0x***********023f4       0xc6 d:/mounriver/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/rv32imacxw/ilp32\libgcc.a(floatsisf.o)
                0x***********023f4                __floatsisf
 .text.__extendsfdf2
                0x***********024ba       0xac d:/mounriver/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/rv32imacxw/ilp32\libgcc.a(extendsfdf2.o)
                0x***********024ba                __extendsfdf2
 .text.__clzsi2
                0x***********02566       0x6e d:/mounriver/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/rv32imacxw/ilp32\libgcc.a(_clzsi2.o)
                0x***********02566                __clzsi2
 .text.printf   0x***********025d4       0x40 d:/mounriver/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libg_nano.a(lib_a-printf.o)
                0x***********025d4                iprintf
                0x***********025d4                printf
 .text._puts_r  0x***********02614       0xd4 d:/mounriver/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libg_nano.a(lib_a-puts.o)
                0x***********02614                _puts_r
 .text.puts     0x***********026e8        0xc d:/mounriver/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libg_nano.a(lib_a-puts.o)
                0x***********026e8                puts
 .text.__swbuf_r
                0x***********026f4       0xbc d:/mounriver/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libg_nano.a(lib_a-wbuf.o)
                0x***********026f4                __swbuf_r
 .text.__swsetup_r
                0x***********027b0       0xfc d:/mounriver/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libg_nano.a(lib_a-wsetup.o)
                0x***********027b0                __swsetup_r
 .text.__sflush_r
                0x***********028ac      0x130 d:/mounriver/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libg_nano.a(lib_a-fflush.o)
                0x***********028ac                __sflush_r
 .text._fflush_r
                0x***********029dc       0x66 d:/mounriver/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libg_nano.a(lib_a-fflush.o)
                0x***********029dc                _fflush_r
 .text.std      0x***********02a42       0x66 d:/mounriver/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libg_nano.a(lib_a-findfp.o)
 .text._cleanup_r
                0x***********02aa8        0xa d:/mounriver/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libg_nano.a(lib_a-findfp.o)
                0x***********02aa8                _cleanup_r
 .text.__sfmoreglue
                0x***********02ab2       0x46 d:/mounriver/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libg_nano.a(lib_a-findfp.o)
                0x***********02ab2                __sfmoreglue
 .text.__sinit  0x***********02af8       0x6c d:/mounriver/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libg_nano.a(lib_a-findfp.o)
                0x***********02af8                __sinit
 .text.__sfp    0x***********02b64       0xa0 d:/mounriver/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libg_nano.a(lib_a-findfp.o)
                0x***********02b64                __sfp
 .text._fwalk_reent
                0x***********02c04       0x6a d:/mounriver/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libg_nano.a(lib_a-fwalk.o)
                0x***********02c04                _fwalk_reent
 .text.__swhatbuf_r
                0x***********02c6e       0x58 d:/mounriver/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libg_nano.a(lib_a-makebuf.o)
                0x***********02c6e                __swhatbuf_r
 .text.__smakebuf_r
                0x***********02cc6       0x92 d:/mounriver/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libg_nano.a(lib_a-makebuf.o)
                0x***********02cc6                __smakebuf_r
 .text._free_r  0x***********02d58       0xa8 d:/mounriver/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libg_nano.a(lib_a-nano-freer.o)
                0x***********02d58                _free_r
 .text._malloc_r
                0x***********02e00       0xd4 d:/mounriver/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libg_nano.a(lib_a-nano-mallocr.o)
                0x***********02e00                _malloc_r
 .text.__sfputc_r
                0x***********02ed4       0x28 d:/mounriver/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libg_nano.a(lib_a-nano-vfprintf.o)
 .text.__sfputs_r
                0x***********02efc       0x42 d:/mounriver/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libg_nano.a(lib_a-nano-vfprintf.o)
                0x***********02efc                __sfputs_r
 .text._vfprintf_r
                0x***********02f3e      0x288 d:/mounriver/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libg_nano.a(lib_a-nano-vfprintf.o)
                0x***********02f3e                _vfprintf_r
                0x***********02f3e                _vfiprintf_r
 .text._printf_common
                0x***********031c6      0x10c d:/mounriver/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libg_nano.a(lib_a-nano-vfprintf_i.o)
                0x***********031c6                _printf_common
 .text._printf_i
                0x***********032d2      0x2a2 d:/mounriver/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libg_nano.a(lib_a-nano-vfprintf_i.o)
                0x***********032d2                _printf_i
 .text._sbrk_r  0x***********03574       0x2a d:/mounriver/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libg_nano.a(lib_a-sbrkr.o)
                0x***********03574                _sbrk_r
 .text.__sread  0x***********0359e       0x2c d:/mounriver/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libg_nano.a(lib_a-stdio.o)
                0x***********0359e                __sread
 .text.__swrite
                0x***********035ca       0x48 d:/mounriver/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libg_nano.a(lib_a-stdio.o)
                0x***********035ca                __swrite
 .text.__sseek  0x***********03612       0x30 d:/mounriver/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libg_nano.a(lib_a-stdio.o)
                0x***********03612                __sseek
 .text.__sclose
                0x***********03642        0x6 d:/mounriver/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libg_nano.a(lib_a-stdio.o)
                0x***********03642                __sclose
 .text._write_r
                0x***********03648       0x2e d:/mounriver/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libg_nano.a(lib_a-writer.o)
                0x***********03648                _write_r
 .text._close_r
                0x***********03676       0x28 d:/mounriver/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libg_nano.a(lib_a-closer.o)
                0x***********03676                _close_r
 .text._fstat_r
                0x***********0369e       0x2a d:/mounriver/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libg_nano.a(lib_a-fstatr.o)
                0x***********0369e                _fstat_r
 .text._isatty_r
                0x***********036c8       0x28 d:/mounriver/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libg_nano.a(lib_a-isattyr.o)
                0x***********036c8                _isatty_r
 .text._lseek_r
                0x***********036f0       0x2c d:/mounriver/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libg_nano.a(lib_a-lseekr.o)
                0x***********036f0                _lseek_r
 .text.memchr   0x***********0371c       0x18 d:/mounriver/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libg_nano.a(lib_a-memchr.o)
                0x***********0371c                memchr
 .text.__malloc_lock
                0x***********03734        0x2 d:/mounriver/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libg_nano.a(lib_a-mlock.o)
                0x***********03734                __malloc_lock
 .text.__malloc_unlock
                0x***********03736        0x2 d:/mounriver/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libg_nano.a(lib_a-mlock.o)
                0x***********03736                __malloc_unlock
 .text._read_r  0x***********03738       0x2c d:/mounriver/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libg_nano.a(lib_a-readr.o)
                0x***********03738                _read_r
 .text._close   0x***********03764        0xc d:/mounriver/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libnosys.a(close.o)
                0x***********03764                _close
 .text._fstat   0x***********03770        0xc d:/mounriver/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libnosys.a(fstat.o)
                0x***********03770                _fstat
 .text._isatty  0x***********0377c        0xc d:/mounriver/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libnosys.a(isatty.o)
                0x***********0377c                _isatty
 .text._lseek   0x***********03788        0xc d:/mounriver/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libnosys.a(lseek.o)
                0x***********03788                _lseek
 .text._read    0x***********03794        0xc d:/mounriver/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libnosys.a(read.o)
                0x***********03794                _read
 *(.rodata)
 *(.rodata*)
 .rodata.TempAlarmHandler.str1.4
                0x***********037a0       0x21 ./User/main.o
 *fill*         0x***********037c1        0x3 
 .rodata.main.cst4
                0x***********037c4        0x4 ./User/main.o
 .rodata.main.str1.4
                0x***********037c8       0x4f ./User/main.o
 *fill*         0x***********03817        0x1 
 .rodata.BrewingControl_Task
                0x***********03818       0x20 ./Driver/brewing_control.o
 .rodata.BrewingControl_KeyHandler.str1.4
                0x***********03838       0x4b ./Driver/brewing_control.o
 *fill*         0x***********03883        0x1 
 .rodata.BrewingControl_Start.str1.4
                0x***********03884       0x20 ./Driver/brewing_control.o
 .rodata.BrewingControl_Task.cst4
                0x***********038a4        0x4 ./Driver/brewing_control.o
 .rodata.BrewingControl_Task.str1.4
                0x***********038a8       0xcb ./Driver/brewing_control.o
 *fill*         0x***********03973        0x1 
 .rodata.DS18B20_Init.str1.4
                0x***********03974       0x22 ./Driver/ds18b20.o
 *fill*         0x***********03996        0x2 
 .rodata.DS18B20_ReadRealtimeTemp.cst4
                0x***********03998        0x4 ./Driver/ds18b20.o
 .rodata.DS18B20_ReadTempRaw.cst4
                0x***********0399c        0x8 ./Driver/ds18b20.o
 .rodata.__mulsf3
                0x***********039a4       0x40 d:/mounriver/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/rv32imacxw/ilp32\libgcc.a(mulsf3.o)
 .rodata.__clz_tab
                0x***********039e4      0x100 d:/mounriver/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/rv32imacxw/ilp32\libgcc.a(_clz.o)
                0x***********039e4                __clz_tab
 .rodata.__sf_fake_stderr
                0x***********03ae4       0x20 d:/mounriver/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libg_nano.a(lib_a-findfp.o)
                0x***********03ae4                __sf_fake_stderr
 .rodata.__sf_fake_stdin
                0x***********03b04       0x20 d:/mounriver/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libg_nano.a(lib_a-findfp.o)
                0x***********03b04                __sf_fake_stdin
 .rodata.__sf_fake_stdout
                0x***********03b24       0x20 d:/mounriver/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libg_nano.a(lib_a-findfp.o)
                0x***********03b24                __sf_fake_stdout
 .rodata._vfprintf_r.str1.4
                0x***********03b44       0x13 d:/mounriver/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libg_nano.a(lib_a-nano-vfprintf.o)
 *fill*         0x***********03b57        0x1 
 .rodata._printf_i.str1.4
                0x***********03b58       0x28 d:/mounriver/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libg_nano.a(lib_a-nano-vfprintf_i.o)
                                         0x25 (size before relaxing)
 *(.gnu.linkonce.t.*)
                0x***********03b80                . = ALIGN (0x4)

.rela.dyn       0x***********03b80        0x0
 .rela.init     0x***********03b80        0x0 ./User/ch32v30x_it.o
 .rela.vector   0x***********03b80        0x0 ./User/ch32v30x_it.o
 .rela.text.handle_reset
                0x***********03b80        0x0 ./User/ch32v30x_it.o
 .rela.text._sbrk
                0x***********03b80        0x0 ./User/ch32v30x_it.o
 .rela.sdata.curbrk.5274
                0x***********03b80        0x0 ./User/ch32v30x_it.o
 .rela.text._vfprintf_r
                0x***********03b80        0x0 ./User/ch32v30x_it.o
 .rela.text._sbrk_r
                0x***********03b80        0x0 ./User/ch32v30x_it.o
 .rela.text._write_r
                0x***********03b80        0x0 ./User/ch32v30x_it.o
 .rela.text._close_r
                0x***********03b80        0x0 ./User/ch32v30x_it.o
 .rela.text._fstat_r
                0x***********03b80        0x0 ./User/ch32v30x_it.o
 .rela.text._isatty_r
                0x***********03b80        0x0 ./User/ch32v30x_it.o
 .rela.text._lseek_r
                0x***********03b80        0x0 ./User/ch32v30x_it.o
 .rela.text._read_r
                0x***********03b80        0x0 ./User/ch32v30x_it.o
 .rela.text._close
                0x***********03b80        0x0 ./User/ch32v30x_it.o
 .rela.text._fstat
                0x***********03b80        0x0 ./User/ch32v30x_it.o
 .rela.text._isatty
                0x***********03b80        0x0 ./User/ch32v30x_it.o
 .rela.text._lseek
                0x***********03b80        0x0 ./User/ch32v30x_it.o
 .rela.text._read
                0x***********03b80        0x0 ./User/ch32v30x_it.o

.fini           0x***********03b80        0x0
 *(SORT_NONE(.fini))
                0x***********03b80                . = ALIGN (0x4)
                [!provide]                        PROVIDE (_etext = .)
                [!provide]                        PROVIDE (_eitcm = .)

.preinit_array  0x***********03b80        0x0
                [!provide]                        PROVIDE (__preinit_array_start = .)
 *(.preinit_array)
                [!provide]                        PROVIDE (__preinit_array_end = .)

.init_array     0x***********03b80        0x0
                [!provide]                        PROVIDE (__init_array_start = .)
 *(SORT_BY_INIT_PRIORITY(.init_array.*) SORT_BY_INIT_PRIORITY(.ctors.*))
 *(.init_array EXCLUDE_FILE(*crtend?.o *crtend.o *crtbegin?.o *crtbegin.o) .ctors)
                [!provide]                        PROVIDE (__init_array_end = .)

.fini_array     0x***********03b80        0x0
                [!provide]                        PROVIDE (__fini_array_start = .)
 *(SORT_BY_INIT_PRIORITY(.fini_array.*) SORT_BY_INIT_PRIORITY(.dtors.*))
 *(.fini_array EXCLUDE_FILE(*crtend?.o *crtend.o *crtbegin?.o *crtbegin.o) .dtors)
                [!provide]                        PROVIDE (__fini_array_end = .)

.ctors
 *crtbegin.o(.ctors)
 *crtbegin?.o(.ctors)
 *(EXCLUDE_FILE(*crtend?.o *crtend.o) .ctors)
 *(SORT_BY_NAME(.ctors.*))
 *(.ctors)

.dtors
 *crtbegin.o(.dtors)
 *crtbegin?.o(.dtors)
 *(EXCLUDE_FILE(*crtend?.o *crtend.o) .dtors)
 *(SORT_BY_NAME(.dtors.*))
 *(.dtors)

.dalign         0x0000000020000000        0x0 load address 0x***********03b80
                0x0000000020000000                . = ALIGN (0x4)
                0x0000000020000000                PROVIDE (_data_vma = .)

.dlalign        0x***********03b80        0x0
                0x***********03b80                . = ALIGN (0x4)
                0x***********03b80                PROVIDE (_data_lma = .)

.data           0x0000000020000000       0xc8 load address 0x***********03b80
 *(.gnu.linkonce.r.*)
 *(.data .data.*)
 .data.scheduler_task_t
                0x0000000020000000       0x30 ./User/main.o
                0x0000000020000000                scheduler_task_t
 .data.AHBPrescTable
                0x0000000020000030       0x10 ./User/system_ch32v30x.o
                0x0000000020000030                AHBPrescTable
 .data.APBAHBPrescTable
                0x0000000020000040       0x10 ./Peripheral/src/ch32v30x_rcc.o
 .data.impure_data
                0x0000000020000050       0x60 d:/mounriver/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libg_nano.a(lib_a-impure.o)
 *(.gnu.linkonce.d.*)
                0x00000000200000b0                . = ALIGN (0x8)
                0x00000000200008b0                PROVIDE (__global_pointer$ = (. + 0x800))
 *(.sdata .sdata.*)
 .sdata.SystemCoreClock
                0x00000000200000b0        0x4 ./User/system_ch32v30x.o
                0x00000000200000b0                SystemCoreClock
 .sdata.ADCPrescTable
                0x00000000200000b4        0x4 ./Peripheral/src/ch32v30x_rcc.o
 .sdata.curbrk.5274
                0x00000000200000b8        0x4 ./Debug/debug.o
 .sdata._impure_ptr
                0x00000000200000bc        0x4 d:/mounriver/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libg_nano.a(lib_a-impure.o)
                0x00000000200000bc                _impure_ptr
 *(.sdata2.*)
 .sdata2._global_impure_ptr
                0x00000000200000c0        0x4 d:/mounriver/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libg_nano.a(lib_a-impure.o)
                0x00000000200000c0                _global_impure_ptr
 *(.gnu.linkonce.s.*)
                0x00000000200000c8                . = ALIGN (0x8)
 *fill*         0x00000000200000c4        0x4 
 *(.srodata.cst16)
 *(.srodata.cst8)
 *(.srodata.cst4)
 *(.srodata.cst2)
 *(.srodata .srodata.*)
                0x00000000200000c8                . = ALIGN (0x4)
                0x00000000200000c8                PROVIDE (_edata = .)

.bss            0x00000000200000c8       0x6c load address 0x***********03c48
                0x00000000200000c8                . = ALIGN (0x4)
                0x00000000200000c8                PROVIDE (_sbss = .)
 *(.sbss*)
 .sbss.key_down
                0x00000000200000c8        0x1 ./User/main.o
                0x00000000200000c8                key_down
 .sbss.key_old  0x00000000200000c9        0x1 ./User/main.o
                0x00000000200000c9                key_old
 .sbss.key_up   0x00000000200000ca        0x1 ./User/main.o
                0x00000000200000ca                key_up
 .sbss.key_val  0x00000000200000cb        0x1 ./User/main.o
                0x00000000200000cb                key_val
 .sbss.p        0x00000000200000cc        0x1 ./User/main.o
                0x00000000200000cc                p
 .sbss.task_num
                0x00000000200000cd        0x1 ./User/main.o
                0x00000000200000cd                task_num
 *fill*         0x00000000200000ce        0x2 
 .sbss.uwtick   0x00000000200000d0        0x4 ./User/main.o
                0x00000000200000d0                uwtick
 .sbss.NVIC_Priority_Group
                0x00000000200000d4        0x4 ./Peripheral/src/ch32v30x_misc.o
                0x00000000200000d4                NVIC_Priority_Group
 .sbss.fac_ms   0x00000000200000d8        0x2 ./Driver/SysTickDelay.o
 .sbss.fac_us   0x00000000200000da        0x1 ./Driver/SysTickDelay.o
 *fill*         0x00000000200000db        0x1 
 .sbss.p_ms     0x00000000200000dc        0x2 ./Debug/debug.o
 .sbss.p_us     0x00000000200000de        0x1 ./Debug/debug.o
 *fill*         0x00000000200000df        0x1 
 .sbss.__malloc_free_list
                0x00000000200000e0        0x4 d:/mounriver/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libg_nano.a(lib_a-nano-mallocr.o)
                0x00000000200000e0                __malloc_free_list
 .sbss.__malloc_sbrk_start
                0x00000000200000e4        0x4 d:/mounriver/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libg_nano.a(lib_a-nano-mallocr.o)
                0x00000000200000e4                __malloc_sbrk_start
 *(.gnu.linkonce.sb.*)
 *(.bss*)
 .bss.ds18b20   0x00000000200000e8       0x10 ./User/main.o
                0x00000000200000e8                ds18b20
 .bss.g_brewing_ctrl
                0x00000000200000f8       0x18 ./Driver/brewing_control.o
                0x00000000200000f8                g_brewing_ctrl
 .bss.g_pump_ctrl
                0x0000000020000110       0x20 ./Driver/water_pump.o
                0x0000000020000110                g_pump_ctrl
 *(.gnu.linkonce.b.*)
 *(COMMON*)
 COMMON         0x0000000020000130        0x4 d:/mounriver/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libg_nano.a(lib_a-reent.o)
                0x0000000020000130                errno
                0x0000000020000134                . = ALIGN (0x4)
                0x0000000020000134                PROVIDE (_ebss = .)
                0x0000000020000134                PROVIDE (_end = _ebss)
                [!provide]                        PROVIDE (end = .)

.stack          0x0000000020007800      0x800
                0x0000000020007800                PROVIDE (_heap_end = .)
                0x0000000020007800                . = ALIGN (0x4)
                [!provide]                        PROVIDE (_susrstack = .)
                0x0000000020008000                . = (. + __stack_size)
 *fill*         0x0000000020007800      0x800 
                0x0000000020008000                PROVIDE (_eusrstack = .)
OUTPUT(ds18b20.elf elf32-littleriscv)

.debug_info     0x***********00000    0x164c7
 .debug_info    0x***********00000      0xbc0 ./User/ch32v30x_it.o
 .debug_info    0x***********00bc0     0x1839 ./User/main.o
 .debug_info    0x***********023f9      0xbb5 ./User/system_ch32v30x.o
 .debug_info    0x***********02fae       0x22 ./Startup/startup_ch32v30x_D8C.o
 .debug_info    0x***********02fd0     0x12aa ./Peripheral/src/ch32v30x_gpio.o
 .debug_info    0x***********0427a      0xf60 ./Peripheral/src/ch32v30x_misc.o
 .debug_info    0x***********051da     0x158a ./Peripheral/src/ch32v30x_rcc.o
 .debug_info    0x***********06764     0x12cd ./Peripheral/src/ch32v30x_spi.o
 .debug_info    0x***********07a31     0x2a32 ./Peripheral/src/ch32v30x_tim.o
 .debug_info    0x***********0a463     0x13cd ./Peripheral/src/ch32v30x_usart.o
 .debug_info    0x***********0b830      0xa95 ./Driver/SysTickDelay.o
 .debug_info    0x***********0c2c5      0xe71 ./Driver/brewing_control.o
 .debug_info    0x***********0d136     0x1228 ./Driver/ds18b20.o
 .debug_info    0x***********0e35e      0xb98 ./Driver/heater.o
 .debug_info    0x***********0eef6      0xfa3 ./Driver/key.o
 .debug_info    0x***********0fe99     0x2b70 ./Driver/lcd.o
 .debug_info    0x***********12a09     0x108f ./Driver/timer.o
 .debug_info    0x***********13a98      0xb87 ./Driver/water_level.o
 .debug_info    0x***********1461f      0xf5e ./Driver/water_pump.o
 .debug_info    0x***********1557d      0xf4a ./Debug/debug.o

.debug_abbrev   0x***********00000     0x3abe
 .debug_abbrev  0x***********00000      0x22c ./User/ch32v30x_it.o
 .debug_abbrev  0x***********0022c      0x3e5 ./User/main.o
 .debug_abbrev  0x***********00611      0x2c0 ./User/system_ch32v30x.o
 .debug_abbrev  0x***********008d1       0x12 ./Startup/startup_ch32v30x_D8C.o
 .debug_abbrev  0x***********008e3      0x31a ./Peripheral/src/ch32v30x_gpio.o
 .debug_abbrev  0x***********00bfd      0x2e3 ./Peripheral/src/ch32v30x_misc.o
 .debug_abbrev  0x***********00ee0      0x351 ./Peripheral/src/ch32v30x_rcc.o
 .debug_abbrev  0x***********01231      0x312 ./Peripheral/src/ch32v30x_spi.o
 .debug_abbrev  0x***********01543      0x408 ./Peripheral/src/ch32v30x_tim.o
 .debug_abbrev  0x***********0194b      0x312 ./Peripheral/src/ch32v30x_usart.o
 .debug_abbrev  0x***********01c5d      0x214 ./Driver/SysTickDelay.o
 .debug_abbrev  0x***********01e71      0x2d4 ./Driver/brewing_control.o
 .debug_abbrev  0x***********02145      0x3f3 ./Driver/ds18b20.o
 .debug_abbrev  0x***********02538      0x26b ./Driver/heater.o
 .debug_abbrev  0x***********027a3      0x29a ./Driver/key.o
 .debug_abbrev  0x***********02a3d      0x436 ./Driver/lcd.o
 .debug_abbrev  0x***********02e73      0x305 ./Driver/timer.o
 .debug_abbrev  0x***********03178      0x27e ./Driver/water_level.o
 .debug_abbrev  0x***********033f6      0x3d3 ./Driver/water_pump.o
 .debug_abbrev  0x***********037c9      0x2f5 ./Debug/debug.o

.debug_aranges  0x***********00000      0xb40
 .debug_aranges
                0x***********00000       0x28 ./User/ch32v30x_it.o
 .debug_aranges
                0x***********00028       0x68 ./User/main.o
 .debug_aranges
                0x***********00090       0x28 ./User/system_ch32v30x.o
 .debug_aranges
                0x***********000b8       0x30 ./Startup/startup_ch32v30x_D8C.o
 .debug_aranges
                0x***********000e8       0xb0 ./Peripheral/src/ch32v30x_gpio.o
 .debug_aranges
                0x***********00198       0x28 ./Peripheral/src/ch32v30x_misc.o
 .debug_aranges
                0x***********001c0      0x1a8 ./Peripheral/src/ch32v30x_rcc.o
 .debug_aranges
                0x***********00368       0xd0 ./Peripheral/src/ch32v30x_spi.o
 .debug_aranges
                0x***********00438      0x2d0 ./Peripheral/src/ch32v30x_tim.o
 .debug_aranges
                0x***********00708       0xf0 ./Peripheral/src/ch32v30x_usart.o
 .debug_aranges
                0x***********007f8       0x30 ./Driver/SysTickDelay.o
 .debug_aranges
                0x***********00828       0x58 ./Driver/brewing_control.o
 .debug_aranges
                0x***********00880       0x90 ./Driver/ds18b20.o
 .debug_aranges
                0x***********00910       0x30 ./Driver/heater.o
 .debug_aranges
                0x***********00940       0x28 ./Driver/key.o
 .debug_aranges
                0x***********00968       0xd8 ./Driver/lcd.o
 .debug_aranges
                0x***********00a40       0x30 ./Driver/timer.o
 .debug_aranges
                0x***********00a70       0x28 ./Driver/water_level.o
 .debug_aranges
                0x***********00a98       0x58 ./Driver/water_pump.o
 .debug_aranges
                0x***********00af0       0x50 ./Debug/debug.o

.debug_ranges   0x***********00000      0xb80
 .debug_ranges  0x***********00000       0x18 ./User/ch32v30x_it.o
 .debug_ranges  0x***********00018       0x98 ./User/main.o
 .debug_ranges  0x***********000b0       0x38 ./User/system_ch32v30x.o
 .debug_ranges  0x***********000e8       0x28 ./Startup/startup_ch32v30x_D8C.o
 .debug_ranges  0x***********00110       0xa0 ./Peripheral/src/ch32v30x_gpio.o
 .debug_ranges  0x***********001b0       0x48 ./Peripheral/src/ch32v30x_misc.o
 .debug_ranges  0x***********001f8      0x198 ./Peripheral/src/ch32v30x_rcc.o
 .debug_ranges  0x***********00390       0xc0 ./Peripheral/src/ch32v30x_spi.o
 .debug_ranges  0x***********00450      0x2f0 ./Peripheral/src/ch32v30x_tim.o
 .debug_ranges  0x***********00740       0xe0 ./Peripheral/src/ch32v30x_usart.o
 .debug_ranges  0x***********00820       0x20 ./Driver/SysTickDelay.o
 .debug_ranges  0x***********00840       0x48 ./Driver/brewing_control.o
 .debug_ranges  0x***********00888       0xa0 ./Driver/ds18b20.o
 .debug_ranges  0x***********00928       0x20 ./Driver/heater.o
 .debug_ranges  0x***********00948       0x18 ./Driver/key.o
 .debug_ranges  0x***********00960      0x130 ./Driver/lcd.o
 .debug_ranges  0x***********00a90       0x20 ./Driver/timer.o
 .debug_ranges  0x***********00ab0       0x18 ./Driver/water_level.o
 .debug_ranges  0x***********00ac8       0x78 ./Driver/water_pump.o
 .debug_ranges  0x***********00b40       0x40 ./Debug/debug.o

.debug_line     0x***********00000     0xe260
 .debug_line    0x***********00000      0x262 ./User/ch32v30x_it.o
 .debug_line    0x***********00262      0x856 ./User/main.o
 .debug_line    0x***********00ab8      0x88c ./User/system_ch32v30x.o
 .debug_line    0x***********01344      0x122 ./Startup/startup_ch32v30x_D8C.o
 .debug_line    0x***********01466     0x11f5 ./Peripheral/src/ch32v30x_gpio.o
 .debug_line    0x***********0265b      0x36c ./Peripheral/src/ch32v30x_misc.o
 .debug_line    0x***********029c7     0x1927 ./Peripheral/src/ch32v30x_rcc.o
 .debug_line    0x***********042ee      0xcce ./Peripheral/src/ch32v30x_spi.o
 .debug_line    0x***********04fbc     0x3294 ./Peripheral/src/ch32v30x_tim.o
 .debug_line    0x***********08250      0xe94 ./Peripheral/src/ch32v30x_usart.o
 .debug_line    0x***********090e4      0x354 ./Driver/SysTickDelay.o
 .debug_line    0x***********09438      0x85a ./Driver/brewing_control.o
 .debug_line    0x***********09c92      0x96d ./Driver/ds18b20.o
 .debug_line    0x***********0a5ff      0x330 ./Driver/heater.o
 .debug_line    0x***********0a92f      0x6be ./Driver/key.o
 .debug_line    0x***********0afed     0x1bb7 ./Driver/lcd.o
 .debug_line    0x***********0cba4      0x4c9 ./Driver/timer.o
 .debug_line    0x***********0d06d      0x30c ./Driver/water_level.o
 .debug_line    0x***********0d379      0x80a ./Driver/water_pump.o
 .debug_line    0x***********0db83      0x6dd ./Debug/debug.o

.debug_str      0x***********00000     0x356e
 .debug_str     0x***********00000      0x5ed ./User/ch32v30x_it.o
                                        0x661 (size before relaxing)
 .debug_str     0x***********005ed      0xcba ./User/main.o
                                       0x12bb (size before relaxing)
 .debug_str     0x***********012a7      0x109 ./User/system_ch32v30x.o
                                        0x6c6 (size before relaxing)
 .debug_str     0x***********013b0       0x2e ./Startup/startup_ch32v30x_D8C.o
                                         0x46 (size before relaxing)
 .debug_str     0x***********013de      0x3df ./Peripheral/src/ch32v30x_gpio.o
                                        0x98c (size before relaxing)
 .debug_str     0x***********017bd       0x9c ./Peripheral/src/ch32v30x_misc.o
                                        0xc95 (size before relaxing)
 .debug_str     0x***********01859      0x5ab ./Peripheral/src/ch32v30x_rcc.o
                                        0xcbb (size before relaxing)
 .debug_str     0x***********01e04      0x366 ./Peripheral/src/ch32v30x_spi.o
                                        0xa6b (size before relaxing)
 .debug_str     0x***********0216a      0x9dc ./Peripheral/src/ch32v30x_tim.o
                                       0x1262 (size before relaxing)
 .debug_str     0x***********02b46      0x3e2 ./Peripheral/src/ch32v30x_usart.o
                                        0xad6 (size before relaxing)
 .debug_str     0x***********02f28       0x6a ./Driver/SysTickDelay.o
                                        0x5fc (size before relaxing)
 .debug_str     0x***********02f92       0xf4 ./Driver/brewing_control.o
                                        0x973 (size before relaxing)
 .debug_str     0x***********03086       0xf4 ./Driver/ds18b20.o
                                        0x8e0 (size before relaxing)
 .debug_str     0x***********0317a       0x13 ./Driver/heater.o
                                        0x73d (size before relaxing)
 .debug_str     0x***********0318d       0x10 ./Driver/key.o
                                        0x754 (size before relaxing)
 .debug_str     0x***********0319d      0x235 ./Driver/lcd.o
                                        0xb35 (size before relaxing)
 .debug_str     0x***********033d2       0x52 ./Driver/timer.o
                                        0xa3b (size before relaxing)
 .debug_str     0x***********03424       0x18 ./Driver/water_level.o
                                        0x75d (size before relaxing)
 .debug_str     0x***********0343c       0xb9 ./Driver/water_pump.o
                                        0x8a6 (size before relaxing)
 .debug_str     0x***********034f5       0x79 ./Debug/debug.o
                                        0x8fd (size before relaxing)

.comment        0x***********00000       0x33
 .comment       0x***********00000       0x33 ./User/ch32v30x_it.o
                                         0x34 (size before relaxing)
 .comment       0x***********00033       0x34 ./User/main.o
 .comment       0x***********00033       0x34 ./User/system_ch32v30x.o
 .comment       0x***********00033       0x34 ./Peripheral/src/ch32v30x_gpio.o
 .comment       0x***********00033       0x34 ./Peripheral/src/ch32v30x_misc.o
 .comment       0x***********00033       0x34 ./Peripheral/src/ch32v30x_rcc.o
 .comment       0x***********00033       0x34 ./Peripheral/src/ch32v30x_spi.o
 .comment       0x***********00033       0x34 ./Peripheral/src/ch32v30x_tim.o
 .comment       0x***********00033       0x34 ./Peripheral/src/ch32v30x_usart.o
 .comment       0x***********00033       0x34 ./Driver/SysTickDelay.o
 .comment       0x***********00033       0x34 ./Driver/brewing_control.o
 .comment       0x***********00033       0x34 ./Driver/ds18b20.o
 .comment       0x***********00033       0x34 ./Driver/heater.o
 .comment       0x***********00033       0x34 ./Driver/key.o
 .comment       0x***********00033       0x34 ./Driver/lcd.o
 .comment       0x***********00033       0x34 ./Driver/timer.o
 .comment       0x***********00033       0x34 ./Driver/water_level.o
 .comment       0x***********00033       0x34 ./Driver/water_pump.o
 .comment       0x***********00033       0x34 ./Debug/debug.o

.debug_frame    0x***********00000     0x2558
 .debug_frame   0x***********00000       0x30 ./User/ch32v30x_it.o
 .debug_frame   0x***********00030      0x130 ./User/main.o
 .debug_frame   0x***********00160       0x3c ./User/system_ch32v30x.o
 .debug_frame   0x***********0019c      0x174 ./Peripheral/src/ch32v30x_gpio.o
 .debug_frame   0x***********00310       0x30 ./Peripheral/src/ch32v30x_misc.o
 .debug_frame   0x***********00340      0x344 ./Peripheral/src/ch32v30x_rcc.o
 .debug_frame   0x***********00684      0x1a8 ./Peripheral/src/ch32v30x_spi.o
 .debug_frame   0x***********0082c      0x614 ./Peripheral/src/ch32v30x_tim.o
 .debug_frame   0x***********00e40      0x1e4 ./Peripheral/src/ch32v30x_usart.o
 .debug_frame   0x***********01024       0x40 ./Driver/SysTickDelay.o
 .debug_frame   0x***********01064       0xe0 ./Driver/brewing_control.o
 .debug_frame   0x***********01144      0x1cc ./Driver/ds18b20.o
 .debug_frame   0x***********01310       0x68 ./Driver/heater.o
 .debug_frame   0x***********01378       0x60 ./Driver/key.o
 .debug_frame   0x***********013d8      0x4a0 ./Driver/lcd.o
 .debug_frame   0x***********01878       0x68 ./Driver/timer.o
 .debug_frame   0x***********018e0       0x4c ./Driver/water_level.o
 .debug_frame   0x***********0192c      0x104 ./Driver/water_pump.o
 .debug_frame   0x***********01a30       0xb8 ./Debug/debug.o
 .debug_frame   0x***********01ae8       0x20 d:/mounriver/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/rv32imacxw/ilp32\libgcc.a(gesf2.o)
 .debug_frame   0x***********01b08       0x20 d:/mounriver/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/rv32imacxw/ilp32\libgcc.a(lesf2.o)
 .debug_frame   0x***********01b28       0x50 d:/mounriver/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/rv32imacxw/ilp32\libgcc.a(mulsf3.o)
 .debug_frame   0x***********01b78       0x3c d:/mounriver/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/rv32imacxw/ilp32\libgcc.a(subsf3.o)
 .debug_frame   0x***********01bb4       0x38 d:/mounriver/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/rv32imacxw/ilp32\libgcc.a(floatsisf.o)
 .debug_frame   0x***********01bec       0x38 d:/mounriver/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/rv32imacxw/ilp32\libgcc.a(extendsfdf2.o)
 .debug_frame   0x***********01c24       0x20 d:/mounriver/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/rv32imacxw/ilp32\libgcc.a(_clzsi2.o)
 .debug_frame   0x***********01c44       0x54 d:/mounriver/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libg_nano.a(lib_a-printf.o)
 .debug_frame   0x***********01c98       0x54 d:/mounriver/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libg_nano.a(lib_a-puts.o)
 .debug_frame   0x***********01cec       0x50 d:/mounriver/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libg_nano.a(lib_a-wbuf.o)
 .debug_frame   0x***********01d3c       0x3c d:/mounriver/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libg_nano.a(lib_a-wsetup.o)
 .debug_frame   0x***********01d78       0x7c d:/mounriver/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libg_nano.a(lib_a-fflush.o)
 .debug_frame   0x***********01df4      0x148 d:/mounriver/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libg_nano.a(lib_a-findfp.o)
 .debug_frame   0x***********01f3c       0x88 d:/mounriver/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libg_nano.a(lib_a-fwalk.o)
 .debug_frame   0x***********01fc4       0x64 d:/mounriver/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libg_nano.a(lib_a-makebuf.o)
 .debug_frame   0x***********02028       0x40 d:/mounriver/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libg_nano.a(lib_a-nano-freer.o)
 .debug_frame   0x***********02068       0x40 d:/mounriver/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libg_nano.a(lib_a-nano-mallocr.o)
 .debug_frame   0x***********020a8       0xd0 d:/mounriver/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libg_nano.a(lib_a-nano-vfprintf.o)
 .debug_frame   0x***********02178       0x8c d:/mounriver/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libg_nano.a(lib_a-nano-vfprintf_i.o)
 .debug_frame   0x***********02204       0x30 d:/mounriver/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libg_nano.a(lib_a-sbrkr.o)
 .debug_frame   0x***********02234       0xa4 d:/mounriver/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libg_nano.a(lib_a-stdio.o)
 .debug_frame   0x***********022d8       0x30 d:/mounriver/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libg_nano.a(lib_a-writer.o)
 .debug_frame   0x***********02308       0x30 d:/mounriver/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libg_nano.a(lib_a-closer.o)
 .debug_frame   0x***********02338       0x30 d:/mounriver/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libg_nano.a(lib_a-fstatr.o)
 .debug_frame   0x***********02368       0x30 d:/mounriver/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libg_nano.a(lib_a-isattyr.o)
 .debug_frame   0x***********02398       0x30 d:/mounriver/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libg_nano.a(lib_a-lseekr.o)
 .debug_frame   0x***********023c8       0x20 d:/mounriver/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libg_nano.a(lib_a-memchr.o)
 .debug_frame   0x***********023e8       0x30 d:/mounriver/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libg_nano.a(lib_a-mlock.o)
 .debug_frame   0x***********02418       0x30 d:/mounriver/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libg_nano.a(lib_a-readr.o)
 .debug_frame   0x***********02448       0x70 d:/mounriver/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libg_nano.a(lib_a-reent.o)
 .debug_frame   0x***********024b8       0x20 d:/mounriver/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libnosys.a(close.o)
 .debug_frame   0x***********024d8       0x20 d:/mounriver/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libnosys.a(fstat.o)
 .debug_frame   0x***********024f8       0x20 d:/mounriver/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libnosys.a(isatty.o)
 .debug_frame   0x***********02518       0x20 d:/mounriver/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libnosys.a(lseek.o)
 .debug_frame   0x***********02538       0x20 d:/mounriver/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libnosys.a(read.o)

.debug_loc      0x***********00000     0x590b
 .debug_loc     0x***********00000       0xc0 ./User/main.o
 .debug_loc     0x***********000c0      0x163 ./User/system_ch32v30x.o
 .debug_loc     0x***********00223      0x72b ./Peripheral/src/ch32v30x_gpio.o
 .debug_loc     0x***********0094e       0x47 ./Peripheral/src/ch32v30x_misc.o
 .debug_loc     0x***********00995      0xadb ./Peripheral/src/ch32v30x_rcc.o
 .debug_loc     0x***********01470      0x598 ./Peripheral/src/ch32v30x_spi.o
 .debug_loc     0x***********01a08     0x195e ./Peripheral/src/ch32v30x_tim.o
 .debug_loc     0x***********03366      0x779 ./Peripheral/src/ch32v30x_usart.o
 .debug_loc     0x***********03adf       0x42 ./Driver/SysTickDelay.o
 .debug_loc     0x***********03b21       0xe7 ./Driver/brewing_control.o
 .debug_loc     0x***********03c08      0x1ee ./Driver/ds18b20.o
 .debug_loc     0x***********03df6       0x2e ./Driver/key.o
 .debug_loc     0x***********03e24     0x15c6 ./Driver/lcd.o
 .debug_loc     0x***********053ea      0x138 ./Driver/timer.o
 .debug_loc     0x***********05522      0x272 ./Driver/water_pump.o
 .debug_loc     0x***********05794      0x177 ./Debug/debug.o

.stab           0x***********00000       0x84
 .stab          0x***********00000       0x24 d:/mounriver/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libnosys.a(close.o)
 .stab          0x***********00024       0x18 d:/mounriver/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libnosys.a(fstat.o)
                                         0x24 (size before relaxing)
 .stab          0x***********0003c       0x18 d:/mounriver/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libnosys.a(isatty.o)
                                         0x24 (size before relaxing)
 .stab          0x***********00054       0x18 d:/mounriver/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libnosys.a(lseek.o)
                                         0x24 (size before relaxing)
 .stab          0x***********0006c       0x18 d:/mounriver/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libnosys.a(read.o)
                                         0x24 (size before relaxing)

.stabstr        0x***********00000      0x117
 .stabstr       0x***********00000      0x117 d:/mounriver/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libnosys.a(close.o)
