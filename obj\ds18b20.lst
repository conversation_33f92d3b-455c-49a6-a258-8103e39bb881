
ds18b20.elf:     file format elf32-littleriscv
ds18b20.elf
architecture: riscv:rv32, flags 0x00000112:
EXEC_P, HAS_SYMS, D_PAGED
start address 0x00000000

Program Header:
    LOAD off    0x00001000 vaddr 0x00000000 paddr 0x00000000 align 2**12
         filesz 0x00003b80 memsz 0x00003b80 flags r-x
    LOAD off    0x00005000 vaddr 0x20000000 paddr 0x00003b80 align 2**12
         filesz 0x000000c8 memsz 0x00000134 flags rw-
    LOAD off    0x00005800 vaddr 0x20007800 paddr 0x20007800 align 2**12
         filesz 0x00000000 memsz 0x00000800 flags rw-

Sections:
Idx Name          Size      VMA       LMA       File off  Algn
  0 .init         00000004  00000000  00000000  00001000  2**1
                  CONTENTS, ALLOC, LOAD, READONLY, CODE
  1 .vector       000001bc  00000004  00000004  00001004  2**1
                  CONTENTS, ALLOC, LOAD, READONLY, CODE
  2 .text         000039c0  000001c0  000001c0  000011c0  2**2
                  CONTENTS, ALLOC, LOAD, READONLY, CODE
  3 .fini         00000000  00003b80  00003b80  000050c8  2**0
                  CONTENTS, ALLOC, LOAD, CODE
  4 .dalign       00000000  20000000  20000000  000050c8  2**0
                  CONTENTS
  5 .dlalign      00000000  00003b80  00003b80  000050c8  2**0
                  CONTENTS
  6 .data         000000c8  20000000  00003b80  00005000  2**2
                  CONTENTS, ALLOC, LOAD, DATA
  7 .bss          0000006c  200000c8  00003c48  000050c8  2**2
                  ALLOC
  8 .stack        00000800  20007800  20007800  00005800  2**0
                  ALLOC
  9 .debug_info   000164c7  00000000  00000000  000050c8  2**0
                  CONTENTS, READONLY, DEBUGGING
 10 .debug_abbrev 00003abe  00000000  00000000  0001b58f  2**0
                  CONTENTS, READONLY, DEBUGGING
 11 .debug_aranges 00000b40  00000000  00000000  0001f050  2**3
                  CONTENTS, READONLY, DEBUGGING
 12 .debug_ranges 00000b80  00000000  00000000  0001fb90  2**3
                  CONTENTS, READONLY, DEBUGGING
 13 .debug_line   0000e260  00000000  00000000  00020710  2**0
                  CONTENTS, READONLY, DEBUGGING
 14 .debug_str    0000356e  00000000  00000000  0002e970  2**0
                  CONTENTS, READONLY, DEBUGGING
 15 .comment      00000033  00000000  00000000  00031ede  2**0
                  CONTENTS, READONLY
 16 .debug_frame  00002558  00000000  00000000  00031f14  2**2
                  CONTENTS, READONLY, DEBUGGING
 17 .debug_loc    0000590b  00000000  00000000  0003446c  2**0
                  CONTENTS, READONLY, DEBUGGING
 18 .stab         00000084  00000000  00000000  00039d78  2**2
                  CONTENTS, READONLY, DEBUGGING
 19 .stabstr      00000117  00000000  00000000  00039dfc  2**0
                  CONTENTS, READONLY, DEBUGGING
SYMBOL TABLE:
00000000 l    d  .init	00000000 .init
00000004 l    d  .vector	00000000 .vector
000001c0 l    d  .text	00000000 .text
00003b80 l    d  .fini	00000000 .fini
20000000 l    d  .dalign	00000000 .dalign
00003b80 l    d  .dlalign	00000000 .dlalign
20000000 l    d  .data	00000000 .data
200000c8 l    d  .bss	00000000 .bss
20007800 l    d  .stack	00000000 .stack
00000000 l    d  .debug_info	00000000 .debug_info
00000000 l    d  .debug_abbrev	00000000 .debug_abbrev
00000000 l    d  .debug_aranges	00000000 .debug_aranges
00000000 l    d  .debug_ranges	00000000 .debug_ranges
00000000 l    d  .debug_line	00000000 .debug_line
00000000 l    d  .debug_str	00000000 .debug_str
00000000 l    d  .comment	00000000 .comment
00000000 l    d  .debug_frame	00000000 .debug_frame
00000000 l    d  .debug_loc	00000000 .debug_loc
00000000 l    d  .stab	00000000 .stab
00000000 l    d  .stabstr	00000000 .stabstr
00000000 l    df *ABS*	00000000 ./Startup/startup_ch32v30x_D8C.o
00000004 l       .vector	00000000 _vector_base
00000000 l    df *ABS*	00000000 ch32v30x_it.c
00000000 l    df *ABS*	00000000 main.c
00000000 l    df *ABS*	00000000 system_ch32v30x.c
00000000 l    df *ABS*	00000000 ch32v30x_gpio.c
00000000 l    df *ABS*	00000000 ch32v30x_misc.c
00000000 l    df *ABS*	00000000 ch32v30x_rcc.c
20000040 l     O .data	00000010 APBAHBPrescTable
200000b4 l     O .data	00000004 ADCPrescTable
00000000 l    df *ABS*	00000000 ch32v30x_spi.c
00000000 l    df *ABS*	00000000 ch32v30x_tim.c
00000000 l    df *ABS*	00000000 ch32v30x_usart.c
00000000 l    df *ABS*	00000000 SysTickDelay.c
200000d8 l     O .bss	00000002 fac_ms
200000da l     O .bss	00000001 fac_us
00000000 l    df *ABS*	00000000 brewing_control.c
00000000 l    df *ABS*	00000000 ds18b20.c
00000000 l    df *ABS*	00000000 heater.c
00000000 l    df *ABS*	00000000 key.c
00000000 l    df *ABS*	00000000 lcd.c
00000000 l    df *ABS*	00000000 timer.c
00000000 l    df *ABS*	00000000 water_level.c
00000000 l    df *ABS*	00000000 water_pump.c
00000000 l    df *ABS*	00000000 debug.c
200000dc l     O .bss	00000002 p_ms
200000de l     O .bss	00000001 p_us
200000b8 l     O .data	00000004 curbrk.5274
00000000 l    df *ABS*	00000000 gesf2.c
00000000 l    df *ABS*	00000000 lesf2.c
00000000 l    df *ABS*	00000000 mulsf3.c
00000000 l    df *ABS*	00000000 subsf3.c
00000000 l    df *ABS*	00000000 floatsisf.c
00000000 l    df *ABS*	00000000 extendsfdf2.c
00000000 l    df *ABS*	00000000 libgcc2.c
00000000 l    df *ABS*	00000000 printf.c
00000000 l    df *ABS*	00000000 puts.c
00000000 l    df *ABS*	00000000 wbuf.c
00000000 l    df *ABS*	00000000 wsetup.c
00000000 l    df *ABS*	00000000 fflush.c
00000000 l    df *ABS*	00000000 findfp.c
00002a42 l     F .text	00000066 std
00000000 l    df *ABS*	00000000 fwalk.c
00000000 l    df *ABS*	00000000 makebuf.c
00000000 l    df *ABS*	00000000 nano-mallocr.c
00000000 l    df *ABS*	00000000 nano-mallocr.c
00000000 l    df *ABS*	00000000 nano-vfprintf.c
00002ed4 l     F .text	00000028 __sfputc_r
00000000 l    df *ABS*	00000000 nano-vfprintf_i.c
00000000 l    df *ABS*	00000000 sbrkr.c
00000000 l    df *ABS*	00000000 stdio.c
00000000 l    df *ABS*	00000000 writer.c
00000000 l    df *ABS*	00000000 closer.c
00000000 l    df *ABS*	00000000 fstatr.c
00000000 l    df *ABS*	00000000 isattyr.c
00000000 l    df *ABS*	00000000 lseekr.c
00000000 l    df *ABS*	00000000 memchr.c
00000000 l    df *ABS*	00000000 mlock.c
00000000 l    df *ABS*	00000000 readr.c
00000000 l    df *ABS*	00000000 close.c
00000000 l    df *ABS*	00000000 fstat.c
00000000 l    df *ABS*	00000000 isatty.c
00000000 l    df *ABS*	00000000 lseek.c
00000000 l    df *ABS*	00000000 read.c
00000000 l    df *ABS*	00000000 libgcc2.c
00000000 l    df *ABS*	00000000 impure.c
20000050 l     O .data	00000060 impure_data
00000000 l    df *ABS*	00000000 reent.c
000006c0  w      .text	00000000 EXTI2_IRQHandler
000006c0  w      .text	00000000 TIM8_TRG_COM_IRQHandler
000006c0  w      .text	00000000 TIM8_CC_IRQHandler
000036c8 g     F .text	00000028 _isatty_r
00002614 g     F .text	000000d4 _puts_r
000036f0 g     F .text	0000002c _lseek_r
00000d9c g     F .text	000000b4 BrewingControl_KeyHandler
000006c0  w      .text	00000000 UART8_IRQHandler
000010f2 g     F .text	00000024 DS18B20_ReadByte
000025d4 g     F .text	00000040 printf
200008b0 g       .data	00000000 __global_pointer$
000001c8 g     F .text	00000028 .hidden __riscv_save_8
000006c0  w      .text	00000000 TIM1_CC_IRQHandler
00000316 g     F .text	0000001a TempAlarmHandler
00003612 g     F .text	00000030 __sseek
00002af8 g     F .text	0000006c __sinit
00000a7c g     F .text	00000004 SPI_I2S_SendData
000026f4 g     F .text	000000bc __swbuf_r
000002ca g     F .text	00000010 HardFault_Handler
200000cc g     O .bss	00000001 p
00002ab2 g     F .text	00000046 __sfmoreglue
00003736 g     F .text	00000002 __malloc_unlock
0000127a g     F .text	00000092 key_init
000002dc g     F .text	0000003a key_proc
20000000 g     O .data	00000030 scheduler_task_t
00000214 g     F .text	0000000c .hidden __riscv_restore_3
000006c0  w      .text	00000000 TIM6_IRQHandler
00000bfe g     F .text	0000000e TIM_OC1PreloadConfig
000006c0  w      .text	00000000 SysTick_Handler
00000824 g     F .text	0000004e NVIC_Init
000006c0  w      .text	00000000 PVD_IRQHandler
000006c0  w      .text	00000000 SDIO_IRQHandler
00001a0c g     F .text	0000005e stir_360
000006c0  w      .text	00000000 TIM9_BRK_IRQHandler
00000200 g     F .text	00000020 .hidden __riscv_restore_10
00000a80 g     F .text	00000004 SPI_I2S_ReceiveData
000006c0  w      .text	00000000 DMA2_Channel8_IRQHandler
000002c8 g     F .text	00000002 NMI_Handler
000006c0  w      .text	00000000 CAN2_RX1_IRQHandler
000006c0  w      .text	00000000 EXTI3_IRQHandler
000001c8 g     F .text	00000028 .hidden __riscv_save_11
000015ec g     F .text	00000062 spi_readwrite
000006c0  w      .text	00000000 USBHS_IRQHandler
00000ce0 g     F .text	0000000a USART_GetFlagStatus
000006c0  w      .text	00000000 DMA2_Channel9_IRQHandler
0000369e g     F .text	0000002a _fstat_r
000006c0  w      .text	00000000 TIM10_CC_IRQHandler
20000130 g     O .bss	00000004 errno
200000c8 g       .bss	00000000 _sbss
00000800 g       *ABS*	00000000 __stack_size
000016b6 g     F .text	00000030 LCD_WR_REG
00001c0c g     F .text	0000005a USART_Printf_Init
0000206c g     F .text	00000388 .hidden __subsf3
00000f84 g     F .text	00000026 DS18B20_IO_OUT
000006c0  w      .text	00000000 USBFS_IRQHandler
00000214 g     F .text	0000000c .hidden __riscv_restore_2
00001a6a g     F .text	00000020 stir
00001ad2 g     F .text	00000082 WaterPump_Init
00000fce g     F .text	00000030 DS18B20_Reset
00001abc g     F .text	00000016 WaterLevel_Detect
00002aa8 g     F .text	0000000a _cleanup_r
00000d7a g     F .text	00000022 BrewingControl_Start
000006c0  w      .text	00000000 EXTI0_IRQHandler
000006c0  w      .text	00000000 I2C2_EV_IRQHandler
000006c0  w      .text	00000000 TIM10_TRG_COM_IRQHandler
00000bba g     F .text	00000018 TIM_Cmd
000026e8 g     F .text	0000000c puts
200000b0 g     O .data	00000004 SystemCoreClock
00003770 g     F .text	0000000c _fstat
00000004 g       .init	00000000 _einit
00000c28 g     F .text	0000000c TIM_ClearITPendingBit
00001d50 g     F .text	0000008c .hidden __lesf2
000009e8 g     F .text	0000001e RCC_APB2PeriphClockCmd
000001c0 g     F .text	00000030 .hidden __riscv_save_12
000006c0  w      .text	00000000 CAN2_SCE_IRQHandler
000006c0  w      .text	00000000 ADC1_2_IRQHandler
00001982 g     F .text	0000008a TIM2_PWM_Init
0000074c g     F .text	000000c0 GPIO_Init
000006c0  w      .text	00000000 Break_Point_Handler
00000200 g     F .text	00000020 .hidden __riscv_restore_11
200000d4 g     O .bss	00000004 NVIC_Priority_Group
000006c0  w      .text	00000000 SPI1_IRQHandler
00000cc2 g     F .text	00000016 USART_Cmd
00003574 g     F .text	0000002a _sbrk_r
000006c0  w      .text	00000000 TAMPER_IRQHandler
000001f0 g     F .text	0000000c .hidden __riscv_save_1
00000214 g     F .text	0000000c .hidden __riscv_restore_0
00003738 g     F .text	0000002c _read_r
000001d6 g     F .text	0000001a .hidden __riscv_save_7
000006c0  w      .text	00000000 CAN2_RX0_IRQHandler
000024ba g     F .text	000000ac .hidden __extendsfdf2
000006c0  w      .text	00000000 TIM8_UP_IRQHandler
00000a06 g     F .text	0000001e RCC_APB1PeriphClockCmd
000006c0  w      .text	00000000 Ecall_M_Mode_Handler
20007800 g       .stack	00000000 _heap_end
0000377c g     F .text	0000000c _isatty
200000c0 g     O .data	00000004 _global_impure_ptr
0000020a g     F .text	00000016 .hidden __riscv_restore_5
00000faa g     F .text	00000024 DS18B20_IO_IN
00000a84 g     F .text	0000000a SPI_I2S_GetFlagStatus
000006c0  w      .text	00000000 DMA2_Channel2_IRQHandler
000006c0  w      .text	00000000 DMA1_Channel4_IRQHandler
00001ca4 g     F .text	00000026 _sbrk
200000e8 g     O .bss	00000010 ds18b20
000006c0  w      .text	00000000 TIM9_UP_IRQHandler
0000020a g     F .text	00000016 .hidden __riscv_restore_6
000006c0  w      .text	00000000 USART3_IRQHandler
200000cb g     O .bss	00000001 key_val
000006c0  w      .text	00000000 RTC_IRQHandler
20000134 g       .bss	00000000 _ebss
000006c0  w      .text	00000000 DMA1_Channel7_IRQHandler
00000b38 g     F .text	00000082 TIM_OC1Init
000006c0  w      .text	00000000 CAN1_RX1_IRQHandler
00001bac g     F .text	0000002a Delay_Init
000006c0  w      .text	00000000 DVP_IRQHandler
000006c0  w      .text	00000000 UART5_IRQHandler
000011cc g     F .text	00000004 DS18B20_SetCallback
0000130c g     F .text	00000200 key_read
200000c9 g     O .bss	00000001 key_old
0000167e g     F .text	00000038 LCD_WR_DATA
00001116 g     F .text	0000004a DS18B20_Init
00001cca g     F .text	00000086 .hidden __gtsf2
0000150c g     F .text	000000dc SPI_LCD_Init
00000816 g     F .text	00000004 GPIO_SetBits
000006c0  w      .text	00000000 TIM4_IRQHandler
00000be4 g     F .text	0000001a TIM_ARRPreloadConfig
000001c8 g     F .text	00000028 .hidden __riscv_save_9
00001b54 g     F .text	00000058 WaterPump_Control
00000d5a g     F .text	00000020 BrewingControl_Init
000006c0  w      .text	00000000 DMA2_Channel1_IRQHandler
0000122e g     F .text	00000016 Heater_Stop
00003ae4 g     O .text	00000020 __sf_fake_stderr
000001d6 g     F .text	0000001a .hidden __riscv_save_4
000006c0  w      .text	00000000 I2C1_EV_IRQHandler
00000c10 g     F .text	00000018 TIM_GetITStatus
00000872 g     F .text	00000176 RCC_GetClocksFreq
000006c0  w      .text	00000000 DMA1_Channel6_IRQHandler
00002566 g     F .text	0000006e .hidden __clzsi2
00002efc g     F .text	00000042 __sfputs_r
000006c0  w      .text	00000000 UART4_IRQHandler
000006c0  w      .text	00000000 DMA2_Channel4_IRQHandler
0000371c g     F .text	00000018 memchr
00000c34 g     F .text	0000008e USART_Init
00001052 g     F .text	00000044 DS18B20_WriteBit
00002d58 g     F .text	000000a8 _free_r
00000388 g     F .text	0000002c TIM3_IRQHandler
000006c0  w      .text	00000000 RCC_IRQHandler
00001a8a g     F .text	00000032 WaterLevel_Init
000001f0 g     F .text	0000000c .hidden __riscv_save_3
00001160 g     F .text	00000020 DS18B20_StartConvert
000006c0  w      .text	00000000 TIM1_TRG_COM_IRQHandler
000006c0  w      .text	00000000 DMA1_Channel1_IRQHandler
00000000 g       .init	00000000 _start
000006c0  w      .text	00000000 DMA2_Channel7_IRQHandler
20000030 g     O .data	00000010 AHBPrescTable
00003788 g     F .text	0000000c _lseek
000003b4 g     F .text	0000003c scheduler_run
000023f4 g     F .text	000000c6 .hidden __floatsisf
000006c0  w      .text	00000000 EXTI15_10_IRQHandler
00001218 g     F .text	00000016 Heater_Start
00001180 g     F .text	0000004c DS18B20_ReadTempRaw
00000bd2 g     F .text	00000012 TIM_ITConfig
000010d6 g     F .text	0000001c DS18B20_WriteByte
0000081a g     F .text	00000004 GPIO_ResetBits
000006c0  w      .text	00000000 TIM7_IRQHandler
00003676 g     F .text	00000028 _close_r
000006c0  w      .text	00000000 CAN2_TX_IRQHandler
20000000 g       .dalign	00000000 _data_vma
000006c0  w      .text	00000000 TIM5_IRQHandler
200000cd g     O .bss	00000001 task_num
00000ffe g     F .text	00000054 DS18B20_Check
000027b0 g     F .text	000000fc __swsetup_r
000006c0  w      .text	00000000 EXTI9_5_IRQHandler
00002b64 g     F .text	000000a0 __sfp
00000d1c g     F .text	0000003e Delay_ms
000001c8 g     F .text	00000028 .hidden __riscv_save_10
0000359e g     F .text	0000002c __sread
000006c0  w      .text	00000000 ETH_WKUP_IRQHandler
00003734 g     F .text	00000002 __malloc_lock
0000020a g     F .text	00000016 .hidden __riscv_restore_4
00000200 g     F .text	00000020 .hidden __riscv_restore_8
00001096 g     F .text	00000040 DS18B20_ReadBit
000029dc g     F .text	00000066 _fflush_r
000001d6 g     F .text	0000001a .hidden __riscv_save_6
000006c0  w      .text	00000000 SPI2_IRQHandler
0000164e g     F .text	00000030 LCD_WR_DATA8
00003b04 g     O .text	00000020 __sf_fake_stdin
0000173a g     F .text	00000206 LCD_Init
00000220 g     F .text	000000a8 memset
00000200 g     F .text	00000020 .hidden __riscv_restore_9
0000020a g     F .text	00000016 .hidden __riscv_restore_7
000003f0 g     F .text	000000a8 main
000006c0  w      .text	00000000 TIM10_BRK_IRQHandler
00001d50 g     F .text	0000008c .hidden __ltsf2
000006c0  w      .text	00000000 TIM9_CC_IRQHandler
00003642 g     F .text	00000006 __sclose
000006c0  w      .text	00000000 DMA2_Channel5_IRQHandler
00002e00 g     F .text	000000d4 _malloc_r
20000110 g     O .bss	00000020 g_pump_ctrl
000006c0  w      .text	00000000 DMA1_Channel5_IRQHandler
000006c0  w      .text	00000000 EXTI4_IRQHandler
00001bd6 g     F .text	00000036 Delay_Ms
000006c0  w      .text	00000000 USB_LP_CAN1_RX0_IRQHandler
00001ddc g     F .text	00000290 .hidden __mulsf3
00000a62 g     F .text	0000001a SPI_Cmd
00000330 g     F .text	00000002 lock_proc
00000498 g     F .text	000000fa SystemInit
000006c0  w      .text	00000000 RNG_IRQHandler
00000e50 g     F .text	00000134 BrewingControl_Task
00001244 g     F .text	00000036 Heater_Init
00001940 g     F .text	00000042 LCD_Fill
000025d4 g     F .text	00000040 iprintf
000006c0  w      .text	00000000 USB_HP_CAN1_TX_IRQHandler
000039e4 g     O .text	00000100 .hidden __clz_tab
00000000 g       .init	00000000 _sinit
000011d0 g     F .text	00000048 DS18B20_ReadRealtimeTemp
00001cca g     F .text	00000086 .hidden __gesf2
00003648 g     F .text	0000002e _write_r
000006c0  w      .text	00000000 DMA1_Channel3_IRQHandler
000016e6 g     F .text	00000054 LCD_Address_Set
00000332 g     F .text	00000056 Tim3_Init
200000f8 g     O .bss	00000018 g_brewing_ctrl
000006c0  w      .text	00000000 ETH_IRQHandler
000031c6 g     F .text	0000010c _printf_common
200000bc g     O .data	00000004 _impure_ptr
000006c0  w      .text	00000000 TIM1_UP_IRQHandler
000028ac g     F .text	00000130 __sflush_r
000002da g     F .text	00000002 lcd_proc
000006c0  w      .text	00000000 WWDG_IRQHandler
000006c0  w      .text	00000000 USBHSWakeup_IRQHandler
000006c0  w      .text	00000000 DMA2_Channel11_IRQHandler
000006c0  w      .text	00000000 Ecall_U_Mode_Handler
000006c0  w      .text	00000000 DMA2_Channel6_IRQHandler
000006c0  w      .text	00000000 TIM2_IRQHandler
20008000 g       .stack	00000000 _eusrstack
000001f0 g     F .text	0000000c .hidden __riscv_save_2
000006c0  w      .text	00000000 SW_Handler
000006c0  w      .text	00000000 TIM1_BRK_IRQHandler
00002c6e g     F .text	00000058 __swhatbuf_r
00000cd8 g     F .text	00000008 USART_SendData
000006c0  w      .text	00000000 DMA2_Channel10_IRQHandler
000006c0  w      .text	00000000 EXTI1_IRQHandler
200000c8 g     O .bss	00000001 key_down
000001d6 g     F .text	0000001a .hidden __riscv_save_5
00001c66 g     F .text	0000003e _write
200000ca g     O .bss	00000001 key_up
200000c8 g       .data	00000000 _edata
20000134 g       .bss	00000000 _end
00000a8e g     F .text	000000aa TIM_TimeBaseInit
000006c0  w      .text	00000000 RTCAlarm_IRQHandler
00003b80 g       .dlalign	00000000 _data_lma
000006c0  w      .text	00000000 TIM10_UP_IRQHandler
000006c0  w      .text	00000000 TIM9_TRG_COM_IRQHandler
000006c0  w      .text	00000000 UART7_IRQHandler
000006c0  w      .text	00000000 USART2_IRQHandler
000006c0  w      .text	00000000 UART6_IRQHandler
000035ca g     F .text	00000048 __swrite
00002f3e g     F .text	00000288 _vfiprintf_r
00002c04 g     F .text	0000006a _fwalk_reent
00000592 g     F .text	0000012e SystemCoreClockUpdate
000006c0  w      .text	00000000 I2C2_ER_IRQHandler
000006c0  w      .text	00000000 DMA1_Channel2_IRQHandler
00003b24 g     O .text	00000020 __sf_fake_stdout
000001fc g     F .text	00000024 .hidden __riscv_restore_12
000006c0  w      .text	00000000 TIM8_BRK_IRQHandler
00003794 g     F .text	0000000c _read
000006c2  w      .text	00000000 handle_reset
000006c0  w      .text	00000000 CAN1_SCE_IRQHandler
000006c0  w      .text	00000000 FLASH_IRQHandler
000001f0 g     F .text	0000000c .hidden __riscv_save_0
000006c0  w      .text	00000000 USART1_IRQHandler
00002cc6 g     F .text	00000092 __smakebuf_r
000032d2 g     F .text	000002a2 _printf_i
000015e8 g     F .text	00000004 SPI3_IRQHandler
200000e4 g     O .bss	00000004 __malloc_sbrk_start
000006c0  w      .text	00000000 I2C1_ER_IRQHandler
00000cea g     F .text	00000032 Delay_us
00000a24 g     F .text	0000003e SPI_Init
0000081e g     F .text	00000006 NVIC_PriorityGroupConfig
200000e0 g     O .bss	00000004 __malloc_free_list
00000214 g     F .text	0000000c .hidden __riscv_restore_1
00002f3e g     F .text	00000288 _vfprintf_r
00000c0c g     F .text	00000004 TIM_SetCompare1
0000080c g     F .text	0000000a GPIO_ReadInputDataBit
200000d0 g     O .bss	00000004 uwtick
000006c0  w      .text	00000000 USBWakeUp_IRQHandler
00003764 g     F .text	0000000c _close
000006c0  w      .text	00000000 DMA2_Channel3_IRQHandler



Disassembly of section .init:

00000000 <_sinit>:
   0:	6c20006f          	j	6c2 <handle_reset>

Disassembly of section .vector:

00000004 <_vector_base>:
	...
   c:	02c8                	addi	a0,sp,324
   e:	0000                	unimp
  10:	02ca                	slli	t0,t0,0x12
  12:	0000                	unimp
  14:	0000                	unimp
  16:	0000                	unimp
  18:	06c0                	addi	s0,sp,836
	...
  22:	0000                	unimp
  24:	06c0                	addi	s0,sp,836
  26:	0000                	unimp
  28:	06c0                	addi	s0,sp,836
	...
  32:	0000                	unimp
  34:	06c0                	addi	s0,sp,836
  36:	0000                	unimp
  38:	0000                	unimp
  3a:	0000                	unimp
  3c:	06c0                	addi	s0,sp,836
  3e:	0000                	unimp
  40:	0000                	unimp
  42:	0000                	unimp
  44:	06c0                	addi	s0,sp,836
  46:	0000                	unimp
  48:	06c0                	addi	s0,sp,836
  4a:	0000                	unimp
  4c:	06c0                	addi	s0,sp,836
  4e:	0000                	unimp
  50:	06c0                	addi	s0,sp,836
  52:	0000                	unimp
  54:	06c0                	addi	s0,sp,836
  56:	0000                	unimp
  58:	06c0                	addi	s0,sp,836
  5a:	0000                	unimp
  5c:	06c0                	addi	s0,sp,836
  5e:	0000                	unimp
  60:	06c0                	addi	s0,sp,836
  62:	0000                	unimp
  64:	06c0                	addi	s0,sp,836
  66:	0000                	unimp
  68:	06c0                	addi	s0,sp,836
  6a:	0000                	unimp
  6c:	06c0                	addi	s0,sp,836
  6e:	0000                	unimp
  70:	06c0                	addi	s0,sp,836
  72:	0000                	unimp
  74:	06c0                	addi	s0,sp,836
  76:	0000                	unimp
  78:	06c0                	addi	s0,sp,836
  7a:	0000                	unimp
  7c:	06c0                	addi	s0,sp,836
  7e:	0000                	unimp
  80:	06c0                	addi	s0,sp,836
  82:	0000                	unimp
  84:	06c0                	addi	s0,sp,836
  86:	0000                	unimp
  88:	06c0                	addi	s0,sp,836
  8a:	0000                	unimp
  8c:	06c0                	addi	s0,sp,836
  8e:	0000                	unimp
  90:	06c0                	addi	s0,sp,836
  92:	0000                	unimp
  94:	06c0                	addi	s0,sp,836
  96:	0000                	unimp
  98:	06c0                	addi	s0,sp,836
  9a:	0000                	unimp
  9c:	06c0                	addi	s0,sp,836
  9e:	0000                	unimp
  a0:	06c0                	addi	s0,sp,836
  a2:	0000                	unimp
  a4:	06c0                	addi	s0,sp,836
  a6:	0000                	unimp
  a8:	06c0                	addi	s0,sp,836
  aa:	0000                	unimp
  ac:	06c0                	addi	s0,sp,836
  ae:	0000                	unimp
  b0:	06c0                	addi	s0,sp,836
  b2:	0000                	unimp
  b4:	06c0                	addi	s0,sp,836
  b6:	0000                	unimp
  b8:	0388                	addi	a0,sp,448
  ba:	0000                	unimp
  bc:	06c0                	addi	s0,sp,836
  be:	0000                	unimp
  c0:	06c0                	addi	s0,sp,836
  c2:	0000                	unimp
  c4:	06c0                	addi	s0,sp,836
  c6:	0000                	unimp
  c8:	06c0                	addi	s0,sp,836
  ca:	0000                	unimp
  cc:	06c0                	addi	s0,sp,836
  ce:	0000                	unimp
  d0:	06c0                	addi	s0,sp,836
  d2:	0000                	unimp
  d4:	06c0                	addi	s0,sp,836
  d6:	0000                	unimp
  d8:	06c0                	addi	s0,sp,836
  da:	0000                	unimp
  dc:	06c0                	addi	s0,sp,836
  de:	0000                	unimp
  e0:	06c0                	addi	s0,sp,836
  e2:	0000                	unimp
  e4:	06c0                	addi	s0,sp,836
  e6:	0000                	unimp
  e8:	06c0                	addi	s0,sp,836
  ea:	0000                	unimp
  ec:	06c0                	addi	s0,sp,836
  ee:	0000                	unimp
  f0:	06c0                	addi	s0,sp,836
  f2:	0000                	unimp
  f4:	06c0                	addi	s0,sp,836
  f6:	0000                	unimp
  f8:	06c0                	addi	s0,sp,836
  fa:	0000                	unimp
  fc:	06c0                	addi	s0,sp,836
  fe:	0000                	unimp
 100:	06c0                	addi	s0,sp,836
 102:	0000                	unimp
 104:	0000                	unimp
 106:	0000                	unimp
 108:	06c0                	addi	s0,sp,836
 10a:	0000                	unimp
 10c:	06c0                	addi	s0,sp,836
 10e:	0000                	unimp
 110:	15e8                	addi	a0,sp,748
 112:	0000                	unimp
 114:	06c0                	addi	s0,sp,836
 116:	0000                	unimp
 118:	06c0                	addi	s0,sp,836
 11a:	0000                	unimp
 11c:	06c0                	addi	s0,sp,836
 11e:	0000                	unimp
 120:	06c0                	addi	s0,sp,836
 122:	0000                	unimp
 124:	06c0                	addi	s0,sp,836
 126:	0000                	unimp
 128:	06c0                	addi	s0,sp,836
 12a:	0000                	unimp
 12c:	06c0                	addi	s0,sp,836
 12e:	0000                	unimp
 130:	06c0                	addi	s0,sp,836
 132:	0000                	unimp
 134:	06c0                	addi	s0,sp,836
 136:	0000                	unimp
 138:	06c0                	addi	s0,sp,836
 13a:	0000                	unimp
 13c:	06c0                	addi	s0,sp,836
 13e:	0000                	unimp
 140:	06c0                	addi	s0,sp,836
 142:	0000                	unimp
 144:	06c0                	addi	s0,sp,836
 146:	0000                	unimp
 148:	06c0                	addi	s0,sp,836
 14a:	0000                	unimp
 14c:	06c0                	addi	s0,sp,836
 14e:	0000                	unimp
 150:	06c0                	addi	s0,sp,836
 152:	0000                	unimp
 154:	06c0                	addi	s0,sp,836
 156:	0000                	unimp
 158:	06c0                	addi	s0,sp,836
 15a:	0000                	unimp
 15c:	06c0                	addi	s0,sp,836
 15e:	0000                	unimp
 160:	06c0                	addi	s0,sp,836
 162:	0000                	unimp
 164:	06c0                	addi	s0,sp,836
 166:	0000                	unimp
 168:	06c0                	addi	s0,sp,836
 16a:	0000                	unimp
 16c:	06c0                	addi	s0,sp,836
 16e:	0000                	unimp
 170:	06c0                	addi	s0,sp,836
 172:	0000                	unimp
 174:	06c0                	addi	s0,sp,836
 176:	0000                	unimp
 178:	06c0                	addi	s0,sp,836
 17a:	0000                	unimp
 17c:	06c0                	addi	s0,sp,836
 17e:	0000                	unimp
 180:	06c0                	addi	s0,sp,836
 182:	0000                	unimp
 184:	06c0                	addi	s0,sp,836
 186:	0000                	unimp
 188:	06c0                	addi	s0,sp,836
 18a:	0000                	unimp
 18c:	06c0                	addi	s0,sp,836
 18e:	0000                	unimp
 190:	06c0                	addi	s0,sp,836
 192:	0000                	unimp
 194:	06c0                	addi	s0,sp,836
 196:	0000                	unimp
 198:	06c0                	addi	s0,sp,836
 19a:	0000                	unimp
 19c:	06c0                	addi	s0,sp,836
 19e:	0000                	unimp
 1a0:	06c0                	addi	s0,sp,836
	...

Disassembly of section .text:

000001c0 <__riscv_save_12>:
     1c0:	7139                	addi	sp,sp,-64
     1c2:	4301                	li	t1,0
     1c4:	c66e                	sw	s11,12(sp)
     1c6:	a019                	j	1cc <__riscv_save_10+0x4>

000001c8 <__riscv_save_10>:
     1c8:	7139                	addi	sp,sp,-64
     1ca:	5341                	li	t1,-16
     1cc:	c86a                	sw	s10,16(sp)
     1ce:	ca66                	sw	s9,20(sp)
     1d0:	cc62                	sw	s8,24(sp)
     1d2:	ce5e                	sw	s7,28(sp)
     1d4:	a019                	j	1da <__riscv_save_4+0x4>

000001d6 <__riscv_save_4>:
     1d6:	7139                	addi	sp,sp,-64
     1d8:	5301                	li	t1,-32
     1da:	d05a                	sw	s6,32(sp)
     1dc:	d256                	sw	s5,36(sp)
     1de:	d452                	sw	s4,40(sp)
     1e0:	d64e                	sw	s3,44(sp)
     1e2:	d84a                	sw	s2,48(sp)
     1e4:	da26                	sw	s1,52(sp)
     1e6:	dc22                	sw	s0,56(sp)
     1e8:	de06                	sw	ra,60(sp)
     1ea:	40610133          	sub	sp,sp,t1
     1ee:	8282                	jr	t0

000001f0 <__riscv_save_0>:
     1f0:	1141                	addi	sp,sp,-16
     1f2:	c04a                	sw	s2,0(sp)
     1f4:	c226                	sw	s1,4(sp)
     1f6:	c422                	sw	s0,8(sp)
     1f8:	c606                	sw	ra,12(sp)
     1fa:	8282                	jr	t0

000001fc <__riscv_restore_12>:
     1fc:	4db2                	lw	s11,12(sp)
     1fe:	0141                	addi	sp,sp,16

00000200 <__riscv_restore_10>:
     200:	4d02                	lw	s10,0(sp)
     202:	4c92                	lw	s9,4(sp)
     204:	4c22                	lw	s8,8(sp)
     206:	4bb2                	lw	s7,12(sp)
     208:	0141                	addi	sp,sp,16

0000020a <__riscv_restore_4>:
     20a:	4b02                	lw	s6,0(sp)
     20c:	4a92                	lw	s5,4(sp)
     20e:	4a22                	lw	s4,8(sp)
     210:	49b2                	lw	s3,12(sp)
     212:	0141                	addi	sp,sp,16

00000214 <__riscv_restore_0>:
     214:	4902                	lw	s2,0(sp)
     216:	4492                	lw	s1,4(sp)
     218:	4422                	lw	s0,8(sp)
     21a:	40b2                	lw	ra,12(sp)
     21c:	0141                	addi	sp,sp,16
     21e:	8082                	ret

00000220 <memset>:
     220:	433d                	li	t1,15
     222:	872a                	mv	a4,a0
     224:	02c37363          	bgeu	t1,a2,24a <memset+0x2a>
     228:	00f77793          	andi	a5,a4,15
     22c:	efbd                	bnez	a5,2aa <memset+0x8a>
     22e:	e5ad                	bnez	a1,298 <memset+0x78>
     230:	ff067693          	andi	a3,a2,-16
     234:	8a3d                	andi	a2,a2,15
     236:	96ba                	add	a3,a3,a4
     238:	c30c                	sw	a1,0(a4)
     23a:	c34c                	sw	a1,4(a4)
     23c:	c70c                	sw	a1,8(a4)
     23e:	c74c                	sw	a1,12(a4)
     240:	0741                	addi	a4,a4,16
     242:	fed76be3          	bltu	a4,a3,238 <memset+0x18>
     246:	e211                	bnez	a2,24a <memset+0x2a>
     248:	8082                	ret
     24a:	40c306b3          	sub	a3,t1,a2
     24e:	068a                	slli	a3,a3,0x2
     250:	00000297          	auipc	t0,0x0
     254:	9696                	add	a3,a3,t0
     256:	00a68067          	jr	10(a3)
     25a:	00b70723          	sb	a1,14(a4)
     25e:	00b706a3          	sb	a1,13(a4)
     262:	00b70623          	sb	a1,12(a4)
     266:	00b705a3          	sb	a1,11(a4)
     26a:	00b70523          	sb	a1,10(a4)
     26e:	00b704a3          	sb	a1,9(a4)
     272:	00b70423          	sb	a1,8(a4)
     276:	00b703a3          	sb	a1,7(a4)
     27a:	00b70323          	sb	a1,6(a4)
     27e:	00b702a3          	sb	a1,5(a4)
     282:	00b70223          	sb	a1,4(a4)
     286:	00b701a3          	sb	a1,3(a4)
     28a:	00b70123          	sb	a1,2(a4)
     28e:	00b700a3          	sb	a1,1(a4)
     292:	00b70023          	sb	a1,0(a4)
     296:	8082                	ret
     298:	0ff5f593          	andi	a1,a1,255
     29c:	00859693          	slli	a3,a1,0x8
     2a0:	8dd5                	or	a1,a1,a3
     2a2:	01059693          	slli	a3,a1,0x10
     2a6:	8dd5                	or	a1,a1,a3
     2a8:	b761                	j	230 <memset+0x10>
     2aa:	00279693          	slli	a3,a5,0x2
     2ae:	00000297          	auipc	t0,0x0
     2b2:	9696                	add	a3,a3,t0
     2b4:	8286                	mv	t0,ra
     2b6:	fa8680e7          	jalr	-88(a3)
     2ba:	8096                	mv	ra,t0
     2bc:	17c1                	addi	a5,a5,-16
     2be:	8f1d                	sub	a4,a4,a5
     2c0:	963e                	add	a2,a2,a5
     2c2:	f8c374e3          	bgeu	t1,a2,24a <memset+0x2a>
     2c6:	b7a5                	j	22e <memset+0xe>

000002c8 <NMI_Handler>:
     2c8:	a001                	j	2c8 <NMI_Handler>

000002ca <HardFault_Handler>:
     2ca:	beef07b7          	lui	a5,0xbeef0
     2ce:	e000e737          	lui	a4,0xe000e
     2d2:	08078793          	addi	a5,a5,128 # beef0080 <_eusrstack+0x9eee8080>
     2d6:	c73c                	sw	a5,72(a4)
     2d8:	a001                	j	2d8 <HardFault_Handler+0xe>

000002da <lcd_proc>:
     2da:	8082                	ret

000002dc <key_proc>:
     2dc:	f15ff2ef          	jal	t0,1f0 <__riscv_save_0>
     2e0:	02c010ef          	jal	ra,130c <key_read>
     2e4:	81918713          	addi	a4,gp,-2023 # 200000c9 <key_old>
     2e8:	80a18da3          	sb	a0,-2021(gp) # 200000cb <key_val>
     2ec:	231c                	lbu	a5,0(a4)
     2ee:	a308                	sb	a0,0(a4)
     2f0:	00f54633          	xor	a2,a0,a5
     2f4:	fff7c793          	not	a5,a5
     2f8:	80c18e23          	sb	a2,-2020(gp) # 200000cc <p>
     2fc:	8fe9                	and	a5,a5,a0
     2fe:	80f18c23          	sb	a5,-2024(gp) # 200000c8 <_edata>
     302:	fff54693          	not	a3,a0
     306:	8ef1                	and	a3,a3,a2
     308:	80d18d23          	sb	a3,-2022(gp) # 200000ca <key_up>
     30c:	c781                	beqz	a5,314 <key_proc+0x38>
     30e:	853e                	mv	a0,a5
     310:	28d000ef          	jal	ra,d9c <BrewingControl_KeyHandler>
     314:	b701                	j	214 <__riscv_restore_0>

00000316 <TempAlarmHandler>:
     316:	edbff2ef          	jal	t0,1f0 <__riscv_save_0>
     31a:	1a0020ef          	jal	ra,24ba <__extendsfdf2>
     31e:	862a                	mv	a2,a0
     320:	00003537          	lui	a0,0x3
     324:	86ae                	mv	a3,a1
     326:	7a050513          	addi	a0,a0,1952 # 37a0 <_read+0xc>
     32a:	2aa020ef          	jal	ra,25d4 <iprintf>
     32e:	b5dd                	j	214 <__riscv_restore_0>

00000330 <lock_proc>:
     330:	8082                	ret

00000332 <Tim3_Init>:
     332:	ebfff2ef          	jal	t0,1f0 <__riscv_save_0>
     336:	1101                	addi	sp,sp,-32
     338:	84aa                	mv	s1,a0
     33a:	842e                	mv	s0,a1
     33c:	4509                	li	a0,2
     33e:	4585                	li	a1,1
     340:	25d9                	jal	a06 <RCC_APB1PeriphClockCmd>
     342:	82e0                	sh	s0,20(sp)
     344:	40000437          	lui	s0,0x40000
     348:	084c                	addi	a1,sp,20
     34a:	40040513          	addi	a0,s0,1024 # 40000400 <_eusrstack+0x1fff8400>
     34e:	84e4                	sh	s1,24(sp)
     350:	00011d23          	sh	zero,26(sp)
     354:	00011b23          	sh	zero,22(sp)
     358:	2f1d                	jal	a8e <TIM_TimeBaseInit>
     35a:	4605                	li	a2,1
     35c:	04100593          	li	a1,65
     360:	40040513          	addi	a0,s0,1024
     364:	06f000ef          	jal	ra,bd2 <TIM_ITConfig>
     368:	12d00793          	li	a5,301
     36c:	867c                	sh	a5,12(sp)
     36e:	478d                	li	a5,3
     370:	875c                	sb	a5,14(sp)
     372:	0068                	addi	a0,sp,12
     374:	4785                	li	a5,1
     376:	c83e                	sw	a5,16(sp)
     378:	2175                	jal	824 <NVIC_Init>
     37a:	4585                	li	a1,1
     37c:	40040513          	addi	a0,s0,1024
     380:	03b000ef          	jal	ra,bba <TIM_Cmd>
     384:	6105                	addi	sp,sp,32
     386:	b579                	j	214 <__riscv_restore_0>

00000388 <TIM3_IRQHandler>:
     388:	40000537          	lui	a0,0x40000
     38c:	4585                	li	a1,1
     38e:	40050513          	addi	a0,a0,1024 # 40000400 <_eusrstack+0x1fff8400>
     392:	07f000ef          	jal	ra,c10 <TIM_GetITStatus>
     396:	c511                	beqz	a0,3a2 <TIM3_IRQHandler+0x1a>
     398:	82018793          	addi	a5,gp,-2016 # 200000d0 <uwtick>
     39c:	4398                	lw	a4,0(a5)
     39e:	0705                	addi	a4,a4,1
     3a0:	c398                	sw	a4,0(a5)
     3a2:	40000537          	lui	a0,0x40000
     3a6:	4585                	li	a1,1
     3a8:	40050513          	addi	a0,a0,1024 # 40000400 <_eusrstack+0x1fff8400>
     3ac:	07d000ef          	jal	ra,c28 <TIM_ClearITPendingBit>
     3b0:	30200073          	mret

000003b4 <scheduler_run>:
     3b4:	e23ff2ef          	jal	t0,1d6 <__riscv_save_4>
     3b8:	200004b7          	lui	s1,0x20000
     3bc:	4401                	li	s0,0
     3be:	00048493          	mv	s1,s1
     3c2:	4a31                	li	s4,12
     3c4:	81d1c783          	lbu	a5,-2019(gp) # 200000cd <task_num>
     3c8:	00f46363          	bltu	s0,a5,3ce <scheduler_run+0x1a>
     3cc:	bd3d                	j	20a <__riscv_restore_4>
     3ce:	034407b3          	mul	a5,s0,s4
     3d2:	8201a683          	lw	a3,-2016(gp) # 200000d0 <uwtick>
     3d6:	97a6                	add	a5,a5,s1
     3d8:	43d8                	lw	a4,4(a5)
     3da:	4790                	lw	a2,8(a5)
     3dc:	9732                	add	a4,a4,a2
     3de:	00e6e563          	bltu	a3,a4,3e8 <scheduler_run+0x34>
     3e2:	c794                	sw	a3,8(a5)
     3e4:	439c                	lw	a5,0(a5)
     3e6:	9782                	jalr	a5
     3e8:	0405                	addi	s0,s0,1
     3ea:	0ff47413          	andi	s0,s0,255
     3ee:	bfd9                	j	3c4 <scheduler_run+0x10>

000003f0 <main>:
     3f0:	e01ff2ef          	jal	t0,1f0 <__riscv_save_0>
     3f4:	4509                	li	a0,2
     3f6:	2125                	jal	81e <NVIC_PriorityGroupConfig>
     3f8:	2a69                	jal	592 <SystemCoreClockUpdate>
     3fa:	6571                	lui	a0,0x1c
     3fc:	20050513          	addi	a0,a0,512 # 1c200 <_data_lma+0x18680>
     400:	00d010ef          	jal	ra,1c0c <USART_Printf_Init>
     404:	7a8010ef          	jal	ra,1bac <Delay_Init>
     408:	63d000ef          	jal	ra,1244 <Heater_Init>
     40c:	6c6010ef          	jal	ra,1ad2 <WaterPump_Init>
     410:	67a010ef          	jal	ra,1a8a <WaterLevel_Init>
     414:	56e010ef          	jal	ra,1982 <TIM2_PWM_Init>
     418:	00003537          	lui	a0,0x3
     41c:	7c850513          	addi	a0,a0,1992 # 37c8 <_read+0x34>
     420:	2c8020ef          	jal	ra,26e8 <puts>
     424:	000037b7          	lui	a5,0x3
     428:	7c47a583          	lw	a1,1988(a5) # 37c4 <_read+0x30>
     42c:	83818513          	addi	a0,gp,-1992 # 200000e8 <ds18b20>
     430:	4e7000ef          	jal	ra,1116 <DS18B20_Init>
     434:	c901                	beqz	a0,444 <main+0x54>
     436:	00003537          	lui	a0,0x3
     43a:	7d450513          	addi	a0,a0,2004 # 37d4 <_read+0x40>
     43e:	2aa020ef          	jal	ra,26e8 <puts>
     442:	a001                	j	442 <main+0x52>
     444:	000005b7          	lui	a1,0x0
     448:	31658593          	addi	a1,a1,790 # 316 <TempAlarmHandler>
     44c:	83818513          	addi	a0,gp,-1992 # 200000e8 <ds18b20>
     450:	57d000ef          	jal	ra,11cc <DS18B20_SetCallback>
     454:	107000ef          	jal	ra,d5a <BrewingControl_Init>
     458:	2e2010ef          	jal	ra,173a <LCD_Init>
     45c:	6741                	lui	a4,0x10
     45e:	177d                	addi	a4,a4,-1
     460:	07f00693          	li	a3,127
     464:	07f00613          	li	a2,127
     468:	4581                	li	a1,0
     46a:	4501                	li	a0,0
     46c:	4d4010ef          	jal	ra,1940 <LCD_Fill>
     470:	60b000ef          	jal	ra,127a <key_init>
     474:	05f00593          	li	a1,95
     478:	3e800513          	li	a0,1000
     47c:	3d5d                	jal	332 <Tim3_Init>
     47e:	00003537          	lui	a0,0x3
     482:	4711                	li	a4,4
     484:	7f450513          	addi	a0,a0,2036 # 37f4 <_read+0x60>
     488:	80e18ea3          	sb	a4,-2019(gp) # 200000cd <task_num>
     48c:	25c020ef          	jal	ra,26e8 <puts>
     490:	0eb000ef          	jal	ra,d7a <BrewingControl_Start>
     494:	3705                	jal	3b4 <scheduler_run>
     496:	bffd                	j	494 <main+0xa4>

00000498 <SystemInit>:
     498:	400217b7          	lui	a5,0x40021
     49c:	4398                	lw	a4,0(a5)
     49e:	f0ff06b7          	lui	a3,0xf0ff0
     4a2:	1141                	addi	sp,sp,-16
     4a4:	00176713          	ori	a4,a4,1
     4a8:	c398                	sw	a4,0(a5)
     4aa:	43d8                	lw	a4,4(a5)
     4ac:	00020637          	lui	a2,0x20
     4b0:	8f75                	and	a4,a4,a3
     4b2:	c3d8                	sw	a4,4(a5)
     4b4:	4398                	lw	a4,0(a5)
     4b6:	fef706b7          	lui	a3,0xfef70
     4ba:	16fd                	addi	a3,a3,-1
     4bc:	8f75                	and	a4,a4,a3
     4be:	c398                	sw	a4,0(a5)
     4c0:	4398                	lw	a4,0(a5)
     4c2:	fffc06b7          	lui	a3,0xfffc0
     4c6:	16fd                	addi	a3,a3,-1
     4c8:	8f75                	and	a4,a4,a3
     4ca:	c398                	sw	a4,0(a5)
     4cc:	43d8                	lw	a4,4(a5)
     4ce:	ff0106b7          	lui	a3,0xff010
     4d2:	16fd                	addi	a3,a3,-1
     4d4:	8f75                	and	a4,a4,a3
     4d6:	c3d8                	sw	a4,4(a5)
     4d8:	4398                	lw	a4,0(a5)
     4da:	ec0006b7          	lui	a3,0xec000
     4de:	16fd                	addi	a3,a3,-1
     4e0:	8f75                	and	a4,a4,a3
     4e2:	c398                	sw	a4,0(a5)
     4e4:	00ff0737          	lui	a4,0xff0
     4e8:	c798                	sw	a4,8(a5)
     4ea:	0207a623          	sw	zero,44(a5) # 4002102c <_eusrstack+0x2001902c>
     4ee:	c402                	sw	zero,8(sp)
     4f0:	c602                	sw	zero,12(sp)
     4f2:	4398                	lw	a4,0(a5)
     4f4:	66c1                	lui	a3,0x10
     4f6:	8f55                	or	a4,a4,a3
     4f8:	c398                	sw	a4,0(a5)
     4fa:	400216b7          	lui	a3,0x40021
     4fe:	6705                	lui	a4,0x1
     500:	429c                	lw	a5,0(a3)
     502:	8ff1                	and	a5,a5,a2
     504:	c63e                	sw	a5,12(sp)
     506:	47a2                	lw	a5,8(sp)
     508:	0785                	addi	a5,a5,1
     50a:	c43e                	sw	a5,8(sp)
     50c:	47b2                	lw	a5,12(sp)
     50e:	e781                	bnez	a5,516 <SystemInit+0x7e>
     510:	47a2                	lw	a5,8(sp)
     512:	fee797e3          	bne	a5,a4,500 <SystemInit+0x68>
     516:	400217b7          	lui	a5,0x40021
     51a:	439c                	lw	a5,0(a5)
     51c:	00e79713          	slli	a4,a5,0xe
     520:	06075763          	bgez	a4,58e <SystemInit+0xf6>
     524:	4785                	li	a5,1
     526:	c63e                	sw	a5,12(sp)
     528:	4732                	lw	a4,12(sp)
     52a:	4785                	li	a5,1
     52c:	04f71f63          	bne	a4,a5,58a <SystemInit+0xf2>
     530:	400217b7          	lui	a5,0x40021
     534:	43d8                	lw	a4,4(a5)
     536:	ffc106b7          	lui	a3,0xffc10
     53a:	16fd                	addi	a3,a3,-1
     53c:	c3d8                	sw	a4,4(a5)
     53e:	43d8                	lw	a4,4(a5)
     540:	c3d8                	sw	a4,4(a5)
     542:	43d8                	lw	a4,4(a5)
     544:	40076713          	ori	a4,a4,1024
     548:	c3d8                	sw	a4,4(a5)
     54a:	43d8                	lw	a4,4(a5)
     54c:	8f75                	and	a4,a4,a3
     54e:	c3d8                	sw	a4,4(a5)
     550:	43d8                	lw	a4,4(a5)
     552:	002906b7          	lui	a3,0x290
     556:	8f55                	or	a4,a4,a3
     558:	c3d8                	sw	a4,4(a5)
     55a:	4398                	lw	a4,0(a5)
     55c:	010006b7          	lui	a3,0x1000
     560:	8f55                	or	a4,a4,a3
     562:	c398                	sw	a4,0(a5)
     564:	4398                	lw	a4,0(a5)
     566:	00671693          	slli	a3,a4,0x6
     56a:	fe06dde3          	bgez	a3,564 <SystemInit+0xcc>
     56e:	43d8                	lw	a4,4(a5)
     570:	400216b7          	lui	a3,0x40021
     574:	9b71                	andi	a4,a4,-4
     576:	c3d8                	sw	a4,4(a5)
     578:	43d8                	lw	a4,4(a5)
     57a:	00276713          	ori	a4,a4,2
     57e:	c3d8                	sw	a4,4(a5)
     580:	4721                	li	a4,8
     582:	42dc                	lw	a5,4(a3)
     584:	8bb1                	andi	a5,a5,12
     586:	fee79ee3          	bne	a5,a4,582 <SystemInit+0xea>
     58a:	0141                	addi	sp,sp,16
     58c:	8082                	ret
     58e:	c602                	sw	zero,12(sp)
     590:	bf61                	j	528 <SystemInit+0x90>

00000592 <SystemCoreClockUpdate>:
     592:	400216b7          	lui	a3,0x40021
     596:	42d8                	lw	a4,4(a3)
     598:	200007b7          	lui	a5,0x20000
     59c:	4611                	li	a2,4
     59e:	8b31                	andi	a4,a4,12
     5a0:	0b078793          	addi	a5,a5,176 # 200000b0 <SystemCoreClock>
     5a4:	00c70563          	beq	a4,a2,5ae <SystemCoreClockUpdate+0x1c>
     5a8:	4621                	li	a2,8
     5aa:	02c70863          	beq	a4,a2,5da <SystemCoreClockUpdate+0x48>
     5ae:	007a1737          	lui	a4,0x7a1
     5b2:	20070713          	addi	a4,a4,512 # 7a1200 <_data_lma+0x79d680>
     5b6:	c398                	sw	a4,0(a5)
     5b8:	40021737          	lui	a4,0x40021
     5bc:	4358                	lw	a4,4(a4)
     5be:	8311                	srli	a4,a4,0x4
     5c0:	00f77693          	andi	a3,a4,15
     5c4:	20000737          	lui	a4,0x20000
     5c8:	03070713          	addi	a4,a4,48 # 20000030 <AHBPrescTable>
     5cc:	9736                	add	a4,a4,a3
     5ce:	2314                	lbu	a3,0(a4)
     5d0:	4398                	lw	a4,0(a5)
     5d2:	00d75733          	srl	a4,a4,a3
     5d6:	c398                	sw	a4,0(a5)
     5d8:	8082                	ret
     5da:	42d8                	lw	a4,4(a3)
     5dc:	42d4                	lw	a3,4(a3)
     5de:	6641                	lui	a2,0x10
     5e0:	8349                	srli	a4,a4,0x12
     5e2:	8b3d                	andi	a4,a4,15
     5e4:	8ef1                	and	a3,a3,a2
     5e6:	00270613          	addi	a2,a4,2
     5ea:	cf15                	beqz	a4,626 <SystemCoreClockUpdate+0x94>
     5ec:	473d                	li	a4,15
     5ee:	02e60f63          	beq	a2,a4,62c <SystemCoreClockUpdate+0x9a>
     5f2:	4741                	li	a4,16
     5f4:	02e60f63          	beq	a2,a4,632 <SystemCoreClockUpdate+0xa0>
     5f8:	4745                	li	a4,17
     5fa:	4581                	li	a1,0
     5fc:	00e61363          	bne	a2,a4,602 <SystemCoreClockUpdate+0x70>
     600:	4641                	li	a2,16
     602:	e2a1                	bnez	a3,642 <SystemCoreClockUpdate+0xb0>
     604:	40024737          	lui	a4,0x40024
     608:	80072703          	lw	a4,-2048(a4) # 40023800 <_eusrstack+0x2001b800>
     60c:	8b41                	andi	a4,a4,16
     60e:	c70d                	beqz	a4,638 <SystemCoreClockUpdate+0xa6>
     610:	007a1737          	lui	a4,0x7a1
     614:	20070713          	addi	a4,a4,512 # 7a1200 <_data_lma+0x79d680>
     618:	02c70633          	mul	a2,a4,a2
     61c:	c390                	sw	a2,0(a5)
     61e:	ddc9                	beqz	a1,5b8 <SystemCoreClockUpdate+0x26>
     620:	4398                	lw	a4,0(a5)
     622:	8305                	srli	a4,a4,0x1
     624:	bf49                	j	5b6 <SystemCoreClockUpdate+0x24>
     626:	4581                	li	a1,0
     628:	4649                	li	a2,18
     62a:	bfe1                	j	602 <SystemCoreClockUpdate+0x70>
     62c:	4585                	li	a1,1
     62e:	4635                	li	a2,13
     630:	bfc9                	j	602 <SystemCoreClockUpdate+0x70>
     632:	4581                	li	a1,0
     634:	463d                	li	a2,15
     636:	b7f1                	j	602 <SystemCoreClockUpdate+0x70>
     638:	003d1737          	lui	a4,0x3d1
     63c:	90070713          	addi	a4,a4,-1792 # 3d0900 <_data_lma+0x3ccd80>
     640:	bfe1                	j	618 <SystemCoreClockUpdate+0x86>
     642:	40021537          	lui	a0,0x40021
     646:	5558                	lw	a4,44(a0)
     648:	00f71693          	slli	a3,a4,0xf
     64c:	5558                	lw	a4,44(a0)
     64e:	0406df63          	bgez	a3,6ac <SystemCoreClockUpdate+0x11a>
     652:	8311                	srli	a4,a4,0x4
     654:	8b3d                	andi	a4,a4,15
     656:	00170693          	addi	a3,a4,1
     65a:	007a1737          	lui	a4,0x7a1
     65e:	20070713          	addi	a4,a4,512 # 7a1200 <_data_lma+0x79d680>
     662:	02d75733          	divu	a4,a4,a3
     666:	c398                	sw	a4,0(a5)
     668:	5554                	lw	a3,44(a0)
     66a:	82a1                	srli	a3,a3,0x8
     66c:	8abd                	andi	a3,a3,15
     66e:	e28d                	bnez	a3,690 <SystemCoreClockUpdate+0xfe>
     670:	4695                	li	a3,5
     672:	02d70733          	mul	a4,a4,a3
     676:	8305                	srli	a4,a4,0x1
     678:	c398                	sw	a4,0(a5)
     67a:	40021737          	lui	a4,0x40021
     67e:	5758                	lw	a4,44(a4)
     680:	4394                	lw	a3,0(a5)
     682:	8b3d                	andi	a4,a4,15
     684:	0705                	addi	a4,a4,1
     686:	02e6d733          	divu	a4,a3,a4
     68a:	c398                	sw	a4,0(a5)
     68c:	4398                	lw	a4,0(a5)
     68e:	b769                	j	618 <SystemCoreClockUpdate+0x86>
     690:	4505                	li	a0,1
     692:	00a69463          	bne	a3,a0,69a <SystemCoreClockUpdate+0x108>
     696:	46e5                	li	a3,25
     698:	bfe9                	j	672 <SystemCoreClockUpdate+0xe0>
     69a:	453d                	li	a0,15
     69c:	00a69663          	bne	a3,a0,6a8 <SystemCoreClockUpdate+0x116>
     6a0:	46d1                	li	a3,20
     6a2:	02e68733          	mul	a4,a3,a4
     6a6:	bfc9                	j	678 <SystemCoreClockUpdate+0xe6>
     6a8:	0689                	addi	a3,a3,2
     6aa:	bfe5                	j	6a2 <SystemCoreClockUpdate+0x110>
     6ac:	8b3d                	andi	a4,a4,15
     6ae:	00170693          	addi	a3,a4,1 # 40021001 <_eusrstack+0x20019001>
     6b2:	007a1737          	lui	a4,0x7a1
     6b6:	20070713          	addi	a4,a4,512 # 7a1200 <_data_lma+0x79d680>
     6ba:	02d75733          	divu	a4,a4,a3
     6be:	b7f1                	j	68a <SystemCoreClockUpdate+0xf8>

000006c0 <ADC1_2_IRQHandler>:
     6c0:	a001                	j	6c0 <ADC1_2_IRQHandler>

000006c2 <handle_reset>:
     6c2:	20000197          	auipc	gp,0x20000
     6c6:	1ee18193          	addi	gp,gp,494 # 200008b0 <__global_pointer$>
     6ca:	20008117          	auipc	sp,0x20008
     6ce:	93610113          	addi	sp,sp,-1738 # 20008000 <_eusrstack>
     6d2:	00003517          	auipc	a0,0x3
     6d6:	4ae50513          	addi	a0,a0,1198 # 3b80 <_data_lma>
     6da:	20000597          	auipc	a1,0x20000
     6de:	92658593          	addi	a1,a1,-1754 # 20000000 <_data_vma>
     6e2:	81818613          	addi	a2,gp,-2024 # 200000c8 <_edata>
     6e6:	00c5fa63          	bgeu	a1,a2,6fa <handle_reset+0x38>
     6ea:	00052283          	lw	t0,0(a0)
     6ee:	0055a023          	sw	t0,0(a1)
     6f2:	0511                	addi	a0,a0,4
     6f4:	0591                	addi	a1,a1,4
     6f6:	fec5eae3          	bltu	a1,a2,6ea <handle_reset+0x28>
     6fa:	81818513          	addi	a0,gp,-2024 # 200000c8 <_edata>
     6fe:	88418593          	addi	a1,gp,-1916 # 20000134 <_ebss>
     702:	00b57763          	bgeu	a0,a1,710 <handle_reset+0x4e>
     706:	00052023          	sw	zero,0(a0)
     70a:	0511                	addi	a0,a0,4
     70c:	feb56de3          	bltu	a0,a1,706 <handle_reset+0x44>
     710:	42fd                	li	t0,31
     712:	bc029073          	csrw	0xbc0,t0
     716:	42ad                	li	t0,11
     718:	80429073          	csrw	0x804,t0
     71c:	000062b7          	lui	t0,0x6
     720:	08828293          	addi	t0,t0,136 # 6088 <_data_lma+0x2508>
     724:	30029073          	csrw	mstatus,t0
     728:	00000297          	auipc	t0,0x0
     72c:	8dc28293          	addi	t0,t0,-1828 # 4 <_einit>
     730:	0032e293          	ori	t0,t0,3
     734:	30529073          	csrw	mtvec,t0
     738:	d61ff0ef          	jal	ra,498 <SystemInit>
     73c:	00000297          	auipc	t0,0x0
     740:	cb428293          	addi	t0,t0,-844 # 3f0 <main>
     744:	34129073          	csrw	mepc,t0
     748:	30200073          	mret

0000074c <GPIO_Init>:
     74c:	459c                	lw	a5,8(a1)
     74e:	0107f713          	andi	a4,a5,16
     752:	00f7f813          	andi	a6,a5,15
     756:	c701                	beqz	a4,75e <GPIO_Init+0x12>
     758:	41d8                	lw	a4,4(a1)
     75a:	00e86833          	or	a6,a6,a4
     75e:	218e                	lhu	a1,0(a1)
     760:	0ff5f713          	andi	a4,a1,255
     764:	c339                	beqz	a4,7aa <GPIO_Init+0x5e>
     766:	4118                	lw	a4,0(a0)
     768:	4681                	li	a3,0
     76a:	4e85                	li	t4,1
     76c:	4f3d                	li	t5,15
     76e:	02800f93          	li	t6,40
     772:	04800293          	li	t0,72
     776:	4e21                	li	t3,8
     778:	00de9633          	sll	a2,t4,a3
     77c:	00c5f8b3          	and	a7,a1,a2
     780:	03161163          	bne	a2,a7,7a2 <GPIO_Init+0x56>
     784:	00269893          	slli	a7,a3,0x2
     788:	011f1333          	sll	t1,t5,a7
     78c:	fff34313          	not	t1,t1
     790:	00e37733          	and	a4,t1,a4
     794:	011818b3          	sll	a7,a6,a7
     798:	00e8e733          	or	a4,a7,a4
     79c:	05f79f63          	bne	a5,t6,7fa <GPIO_Init+0xae>
     7a0:	c950                	sw	a2,20(a0)
     7a2:	0685                	addi	a3,a3,1
     7a4:	fdc69ae3          	bne	a3,t3,778 <GPIO_Init+0x2c>
     7a8:	c118                	sw	a4,0(a0)
     7aa:	0ff00713          	li	a4,255
     7ae:	04b77563          	bgeu	a4,a1,7f8 <GPIO_Init+0xac>
     7b2:	4154                	lw	a3,4(a0)
     7b4:	4621                	li	a2,8
     7b6:	4e85                	li	t4,1
     7b8:	4f3d                	li	t5,15
     7ba:	02800f93          	li	t6,40
     7be:	04800293          	li	t0,72
     7c2:	4e41                	li	t3,16
     7c4:	00ce98b3          	sll	a7,t4,a2
     7c8:	0115f733          	and	a4,a1,a7
     7cc:	02e89263          	bne	a7,a4,7f0 <GPIO_Init+0xa4>
     7d0:	00261713          	slli	a4,a2,0x2
     7d4:	1701                	addi	a4,a4,-32
     7d6:	00ef1333          	sll	t1,t5,a4
     7da:	fff34313          	not	t1,t1
     7de:	00d376b3          	and	a3,t1,a3
     7e2:	00e81733          	sll	a4,a6,a4
     7e6:	8ed9                	or	a3,a3,a4
     7e8:	01f79d63          	bne	a5,t6,802 <__stack_size+0x2>
     7ec:	01152a23          	sw	a7,20(a0)
     7f0:	0605                	addi	a2,a2,1
     7f2:	fdc619e3          	bne	a2,t3,7c4 <GPIO_Init+0x78>
     7f6:	c154                	sw	a3,4(a0)
     7f8:	8082                	ret
     7fa:	fa5794e3          	bne	a5,t0,7a2 <GPIO_Init+0x56>
     7fe:	c910                	sw	a2,16(a0)
     800:	b74d                	j	7a2 <GPIO_Init+0x56>
     802:	fe5797e3          	bne	a5,t0,7f0 <GPIO_Init+0xa4>
     806:	01152823          	sw	a7,16(a0)
     80a:	b7dd                	j	7f0 <GPIO_Init+0xa4>

0000080c <GPIO_ReadInputDataBit>:
     80c:	4508                	lw	a0,8(a0)
     80e:	8d6d                	and	a0,a0,a1
     810:	00a03533          	snez	a0,a0
     814:	8082                	ret

00000816 <GPIO_SetBits>:
     816:	c90c                	sw	a1,16(a0)
     818:	8082                	ret

0000081a <GPIO_ResetBits>:
     81a:	c94c                	sw	a1,20(a0)
     81c:	8082                	ret

0000081e <NVIC_PriorityGroupConfig>:
     81e:	82a1a223          	sw	a0,-2012(gp) # 200000d4 <NVIC_Priority_Group>
     822:	8082                	ret

00000824 <NVIC_Init>:
     824:	8241a703          	lw	a4,-2012(gp) # 200000d4 <NVIC_Priority_Group>
     828:	4789                	li	a5,2
     82a:	2110                	lbu	a2,0(a0)
     82c:	02f71163          	bne	a4,a5,84e <NVIC_Init+0x2a>
     830:	3114                	lbu	a3,1(a0)
     832:	478d                	li	a5,3
     834:	00d7ed63          	bltu	a5,a3,84e <NVIC_Init+0x2a>
     838:	213c                	lbu	a5,2(a0)
     83a:	069a                	slli	a3,a3,0x6
     83c:	e000e737          	lui	a4,0xe000e
     840:	0796                	slli	a5,a5,0x5
     842:	8fd5                	or	a5,a5,a3
     844:	0ff7f793          	andi	a5,a5,255
     848:	9732                	add	a4,a4,a2
     84a:	40f70023          	sb	a5,1024(a4) # e000e400 <_eusrstack+0xc0006400>
     84e:	4154                	lw	a3,4(a0)
     850:	4705                	li	a4,1
     852:	00565793          	srli	a5,a2,0x5
     856:	00c71733          	sll	a4,a4,a2
     85a:	ca89                	beqz	a3,86c <NVIC_Init+0x48>
     85c:	04078793          	addi	a5,a5,64
     860:	078a                	slli	a5,a5,0x2
     862:	e000e6b7          	lui	a3,0xe000e
     866:	97b6                	add	a5,a5,a3
     868:	c398                	sw	a4,0(a5)
     86a:	8082                	ret
     86c:	06078793          	addi	a5,a5,96
     870:	bfc5                	j	860 <NVIC_Init+0x3c>

00000872 <RCC_GetClocksFreq>:
     872:	40021737          	lui	a4,0x40021
     876:	435c                	lw	a5,4(a4)
     878:	4691                	li	a3,4
     87a:	8bb1                	andi	a5,a5,12
     87c:	00d78563          	beq	a5,a3,886 <RCC_GetClocksFreq+0x14>
     880:	46a1                	li	a3,8
     882:	06d78263          	beq	a5,a3,8e6 <RCC_GetClocksFreq+0x74>
     886:	007a17b7          	lui	a5,0x7a1
     88a:	20078793          	addi	a5,a5,512 # 7a1200 <_data_lma+0x79d680>
     88e:	c11c                	sw	a5,0(a0)
     890:	40021637          	lui	a2,0x40021
     894:	425c                	lw	a5,4(a2)
     896:	20000737          	lui	a4,0x20000
     89a:	04070713          	addi	a4,a4,64 # 20000040 <APBAHBPrescTable>
     89e:	8391                	srli	a5,a5,0x4
     8a0:	8bbd                	andi	a5,a5,15
     8a2:	97ba                	add	a5,a5,a4
     8a4:	2394                	lbu	a3,0(a5)
     8a6:	411c                	lw	a5,0(a0)
     8a8:	00d7d7b3          	srl	a5,a5,a3
     8ac:	c15c                	sw	a5,4(a0)
     8ae:	4254                	lw	a3,4(a2)
     8b0:	82a1                	srli	a3,a3,0x8
     8b2:	8a9d                	andi	a3,a3,7
     8b4:	96ba                	add	a3,a3,a4
     8b6:	2294                	lbu	a3,0(a3)
     8b8:	00d7d6b3          	srl	a3,a5,a3
     8bc:	c514                	sw	a3,8(a0)
     8be:	4254                	lw	a3,4(a2)
     8c0:	82ad                	srli	a3,a3,0xb
     8c2:	8a9d                	andi	a3,a3,7
     8c4:	9736                	add	a4,a4,a3
     8c6:	2318                	lbu	a4,0(a4)
     8c8:	00e7d7b3          	srl	a5,a5,a4
     8cc:	c55c                	sw	a5,12(a0)
     8ce:	4258                	lw	a4,4(a2)
     8d0:	8339                	srli	a4,a4,0xe
     8d2:	00377693          	andi	a3,a4,3
     8d6:	80418713          	addi	a4,gp,-2044 # 200000b4 <ADCPrescTable>
     8da:	9736                	add	a4,a4,a3
     8dc:	2318                	lbu	a4,0(a4)
     8de:	02e7d7b3          	divu	a5,a5,a4
     8e2:	c91c                	sw	a5,16(a0)
     8e4:	8082                	ret
     8e6:	435c                	lw	a5,4(a4)
     8e8:	4358                	lw	a4,4(a4)
     8ea:	66c1                	lui	a3,0x10
     8ec:	83c9                	srli	a5,a5,0x12
     8ee:	8f75                	and	a4,a4,a3
     8f0:	1ffff6b7          	lui	a3,0x1ffff
     8f4:	70c6a683          	lw	a3,1804(a3) # 1ffff70c <_data_lma+0x1fffbb8c>
     8f8:	8bbd                	andi	a5,a5,15
     8fa:	0789                	addi	a5,a5,2
     8fc:	01169613          	slli	a2,a3,0x11
     900:	00064863          	bltz	a2,910 <RCC_GetClocksFreq+0x9e>
     904:	46c5                	li	a3,17
     906:	4601                	li	a2,0
     908:	02d79263          	bne	a5,a3,92c <RCC_GetClocksFreq+0xba>
     90c:	47c9                	li	a5,18
     90e:	a839                	j	92c <RCC_GetClocksFreq+0xba>
     910:	4689                	li	a3,2
     912:	02d78f63          	beq	a5,a3,950 <RCC_GetClocksFreq+0xde>
     916:	46bd                	li	a3,15
     918:	02d78e63          	beq	a5,a3,954 <RCC_GetClocksFreq+0xe2>
     91c:	46c1                	li	a3,16
     91e:	02d78e63          	beq	a5,a3,95a <RCC_GetClocksFreq+0xe8>
     922:	46c5                	li	a3,17
     924:	4601                	li	a2,0
     926:	00d79363          	bne	a5,a3,92c <RCC_GetClocksFreq+0xba>
     92a:	47c1                	li	a5,16
     92c:	ef1d                	bnez	a4,96a <RCC_GetClocksFreq+0xf8>
     92e:	40024737          	lui	a4,0x40024
     932:	80072703          	lw	a4,-2048(a4) # 40023800 <_eusrstack+0x2001b800>
     936:	8b41                	andi	a4,a4,16
     938:	c705                	beqz	a4,960 <RCC_GetClocksFreq+0xee>
     93a:	007a1737          	lui	a4,0x7a1
     93e:	20070713          	addi	a4,a4,512 # 7a1200 <_data_lma+0x79d680>
     942:	02f707b3          	mul	a5,a4,a5
     946:	c11c                	sw	a5,0(a0)
     948:	d621                	beqz	a2,890 <RCC_GetClocksFreq+0x1e>
     94a:	411c                	lw	a5,0(a0)
     94c:	8385                	srli	a5,a5,0x1
     94e:	b781                	j	88e <RCC_GetClocksFreq+0x1c>
     950:	4601                	li	a2,0
     952:	bf6d                	j	90c <RCC_GetClocksFreq+0x9a>
     954:	4605                	li	a2,1
     956:	47b5                	li	a5,13
     958:	bfd1                	j	92c <RCC_GetClocksFreq+0xba>
     95a:	4601                	li	a2,0
     95c:	47bd                	li	a5,15
     95e:	b7f9                	j	92c <RCC_GetClocksFreq+0xba>
     960:	003d1737          	lui	a4,0x3d1
     964:	90070713          	addi	a4,a4,-1792 # 3d0900 <_data_lma+0x3ccd80>
     968:	bfe9                	j	942 <RCC_GetClocksFreq+0xd0>
     96a:	400215b7          	lui	a1,0x40021
     96e:	55d8                	lw	a4,44(a1)
     970:	00f71693          	slli	a3,a4,0xf
     974:	55d8                	lw	a4,44(a1)
     976:	0406df63          	bgez	a3,9d4 <RCC_GetClocksFreq+0x162>
     97a:	8311                	srli	a4,a4,0x4
     97c:	8b3d                	andi	a4,a4,15
     97e:	00170693          	addi	a3,a4,1
     982:	007a1737          	lui	a4,0x7a1
     986:	20070713          	addi	a4,a4,512 # 7a1200 <_data_lma+0x79d680>
     98a:	02d75733          	divu	a4,a4,a3
     98e:	c118                	sw	a4,0(a0)
     990:	55d4                	lw	a3,44(a1)
     992:	82a1                	srli	a3,a3,0x8
     994:	8abd                	andi	a3,a3,15
     996:	e28d                	bnez	a3,9b8 <RCC_GetClocksFreq+0x146>
     998:	4695                	li	a3,5
     99a:	02d70733          	mul	a4,a4,a3
     99e:	8305                	srli	a4,a4,0x1
     9a0:	c118                	sw	a4,0(a0)
     9a2:	40021737          	lui	a4,0x40021
     9a6:	5758                	lw	a4,44(a4)
     9a8:	4114                	lw	a3,0(a0)
     9aa:	8b3d                	andi	a4,a4,15
     9ac:	0705                	addi	a4,a4,1
     9ae:	02e6d733          	divu	a4,a3,a4
     9b2:	c118                	sw	a4,0(a0)
     9b4:	4118                	lw	a4,0(a0)
     9b6:	b771                	j	942 <RCC_GetClocksFreq+0xd0>
     9b8:	4585                	li	a1,1
     9ba:	00b69463          	bne	a3,a1,9c2 <RCC_GetClocksFreq+0x150>
     9be:	46e5                	li	a3,25
     9c0:	bfe9                	j	99a <RCC_GetClocksFreq+0x128>
     9c2:	45bd                	li	a1,15
     9c4:	00b69663          	bne	a3,a1,9d0 <RCC_GetClocksFreq+0x15e>
     9c8:	46d1                	li	a3,20
     9ca:	02e68733          	mul	a4,a3,a4
     9ce:	bfc9                	j	9a0 <RCC_GetClocksFreq+0x12e>
     9d0:	0689                	addi	a3,a3,2
     9d2:	bfe5                	j	9ca <RCC_GetClocksFreq+0x158>
     9d4:	8b3d                	andi	a4,a4,15
     9d6:	00170693          	addi	a3,a4,1 # 40021001 <_eusrstack+0x20019001>
     9da:	007a1737          	lui	a4,0x7a1
     9de:	20070713          	addi	a4,a4,512 # 7a1200 <_data_lma+0x79d680>
     9e2:	02d75733          	divu	a4,a4,a3
     9e6:	b7f1                	j	9b2 <RCC_GetClocksFreq+0x140>

000009e8 <RCC_APB2PeriphClockCmd>:
     9e8:	c599                	beqz	a1,9f6 <RCC_APB2PeriphClockCmd+0xe>
     9ea:	40021737          	lui	a4,0x40021
     9ee:	4f1c                	lw	a5,24(a4)
     9f0:	8d5d                	or	a0,a0,a5
     9f2:	cf08                	sw	a0,24(a4)
     9f4:	8082                	ret
     9f6:	400217b7          	lui	a5,0x40021
     9fa:	4f98                	lw	a4,24(a5)
     9fc:	fff54513          	not	a0,a0
     a00:	8d79                	and	a0,a0,a4
     a02:	cf88                	sw	a0,24(a5)
     a04:	8082                	ret

00000a06 <RCC_APB1PeriphClockCmd>:
     a06:	c599                	beqz	a1,a14 <RCC_APB1PeriphClockCmd+0xe>
     a08:	40021737          	lui	a4,0x40021
     a0c:	4f5c                	lw	a5,28(a4)
     a0e:	8d5d                	or	a0,a0,a5
     a10:	cf48                	sw	a0,28(a4)
     a12:	8082                	ret
     a14:	400217b7          	lui	a5,0x40021
     a18:	4fd8                	lw	a4,28(a5)
     a1a:	fff54513          	not	a0,a0
     a1e:	8d79                	and	a0,a0,a4
     a20:	cfc8                	sw	a0,28(a5)
     a22:	8082                	ret

00000a24 <SPI_Init>:
     a24:	211a                	lhu	a4,0(a0)
     a26:	678d                	lui	a5,0x3
     a28:	04078793          	addi	a5,a5,64 # 3040 <_vfiprintf_r+0x102>
     a2c:	21b6                	lhu	a3,2(a1)
     a2e:	8f7d                	and	a4,a4,a5
     a30:	219e                	lhu	a5,0(a1)
     a32:	8fd5                	or	a5,a5,a3
     a34:	21d6                	lhu	a3,4(a1)
     a36:	8fd5                	or	a5,a5,a3
     a38:	21f6                	lhu	a3,6(a1)
     a3a:	8fd5                	or	a5,a5,a3
     a3c:	2596                	lhu	a3,8(a1)
     a3e:	8fd5                	or	a5,a5,a3
     a40:	25b6                	lhu	a3,10(a1)
     a42:	8fd5                	or	a5,a5,a3
     a44:	25d6                	lhu	a3,12(a1)
     a46:	8fd5                	or	a5,a5,a3
     a48:	25f6                	lhu	a3,14(a1)
     a4a:	8fd5                	or	a5,a5,a3
     a4c:	8fd9                	or	a5,a5,a4
     a4e:	a11e                	sh	a5,0(a0)
     a50:	2d5a                	lhu	a4,28(a0)
     a52:	77fd                	lui	a5,0xfffff
     a54:	7ff78793          	addi	a5,a5,2047 # fffff7ff <_eusrstack+0xdfff77ff>
     a58:	8ff9                	and	a5,a5,a4
     a5a:	ad5e                	sh	a5,28(a0)
     a5c:	299e                	lhu	a5,16(a1)
     a5e:	a91e                	sh	a5,16(a0)
     a60:	8082                	ret

00000a62 <SPI_Cmd>:
     a62:	211e                	lhu	a5,0(a0)
     a64:	c589                	beqz	a1,a6e <SPI_Cmd+0xc>
     a66:	0407e793          	ori	a5,a5,64
     a6a:	a11e                	sh	a5,0(a0)
     a6c:	8082                	ret
     a6e:	07c2                	slli	a5,a5,0x10
     a70:	83c1                	srli	a5,a5,0x10
     a72:	fbf7f793          	andi	a5,a5,-65
     a76:	07c2                	slli	a5,a5,0x10
     a78:	83c1                	srli	a5,a5,0x10
     a7a:	bfc5                	j	a6a <SPI_Cmd+0x8>

00000a7c <SPI_I2S_SendData>:
     a7c:	a54e                	sh	a1,12(a0)
     a7e:	8082                	ret

00000a80 <SPI_I2S_ReceiveData>:
     a80:	254a                	lhu	a0,12(a0)
     a82:	8082                	ret

00000a84 <SPI_I2S_GetFlagStatus>:
     a84:	250a                	lhu	a0,8(a0)
     a86:	8d6d                	and	a0,a0,a1
     a88:	00a03533          	snez	a0,a0
     a8c:	8082                	ret

00000a8e <TIM_TimeBaseInit>:
     a8e:	211e                	lhu	a5,0(a0)
     a90:	40013737          	lui	a4,0x40013
     a94:	c0070693          	addi	a3,a4,-1024 # 40012c00 <_eusrstack+0x2000ac00>
     a98:	07c2                	slli	a5,a5,0x10
     a9a:	83c1                	srli	a5,a5,0x10
     a9c:	04d50063          	beq	a0,a3,adc <TIM_TimeBaseInit+0x4e>
     aa0:	400006b7          	lui	a3,0x40000
     aa4:	02d50c63          	beq	a0,a3,adc <TIM_TimeBaseInit+0x4e>
     aa8:	40068693          	addi	a3,a3,1024 # 40000400 <_eusrstack+0x1fff8400>
     aac:	02d50863          	beq	a0,a3,adc <TIM_TimeBaseInit+0x4e>
     ab0:	400016b7          	lui	a3,0x40001
     ab4:	80068613          	addi	a2,a3,-2048 # 40000800 <_eusrstack+0x1fff8800>
     ab8:	02c50263          	beq	a0,a2,adc <TIM_TimeBaseInit+0x4e>
     abc:	c0068693          	addi	a3,a3,-1024
     ac0:	00d50e63          	beq	a0,a3,adc <TIM_TimeBaseInit+0x4e>
     ac4:	40070713          	addi	a4,a4,1024
     ac8:	00e50a63          	beq	a0,a4,adc <TIM_TimeBaseInit+0x4e>
     acc:	40015737          	lui	a4,0x40015
     ad0:	c0070693          	addi	a3,a4,-1024 # 40014c00 <_eusrstack+0x2000cc00>
     ad4:	00d50463          	beq	a0,a3,adc <TIM_TimeBaseInit+0x4e>
     ad8:	00e51663          	bne	a0,a4,ae4 <TIM_TimeBaseInit+0x56>
     adc:	21ba                	lhu	a4,2(a1)
     ade:	f8f7f793          	andi	a5,a5,-113
     ae2:	8fd9                	or	a5,a5,a4
     ae4:	40001737          	lui	a4,0x40001
     ae8:	00e50c63          	beq	a0,a4,b00 <TIM_TimeBaseInit+0x72>
     aec:	40070713          	addi	a4,a4,1024 # 40001400 <_eusrstack+0x1fff9400>
     af0:	00e50863          	beq	a0,a4,b00 <TIM_TimeBaseInit+0x72>
     af4:	cff7f793          	andi	a5,a5,-769
     af8:	21fa                	lhu	a4,6(a1)
     afa:	07c2                	slli	a5,a5,0x10
     afc:	83c1                	srli	a5,a5,0x10
     afe:	8fd9                	or	a5,a5,a4
     b00:	a11e                	sh	a5,0(a0)
     b02:	21de                	lhu	a5,4(a1)
     b04:	b55e                	sh	a5,44(a0)
     b06:	219e                	lhu	a5,0(a1)
     b08:	b51e                	sh	a5,40(a0)
     b0a:	400137b7          	lui	a5,0x40013
     b0e:	c0078713          	addi	a4,a5,-1024 # 40012c00 <_eusrstack+0x2000ac00>
     b12:	00e50e63          	beq	a0,a4,b2e <TIM_TimeBaseInit+0xa0>
     b16:	40078793          	addi	a5,a5,1024
     b1a:	00f50a63          	beq	a0,a5,b2e <TIM_TimeBaseInit+0xa0>
     b1e:	400157b7          	lui	a5,0x40015
     b22:	c0078713          	addi	a4,a5,-1024 # 40014c00 <_eusrstack+0x2000cc00>
     b26:	00e50463          	beq	a0,a4,b2e <TIM_TimeBaseInit+0xa0>
     b2a:	00f51463          	bne	a0,a5,b32 <TIM_TimeBaseInit+0xa4>
     b2e:	259c                	lbu	a5,8(a1)
     b30:	b91e                	sh	a5,48(a0)
     b32:	4785                	li	a5,1
     b34:	a95e                	sh	a5,20(a0)
     b36:	8082                	ret

00000b38 <TIM_OC1Init>:
     b38:	311e                	lhu	a5,32(a0)
     b3a:	2192                	lhu	a2,0(a1)
     b3c:	0025d803          	lhu	a6,2(a1) # 40021002 <_eusrstack+0x20019002>
     b40:	07c2                	slli	a5,a5,0x10
     b42:	83c1                	srli	a5,a5,0x10
     b44:	9bf9                	andi	a5,a5,-2
     b46:	07c2                	slli	a5,a5,0x10
     b48:	83c1                	srli	a5,a5,0x10
     b4a:	b11e                	sh	a5,32(a0)
     b4c:	311e                	lhu	a5,32(a0)
     b4e:	2156                	lhu	a3,4(a0)
     b50:	2d1a                	lhu	a4,24(a0)
     b52:	07c2                	slli	a5,a5,0x10
     b54:	83c1                	srli	a5,a5,0x10
     b56:	0742                	slli	a4,a4,0x10
     b58:	8341                	srli	a4,a4,0x10
     b5a:	f8c77713          	andi	a4,a4,-116
     b5e:	8f51                	or	a4,a4,a2
     b60:	2592                	lhu	a2,8(a1)
     b62:	9bf5                	andi	a5,a5,-3
     b64:	06c2                	slli	a3,a3,0x10
     b66:	01066633          	or	a2,a2,a6
     b6a:	8fd1                	or	a5,a5,a2
     b6c:	40013637          	lui	a2,0x40013
     b70:	c0060813          	addi	a6,a2,-1024 # 40012c00 <_eusrstack+0x2000ac00>
     b74:	82c1                	srli	a3,a3,0x10
     b76:	01050e63          	beq	a0,a6,b92 <TIM_OC1Init+0x5a>
     b7a:	40060613          	addi	a2,a2,1024
     b7e:	00c50a63          	beq	a0,a2,b92 <TIM_OC1Init+0x5a>
     b82:	40015637          	lui	a2,0x40015
     b86:	c0060813          	addi	a6,a2,-1024 # 40014c00 <_eusrstack+0x2000cc00>
     b8a:	01050463          	beq	a0,a6,b92 <TIM_OC1Init+0x5a>
     b8e:	02c51063          	bne	a0,a2,bae <TIM_OC1Init+0x76>
     b92:	25b2                	lhu	a2,10(a1)
     b94:	9bdd                	andi	a5,a5,-9
     b96:	00e5d803          	lhu	a6,14(a1)
     b9a:	8fd1                	or	a5,a5,a2
     b9c:	21d2                	lhu	a2,4(a1)
     b9e:	9bed                	andi	a5,a5,-5
     ba0:	cff6f693          	andi	a3,a3,-769
     ba4:	8fd1                	or	a5,a5,a2
     ba6:	25d2                	lhu	a2,12(a1)
     ba8:	01066633          	or	a2,a2,a6
     bac:	8ed1                	or	a3,a3,a2
     bae:	a156                	sh	a3,4(a0)
     bb0:	ad1a                	sh	a4,24(a0)
     bb2:	21fa                	lhu	a4,6(a1)
     bb4:	b95a                	sh	a4,52(a0)
     bb6:	b11e                	sh	a5,32(a0)
     bb8:	8082                	ret

00000bba <TIM_Cmd>:
     bba:	211e                	lhu	a5,0(a0)
     bbc:	c589                	beqz	a1,bc6 <TIM_Cmd+0xc>
     bbe:	0017e793          	ori	a5,a5,1
     bc2:	a11e                	sh	a5,0(a0)
     bc4:	8082                	ret
     bc6:	07c2                	slli	a5,a5,0x10
     bc8:	83c1                	srli	a5,a5,0x10
     bca:	9bf9                	andi	a5,a5,-2
     bcc:	07c2                	slli	a5,a5,0x10
     bce:	83c1                	srli	a5,a5,0x10
     bd0:	bfcd                	j	bc2 <TIM_Cmd+0x8>

00000bd2 <TIM_ITConfig>:
     bd2:	255e                	lhu	a5,12(a0)
     bd4:	c601                	beqz	a2,bdc <TIM_ITConfig+0xa>
     bd6:	8ddd                	or	a1,a1,a5
     bd8:	a54e                	sh	a1,12(a0)
     bda:	8082                	ret
     bdc:	fff5c593          	not	a1,a1
     be0:	8dfd                	and	a1,a1,a5
     be2:	bfdd                	j	bd8 <TIM_ITConfig+0x6>

00000be4 <TIM_ARRPreloadConfig>:
     be4:	211e                	lhu	a5,0(a0)
     be6:	c589                	beqz	a1,bf0 <TIM_ARRPreloadConfig+0xc>
     be8:	0807e793          	ori	a5,a5,128
     bec:	a11e                	sh	a5,0(a0)
     bee:	8082                	ret
     bf0:	07c2                	slli	a5,a5,0x10
     bf2:	83c1                	srli	a5,a5,0x10
     bf4:	f7f7f793          	andi	a5,a5,-129
     bf8:	07c2                	slli	a5,a5,0x10
     bfa:	83c1                	srli	a5,a5,0x10
     bfc:	bfc5                	j	bec <TIM_ARRPreloadConfig+0x8>

00000bfe <TIM_OC1PreloadConfig>:
     bfe:	2d1e                	lhu	a5,24(a0)
     c00:	07c2                	slli	a5,a5,0x10
     c02:	83c1                	srli	a5,a5,0x10
     c04:	9bdd                	andi	a5,a5,-9
     c06:	8ddd                	or	a1,a1,a5
     c08:	ad0e                	sh	a1,24(a0)
     c0a:	8082                	ret

00000c0c <TIM_SetCompare1>:
     c0c:	b94e                	sh	a1,52(a0)
     c0e:	8082                	ret

00000c10 <TIM_GetITStatus>:
     c10:	291e                	lhu	a5,16(a0)
     c12:	254a                	lhu	a0,12(a0)
     c14:	8fed                	and	a5,a5,a1
     c16:	0542                	slli	a0,a0,0x10
     c18:	8141                	srli	a0,a0,0x10
     c1a:	c789                	beqz	a5,c24 <TIM_GetITStatus+0x14>
     c1c:	8d6d                	and	a0,a0,a1
     c1e:	00a03533          	snez	a0,a0
     c22:	8082                	ret
     c24:	4501                	li	a0,0
     c26:	8082                	ret

00000c28 <TIM_ClearITPendingBit>:
     c28:	fff5c593          	not	a1,a1
     c2c:	05c2                	slli	a1,a1,0x10
     c2e:	81c1                	srli	a1,a1,0x10
     c30:	a90e                	sh	a1,16(a0)
     c32:	8082                	ret

00000c34 <USART_Init>:
     c34:	dbcff2ef          	jal	t0,1f0 <__riscv_save_0>
     c38:	2916                	lhu	a3,16(a0)
     c3a:	77f5                	lui	a5,0xffffd
     c3c:	17fd                	addi	a5,a5,-1
     c3e:	8ff5                	and	a5,a5,a3
     c40:	21f6                	lhu	a3,6(a1)
     c42:	25da                	lhu	a4,12(a1)
     c44:	7179                	addi	sp,sp,-48
     c46:	8fd5                	or	a5,a5,a3
     c48:	a91e                	sh	a5,16(a0)
     c4a:	2556                	lhu	a3,12(a0)
     c4c:	77fd                	lui	a5,0xfffff
     c4e:	9f378793          	addi	a5,a5,-1549 # ffffe9f3 <_eusrstack+0xdfff69f3>
     c52:	8ff5                	and	a5,a5,a3
     c54:	21d6                	lhu	a3,4(a1)
     c56:	842a                	mv	s0,a0
     c58:	c62e                	sw	a1,12(sp)
     c5a:	8fd5                	or	a5,a5,a3
     c5c:	2596                	lhu	a3,8(a1)
     c5e:	8fd5                	or	a5,a5,a3
     c60:	25b6                	lhu	a3,10(a1)
     c62:	8fd5                	or	a5,a5,a3
     c64:	a55e                	sh	a5,12(a0)
     c66:	295e                	lhu	a5,20(a0)
     c68:	07c2                	slli	a5,a5,0x10
     c6a:	83c1                	srli	a5,a5,0x10
     c6c:	cff7f793          	andi	a5,a5,-769
     c70:	8fd9                	or	a5,a5,a4
     c72:	a95e                	sh	a5,20(a0)
     c74:	0868                	addi	a0,sp,28
     c76:	3ef5                	jal	872 <RCC_GetClocksFreq>
     c78:	400147b7          	lui	a5,0x40014
     c7c:	80078793          	addi	a5,a5,-2048 # 40013800 <_eusrstack+0x2000b800>
     c80:	45b2                	lw	a1,12(sp)
     c82:	02f41e63          	bne	s0,a5,cbe <USART_Init+0x8a>
     c86:	57a2                	lw	a5,40(sp)
     c88:	4765                	li	a4,25
     c8a:	02e787b3          	mul	a5,a5,a4
     c8e:	4198                	lw	a4,0(a1)
     c90:	06400693          	li	a3,100
     c94:	070a                	slli	a4,a4,0x2
     c96:	02e7d7b3          	divu	a5,a5,a4
     c9a:	02d7d733          	divu	a4,a5,a3
     c9e:	02d7f7b3          	remu	a5,a5,a3
     ca2:	0712                	slli	a4,a4,0x4
     ca4:	0792                	slli	a5,a5,0x4
     ca6:	03278793          	addi	a5,a5,50
     caa:	02d7d7b3          	divu	a5,a5,a3
     cae:	8bbd                	andi	a5,a5,15
     cb0:	8fd9                	or	a5,a5,a4
     cb2:	07c2                	slli	a5,a5,0x10
     cb4:	83c1                	srli	a5,a5,0x10
     cb6:	a41e                	sh	a5,8(s0)
     cb8:	6145                	addi	sp,sp,48
     cba:	d5aff06f          	j	214 <__riscv_restore_0>
     cbe:	5792                	lw	a5,36(sp)
     cc0:	b7e1                	j	c88 <USART_Init+0x54>

00000cc2 <USART_Cmd>:
     cc2:	c591                	beqz	a1,cce <USART_Cmd+0xc>
     cc4:	255e                	lhu	a5,12(a0)
     cc6:	6709                	lui	a4,0x2
     cc8:	8fd9                	or	a5,a5,a4
     cca:	a55e                	sh	a5,12(a0)
     ccc:	8082                	ret
     cce:	255a                	lhu	a4,12(a0)
     cd0:	77f9                	lui	a5,0xffffe
     cd2:	17fd                	addi	a5,a5,-1
     cd4:	8ff9                	and	a5,a5,a4
     cd6:	bfd5                	j	cca <USART_Cmd+0x8>

00000cd8 <USART_SendData>:
     cd8:	1ff5f593          	andi	a1,a1,511
     cdc:	a14e                	sh	a1,4(a0)
     cde:	8082                	ret

00000ce0 <USART_GetFlagStatus>:
     ce0:	210a                	lhu	a0,0(a0)
     ce2:	8d6d                	and	a0,a0,a1
     ce4:	00a03533          	snez	a0,a0
     ce8:	8082                	ret

00000cea <Delay_us>:
     cea:	82a1c783          	lbu	a5,-2006(gp) # 200000da <fac_us>
     cee:	e000f637          	lui	a2,0xe000f
     cf2:	02a78533          	mul	a0,a5,a0
     cf6:	e000f7b7          	lui	a5,0xe000f
     cfa:	0087a803          	lw	a6,8(a5) # e000f008 <_eusrstack+0xc0007008>
     cfe:	00c7a883          	lw	a7,12(a5)
     d02:	00862303          	lw	t1,8(a2) # e000f008 <_eusrstack+0xc0007008>
     d06:	00c62383          	lw	t2,12(a2)
     d0a:	410306b3          	sub	a3,t1,a6
     d0e:	00d33733          	sltu	a4,t1,a3
     d12:	00771463          	bne	a4,t2,d1a <Delay_us+0x30>
     d16:	fea6e6e3          	bltu	a3,a0,d02 <Delay_us+0x18>
     d1a:	8082                	ret

00000d1c <Delay_ms>:
     d1c:	8281d783          	lhu	a5,-2008(gp) # 200000d8 <fac_ms>
     d20:	e000f337          	lui	t1,0xe000f
     d24:	02a78533          	mul	a0,a5,a0
     d28:	e000f7b7          	lui	a5,0xe000f
     d2c:	0087a803          	lw	a6,8(a5) # e000f008 <_eusrstack+0xc0007008>
     d30:	00c7a883          	lw	a7,12(a5)
     d34:	41f55593          	srai	a1,a0,0x1f
     d38:	00832603          	lw	a2,8(t1) # e000f008 <_eusrstack+0xc0007008>
     d3c:	00c32683          	lw	a3,12(t1)
     d40:	41060733          	sub	a4,a2,a6
     d44:	00e637b3          	sltu	a5,a2,a4
     d48:	40f687b3          	sub	a5,a3,a5
     d4c:	feb7e6e3          	bltu	a5,a1,d38 <Delay_ms+0x1c>
     d50:	00f59463          	bne	a1,a5,d58 <Delay_ms+0x3c>
     d54:	fea762e3          	bltu	a4,a0,d38 <Delay_ms+0x1c>
     d58:	8082                	ret

00000d5a <BrewingControl_Init>:
     d5a:	84818793          	addi	a5,gp,-1976 # 200000f8 <g_brewing_ctrl>
     d5e:	00000713          	li	a4,0
     d62:	0007a023          	sw	zero,0(a5)
     d66:	0007a223          	sw	zero,4(a5)
     d6a:	0007a423          	sw	zero,8(a5)
     d6e:	0007a623          	sw	zero,12(a5)
     d72:	cb98                	sw	a4,16(a5)
     d74:	00079a23          	sh	zero,20(a5)
     d78:	8082                	ret

00000d7a <BrewingControl_Start>:
     d7a:	84818793          	addi	a5,gp,-1976 # 200000f8 <g_brewing_ctrl>
     d7e:	4398                	lw	a4,0(a5)
     d80:	ef09                	bnez	a4,d9a <BrewingControl_Start+0x20>
     d82:	c6eff2ef          	jal	t0,1f0 <__riscv_save_0>
     d86:	00004537          	lui	a0,0x4
     d8a:	4705                	li	a4,1
     d8c:	88450513          	addi	a0,a0,-1916 # 3884 <_read+0xf0>
     d90:	c398                	sw	a4,0(a5)
     d92:	157010ef          	jal	ra,26e8 <puts>
     d96:	c7eff06f          	j	214 <__riscv_restore_0>
     d9a:	8082                	ret

00000d9c <BrewingControl_KeyHandler>:
     d9c:	c54ff2ef          	jal	t0,1f0 <__riscv_save_0>
     da0:	84818413          	addi	s0,gp,-1976 # 200000f8 <g_brewing_ctrl>
     da4:	401c                	lw	a5,0(s0)
     da6:	470d                	li	a4,3
     da8:	04e78e63          	beq	a5,a4,e04 <BrewingControl_KeyHandler+0x68>
     dac:	00f76763          	bltu	a4,a5,dba <BrewingControl_KeyHandler+0x1e>
     db0:	4705                	li	a4,1
     db2:	02e78763          	beq	a5,a4,de0 <BrewingControl_KeyHandler+0x44>
     db6:	c5eff06f          	j	214 <__riscv_restore_0>
     dba:	4695                	li	a3,5
     dbc:	06d78c63          	beq	a5,a3,e34 <BrewingControl_KeyHandler+0x98>
     dc0:	4719                	li	a4,6
     dc2:	fee79ae3          	bne	a5,a4,db6 <BrewingControl_KeyHandler+0x1a>
     dc6:	4791                	li	a5,4
     dc8:	fef517e3          	bne	a0,a5,db6 <BrewingControl_KeyHandler+0x1a>
     dcc:	479d                	li	a5,7
     dce:	4501                	li	a0,0
     dd0:	c01c                	sw	a5,0(s0)
     dd2:	499000ef          	jal	ra,1a6a <stir>
     dd6:	00004537          	lui	a0,0x4
     dda:	87850513          	addi	a0,a0,-1928 # 3878 <_read+0xe4>
     dde:	a005                	j	dfe <BrewingControl_KeyHandler+0x62>
     de0:	fcf51be3          	bne	a0,a5,db6 <BrewingControl_KeyHandler+0x1a>
     de4:	4789                	li	a5,2
     de6:	c01c                	sw	a5,0(s0)
     de8:	8201a783          	lw	a5,-2016(gp) # 200000d0 <uwtick>
     dec:	4501                	li	a0,0
     dee:	4585                	li	a1,1
     df0:	c05c                	sw	a5,4(s0)
     df2:	563000ef          	jal	ra,1b54 <WaterPump_Control>
     df6:	00004537          	lui	a0,0x4
     dfa:	83850513          	addi	a0,a0,-1992 # 3838 <_read+0xa4>
     dfe:	0eb010ef          	jal	ra,26e8 <puts>
     e02:	bf55                	j	db6 <BrewingControl_KeyHandler+0x1a>
     e04:	4789                	li	a5,2
     e06:	faf518e3          	bne	a0,a5,db6 <BrewingControl_KeyHandler+0x1a>
     e0a:	4791                	li	a5,4
     e0c:	83818513          	addi	a0,gp,-1992 # 200000e8 <ds18b20>
     e10:	c01c                	sw	a5,0(s0)
     e12:	2e7d                	jal	11d0 <DS18B20_ReadRealtimeTemp>
     e14:	8381a783          	lw	a5,-1992(gp) # 200000e8 <ds18b20>
     e18:	c81c                	sw	a5,16(s0)
     e1a:	2efd                	jal	1218 <Heater_Start>
     e1c:	4808                	lw	a0,16(s0)
     e1e:	69c010ef          	jal	ra,24ba <__extendsfdf2>
     e22:	862a                	mv	a2,a0
     e24:	00004537          	lui	a0,0x4
     e28:	86ae                	mv	a3,a1
     e2a:	84c50513          	addi	a0,a0,-1972 # 384c <_read+0xb8>
     e2e:	7a6010ef          	jal	ra,25d4 <iprintf>
     e32:	b751                	j	db6 <BrewingControl_KeyHandler+0x1a>
     e34:	f8e511e3          	bne	a0,a4,db6 <BrewingControl_KeyHandler+0x1a>
     e38:	4799                	li	a5,6
     e3a:	4505                	li	a0,1
     e3c:	06400593          	li	a1,100
     e40:	c01c                	sw	a5,0(s0)
     e42:	3cb000ef          	jal	ra,1a0c <stir_360>
     e46:	00004537          	lui	a0,0x4
     e4a:	86c50513          	addi	a0,a0,-1940 # 386c <_read+0xd8>
     e4e:	bf45                	j	dfe <BrewingControl_KeyHandler+0x62>

00000e50 <BrewingControl_Task>:
     e50:	ba0ff2ef          	jal	t0,1f0 <__riscv_save_0>
     e54:	8201a483          	lw	s1,-2016(gp) # 200000d0 <uwtick>
     e58:	8481a783          	lw	a5,-1976(gp) # 200000f8 <g_brewing_ctrl>
     e5c:	471d                	li	a4,7
     e5e:	17f9                	addi	a5,a5,-2
     e60:	02f76e63          	bltu	a4,a5,e9c <BrewingControl_Task+0x4c>
     e64:	6711                	lui	a4,0x4
     e66:	078a                	slli	a5,a5,0x2
     e68:	81870713          	addi	a4,a4,-2024 # 3818 <_read+0x84>
     e6c:	97ba                	add	a5,a5,a4
     e6e:	439c                	lw	a5,0(a5)
     e70:	84818413          	addi	s0,gp,-1976 # 200000f8 <g_brewing_ctrl>
     e74:	8782                	jr	a5
     e76:	447000ef          	jal	ra,1abc <WaterLevel_Detect>
     e7a:	405c                	lw	a5,4(s0)
     e7c:	8c9d                	sub	s1,s1,a5
     e7e:	c10d                	beqz	a0,ea0 <BrewingControl_Task+0x50>
     e80:	4581                	li	a1,0
     e82:	4501                	li	a0,0
     e84:	c404                	sw	s1,8(s0)
     e86:	4cf000ef          	jal	ra,1b54 <WaterPump_Control>
     e8a:	440c                	lw	a1,8(s0)
     e8c:	00004537          	lui	a0,0x4
     e90:	478d                	li	a5,3
     e92:	8a850513          	addi	a0,a0,-1880 # 38a8 <_read+0x114>
     e96:	c01c                	sw	a5,0(s0)
     e98:	73c010ef          	jal	ra,25d4 <iprintf>
     e9c:	b78ff06f          	j	214 <__riscv_restore_0>
     ea0:	67bd                	lui	a5,0xf
     ea2:	a5f78793          	addi	a5,a5,-1441 # ea5f <_data_lma+0xaedf>
     ea6:	fe97fbe3          	bgeu	a5,s1,e9c <BrewingControl_Task+0x4c>
     eaa:	4581                	li	a1,0
     eac:	4a9000ef          	jal	ra,1b54 <WaterPump_Control>
     eb0:	00004537          	lui	a0,0x4
     eb4:	00042023          	sw	zero,0(s0)
     eb8:	8d450513          	addi	a0,a0,-1836 # 38d4 <_read+0x140>
     ebc:	02d010ef          	jal	ra,26e8 <puts>
     ec0:	bff1                	j	e9c <BrewingControl_Task+0x4c>
     ec2:	83818513          	addi	a0,gp,-1992 # 200000e8 <ds18b20>
     ec6:	2629                	jal	11d0 <DS18B20_ReadRealtimeTemp>
     ec8:	480c                	lw	a1,16(s0)
     eca:	8381a503          	lw	a0,-1992(gp) # 200000e8 <ds18b20>
     ece:	19e010ef          	jal	ra,206c <__subsf3>
     ed2:	000047b7          	lui	a5,0x4
     ed6:	8a47a583          	lw	a1,-1884(a5) # 38a4 <_read+0x110>
     eda:	5f1000ef          	jal	ra,1cca <__gesf2>
     ede:	02054563          	bltz	a0,f08 <BrewingControl_Task+0xb8>
     ee2:	26b1                	jal	122e <Heater_Stop>
     ee4:	480c                	lw	a1,16(s0)
     ee6:	8381a503          	lw	a0,-1992(gp) # 200000e8 <ds18b20>
     eea:	4795                	li	a5,5
     eec:	c01c                	sw	a5,0(s0)
     eee:	17e010ef          	jal	ra,206c <__subsf3>
     ef2:	5c8010ef          	jal	ra,24ba <__extendsfdf2>
     ef6:	862a                	mv	a2,a0
     ef8:	00004537          	lui	a0,0x4
     efc:	86ae                	mv	a3,a1
     efe:	8e850513          	addi	a0,a0,-1816 # 38e8 <_read+0x154>
     f02:	6d2010ef          	jal	ra,25d4 <iprintf>
     f06:	bf59                	j	e9c <BrewingControl_Task+0x4c>
     f08:	405c                	lw	a5,4(s0)
     f0a:	8c9d                	sub	s1,s1,a5
     f0c:	000497b7          	lui	a5,0x49
     f10:	3df78793          	addi	a5,a5,991 # 493df <_data_lma+0x4585f>
     f14:	f897f4e3          	bgeu	a5,s1,e9c <BrewingControl_Task+0x4c>
     f18:	2e19                	jal	122e <Heater_Stop>
     f1a:	00004537          	lui	a0,0x4
     f1e:	00042023          	sw	zero,0(s0)
     f22:	91850513          	addi	a0,a0,-1768 # 3918 <_read+0x184>
     f26:	bf59                	j	ebc <BrewingControl_Task+0x6c>
     f28:	47a1                	li	a5,8
     f2a:	00004537          	lui	a0,0x4
     f2e:	c01c                	sw	a5,0(s0)
     f30:	c444                	sw	s1,12(s0)
     f32:	92c50513          	addi	a0,a0,-1748 # 392c <_read+0x198>
     f36:	b759                	j	ebc <BrewingControl_Task+0x6c>
     f38:	445c                	lw	a5,12(s0)
     f3a:	8c9d                	sub	s1,s1,a5
     f3c:	679d                	lui	a5,0x7
     f3e:	52f78793          	addi	a5,a5,1327 # 752f <_data_lma+0x39af>
     f42:	f497fde3          	bgeu	a5,s1,e9c <BrewingControl_Task+0x4c>
     f46:	47a5                	li	a5,9
     f48:	4505                	li	a0,1
     f4a:	4585                	li	a1,1
     f4c:	c01c                	sw	a5,0(s0)
     f4e:	407000ef          	jal	ra,1b54 <WaterPump_Control>
     f52:	00004537          	lui	a0,0x4
     f56:	93c50513          	addi	a0,a0,-1732 # 393c <_read+0x1a8>
     f5a:	b78d                	j	ebc <BrewingControl_Task+0x6c>
     f5c:	77e5                	lui	a5,0xffff9
     f5e:	ad078793          	addi	a5,a5,-1328 # ffff8ad0 <_eusrstack+0xdfff0ad0>
     f62:	94be                	add	s1,s1,a5
     f64:	445c                	lw	a5,12(s0)
     f66:	8c9d                	sub	s1,s1,a5
     f68:	441c                	lw	a5,8(s0)
     f6a:	f2f4e9e3          	bltu	s1,a5,e9c <BrewingControl_Task+0x4c>
     f6e:	4505                	li	a0,1
     f70:	4581                	li	a1,0
     f72:	3e3000ef          	jal	ra,1b54 <WaterPump_Control>
     f76:	47a9                	li	a5,10
     f78:	00004537          	lui	a0,0x4
     f7c:	c01c                	sw	a5,0(s0)
     f7e:	95c50513          	addi	a0,a0,-1700 # 395c <_read+0x1c8>
     f82:	bf2d                	j	ebc <BrewingControl_Task+0x6c>

00000f84 <DS18B20_IO_OUT>:
     f84:	a6cff2ef          	jal	t0,1f0 <__riscv_save_0>
     f88:	1141                	addi	sp,sp,-16
     f8a:	4789                	li	a5,2
     f8c:	827c                	sh	a5,4(sp)
     f8e:	40011537          	lui	a0,0x40011
     f92:	47d1                	li	a5,20
     f94:	c63e                	sw	a5,12(sp)
     f96:	004c                	addi	a1,sp,4
     f98:	478d                	li	a5,3
     f9a:	80050513          	addi	a0,a0,-2048 # 40010800 <_eusrstack+0x20008800>
     f9e:	c43e                	sw	a5,8(sp)
     fa0:	facff0ef          	jal	ra,74c <GPIO_Init>
     fa4:	0141                	addi	sp,sp,16
     fa6:	a6eff06f          	j	214 <__riscv_restore_0>

00000faa <DS18B20_IO_IN>:
     faa:	a46ff2ef          	jal	t0,1f0 <__riscv_save_0>
     fae:	1141                	addi	sp,sp,-16
     fb0:	4789                	li	a5,2
     fb2:	40011537          	lui	a0,0x40011
     fb6:	827c                	sh	a5,4(sp)
     fb8:	004c                	addi	a1,sp,4
     fba:	04800793          	li	a5,72
     fbe:	80050513          	addi	a0,a0,-2048 # 40010800 <_eusrstack+0x20008800>
     fc2:	c63e                	sw	a5,12(sp)
     fc4:	f88ff0ef          	jal	ra,74c <GPIO_Init>
     fc8:	0141                	addi	sp,sp,16
     fca:	a4aff06f          	j	214 <__riscv_restore_0>

00000fce <DS18B20_Reset>:
     fce:	a22ff2ef          	jal	t0,1f0 <__riscv_save_0>
     fd2:	3f4d                	jal	f84 <DS18B20_IO_OUT>
     fd4:	40011437          	lui	s0,0x40011
     fd8:	4589                	li	a1,2
     fda:	80040513          	addi	a0,s0,-2048 # 40010800 <_eusrstack+0x20008800>
     fde:	83dff0ef          	jal	ra,81a <GPIO_ResetBits>
     fe2:	1e000513          	li	a0,480
     fe6:	3311                	jal	cea <Delay_us>
     fe8:	4589                	li	a1,2
     fea:	80040513          	addi	a0,s0,-2048
     fee:	829ff0ef          	jal	ra,816 <GPIO_SetBits>
     ff2:	04600513          	li	a0,70
     ff6:	39d5                	jal	cea <Delay_us>
     ff8:	3f4d                	jal	faa <DS18B20_IO_IN>
     ffa:	a1aff06f          	j	214 <__riscv_restore_0>

00000ffe <DS18B20_Check>:
     ffe:	9f2ff2ef          	jal	t0,1f0 <__riscv_save_0>
    1002:	400114b7          	lui	s1,0x40011
    1006:	06500413          	li	s0,101
    100a:	80048493          	addi	s1,s1,-2048 # 40010800 <_eusrstack+0x20008800>
    100e:	4589                	li	a1,2
    1010:	8526                	mv	a0,s1
    1012:	ffaff0ef          	jal	ra,80c <GPIO_ReadInputDataBit>
    1016:	ed11                	bnez	a0,1032 <DS18B20_Check+0x34>
    1018:	400114b7          	lui	s1,0x40011
    101c:	0f100413          	li	s0,241
    1020:	80048493          	addi	s1,s1,-2048 # 40010800 <_eusrstack+0x20008800>
    1024:	4589                	li	a1,2
    1026:	8526                	mv	a0,s1
    1028:	fe4ff0ef          	jal	ra,80c <GPIO_ReadInputDataBit>
    102c:	cd01                	beqz	a0,1044 <DS18B20_Check+0x46>
    102e:	4505                	li	a0,1
    1030:	a801                	j	1040 <DS18B20_Check+0x42>
    1032:	147d                	addi	s0,s0,-1
    1034:	4505                	li	a0,1
    1036:	0ff47413          	andi	s0,s0,255
    103a:	3945                	jal	cea <Delay_us>
    103c:	f869                	bnez	s0,100e <DS18B20_Check+0x10>
    103e:	4501                	li	a0,0
    1040:	9d4ff06f          	j	214 <__riscv_restore_0>
    1044:	147d                	addi	s0,s0,-1
    1046:	4505                	li	a0,1
    1048:	0ff47413          	andi	s0,s0,255
    104c:	3979                	jal	cea <Delay_us>
    104e:	f879                	bnez	s0,1024 <DS18B20_Check+0x26>
    1050:	b7fd                	j	103e <DS18B20_Check+0x40>

00001052 <DS18B20_WriteBit>:
    1052:	99eff2ef          	jal	t0,1f0 <__riscv_save_0>
    1056:	84aa                	mv	s1,a0
    1058:	40011437          	lui	s0,0x40011
    105c:	3725                	jal	f84 <DS18B20_IO_OUT>
    105e:	4589                	li	a1,2
    1060:	80040513          	addi	a0,s0,-2048 # 40010800 <_eusrstack+0x20008800>
    1064:	fb6ff0ef          	jal	ra,81a <GPIO_ResetBits>
    1068:	cc89                	beqz	s1,1082 <DS18B20_WriteBit+0x30>
    106a:	4529                	li	a0,10
    106c:	39bd                	jal	cea <Delay_us>
    106e:	80040513          	addi	a0,s0,-2048
    1072:	4589                	li	a1,2
    1074:	fa2ff0ef          	jal	ra,816 <GPIO_SetBits>
    1078:	03700513          	li	a0,55
    107c:	31bd                	jal	cea <Delay_us>
    107e:	996ff06f          	j	214 <__riscv_restore_0>
    1082:	04100513          	li	a0,65
    1086:	3195                	jal	cea <Delay_us>
    1088:	80040513          	addi	a0,s0,-2048
    108c:	4589                	li	a1,2
    108e:	f88ff0ef          	jal	ra,816 <GPIO_SetBits>
    1092:	4515                	li	a0,5
    1094:	b7e5                	j	107c <DS18B20_WriteBit+0x2a>

00001096 <DS18B20_ReadBit>:
    1096:	95aff2ef          	jal	t0,1f0 <__riscv_save_0>
    109a:	35ed                	jal	f84 <DS18B20_IO_OUT>
    109c:	40011437          	lui	s0,0x40011
    10a0:	4589                	li	a1,2
    10a2:	80040513          	addi	a0,s0,-2048 # 40010800 <_eusrstack+0x20008800>
    10a6:	f74ff0ef          	jal	ra,81a <GPIO_ResetBits>
    10aa:	450d                	li	a0,3
    10ac:	393d                	jal	cea <Delay_us>
    10ae:	4589                	li	a1,2
    10b0:	80040513          	addi	a0,s0,-2048
    10b4:	f62ff0ef          	jal	ra,816 <GPIO_SetBits>
    10b8:	3dcd                	jal	faa <DS18B20_IO_IN>
    10ba:	4529                	li	a0,10
    10bc:	313d                	jal	cea <Delay_us>
    10be:	4589                	li	a1,2
    10c0:	80040513          	addi	a0,s0,-2048
    10c4:	f48ff0ef          	jal	ra,80c <GPIO_ReadInputDataBit>
    10c8:	842a                	mv	s0,a0
    10ca:	03500513          	li	a0,53
    10ce:	3931                	jal	cea <Delay_us>
    10d0:	8522                	mv	a0,s0
    10d2:	942ff06f          	j	214 <__riscv_restore_0>

000010d6 <DS18B20_WriteByte>:
    10d6:	91aff2ef          	jal	t0,1f0 <__riscv_save_0>
    10da:	84aa                	mv	s1,a0
    10dc:	4421                	li	s0,8
    10de:	147d                	addi	s0,s0,-1
    10e0:	0014f513          	andi	a0,s1,1
    10e4:	0ff47413          	andi	s0,s0,255
    10e8:	37ad                	jal	1052 <DS18B20_WriteBit>
    10ea:	8085                	srli	s1,s1,0x1
    10ec:	f86d                	bnez	s0,10de <DS18B20_WriteByte+0x8>
    10ee:	926ff06f          	j	214 <__riscv_restore_0>

000010f2 <DS18B20_ReadByte>:
    10f2:	8feff2ef          	jal	t0,1f0 <__riscv_save_0>
    10f6:	44a1                	li	s1,8
    10f8:	4401                	li	s0,0
    10fa:	8005                	srli	s0,s0,0x1
    10fc:	3f69                	jal	1096 <DS18B20_ReadBit>
    10fe:	c509                	beqz	a0,1108 <DS18B20_ReadByte+0x16>
    1100:	f8046413          	ori	s0,s0,-128
    1104:	0ff47413          	andi	s0,s0,255
    1108:	14fd                	addi	s1,s1,-1
    110a:	0ff4f493          	andi	s1,s1,255
    110e:	f4f5                	bnez	s1,10fa <DS18B20_ReadByte+0x8>
    1110:	8522                	mv	a0,s0
    1112:	902ff06f          	j	214 <__riscv_restore_0>

00001116 <DS18B20_Init>:
    1116:	8daff2ef          	jal	t0,1f0 <__riscv_save_0>
    111a:	842a                	mv	s0,a0
    111c:	84ae                	mv	s1,a1
    111e:	4511                	li	a0,4
    1120:	4585                	li	a1,1
    1122:	8c7ff0ef          	jal	ra,9e8 <RCC_APB2PeriphClockCmd>
    1126:	3db9                	jal	f84 <DS18B20_IO_OUT>
    1128:	40011537          	lui	a0,0x40011
    112c:	4589                	li	a1,2
    112e:	80050513          	addi	a0,a0,-2048 # 40010800 <_eusrstack+0x20008800>
    1132:	ee4ff0ef          	jal	ra,816 <GPIO_SetBits>
    1136:	4529                	li	a0,10
    1138:	36d5                	jal	d1c <Delay_ms>
    113a:	c044                	sw	s1,4(s0)
    113c:	00040423          	sb	zero,8(s0)
    1140:	00042623          	sw	zero,12(s0)
    1144:	3569                	jal	fce <DS18B20_Reset>
    1146:	3d65                	jal	ffe <DS18B20_Check>
    1148:	e911                	bnez	a0,115c <DS18B20_Init+0x46>
    114a:	00004537          	lui	a0,0x4
    114e:	97450513          	addi	a0,a0,-1676 # 3974 <_read+0x1e0>
    1152:	596010ef          	jal	ra,26e8 <puts>
    1156:	4505                	li	a0,1
    1158:	8bcff06f          	j	214 <__riscv_restore_0>
    115c:	4501                	li	a0,0
    115e:	bfed                	j	1158 <DS18B20_Init+0x42>

00001160 <DS18B20_StartConvert>:
    1160:	890ff2ef          	jal	t0,1f0 <__riscv_save_0>
    1164:	35ad                	jal	fce <DS18B20_Reset>
    1166:	3d61                	jal	ffe <DS18B20_Check>
    1168:	4785                	li	a5,1
    116a:	c901                	beqz	a0,117a <DS18B20_StartConvert+0x1a>
    116c:	0cc00513          	li	a0,204
    1170:	379d                	jal	10d6 <DS18B20_WriteByte>
    1172:	04400513          	li	a0,68
    1176:	3785                	jal	10d6 <DS18B20_WriteByte>
    1178:	4781                	li	a5,0
    117a:	853e                	mv	a0,a5
    117c:	898ff06f          	j	214 <__riscv_restore_0>

00001180 <DS18B20_ReadTempRaw>:
    1180:	870ff2ef          	jal	t0,1f0 <__riscv_save_0>
    1184:	3ff1                	jal	1160 <DS18B20_StartConvert>
    1186:	c519                	beqz	a0,1194 <DS18B20_ReadTempRaw+0x14>
    1188:	000047b7          	lui	a5,0x4
    118c:	99c7a503          	lw	a0,-1636(a5) # 399c <_read+0x208>
    1190:	884ff06f          	j	214 <__riscv_restore_0>
    1194:	2ee00513          	li	a0,750
    1198:	3651                	jal	d1c <Delay_ms>
    119a:	3d15                	jal	fce <DS18B20_Reset>
    119c:	358d                	jal	ffe <DS18B20_Check>
    119e:	d56d                	beqz	a0,1188 <DS18B20_ReadTempRaw+0x8>
    11a0:	0cc00513          	li	a0,204
    11a4:	3f0d                	jal	10d6 <DS18B20_WriteByte>
    11a6:	0be00513          	li	a0,190
    11aa:	3735                	jal	10d6 <DS18B20_WriteByte>
    11ac:	3799                	jal	10f2 <DS18B20_ReadByte>
    11ae:	842a                	mv	s0,a0
    11b0:	3789                	jal	10f2 <DS18B20_ReadByte>
    11b2:	0522                	slli	a0,a0,0x8
    11b4:	8d41                	or	a0,a0,s0
    11b6:	0542                	slli	a0,a0,0x10
    11b8:	8541                	srai	a0,a0,0x10
    11ba:	23a010ef          	jal	ra,23f4 <__floatsisf>
    11be:	000047b7          	lui	a5,0x4
    11c2:	9a07a583          	lw	a1,-1632(a5) # 39a0 <_read+0x20c>
    11c6:	417000ef          	jal	ra,1ddc <__mulsf3>
    11ca:	b7d9                	j	1190 <DS18B20_ReadTempRaw+0x10>

000011cc <DS18B20_SetCallback>:
    11cc:	c54c                	sw	a1,12(a0)
    11ce:	8082                	ret

000011d0 <DS18B20_ReadRealtimeTemp>:
    11d0:	820ff2ef          	jal	t0,1f0 <__riscv_save_0>
    11d4:	842a                	mv	s0,a0
    11d6:	376d                	jal	1180 <DS18B20_ReadTempRaw>
    11d8:	000047b7          	lui	a5,0x4
    11dc:	9987a583          	lw	a1,-1640(a5) # 3998 <_read+0x204>
    11e0:	84aa                	mv	s1,a0
    11e2:	36f000ef          	jal	ra,1d50 <__lesf2>
    11e6:	02a05763          	blez	a0,1214 <DS18B20_ReadRealtimeTemp+0x44>
    11ea:	4048                	lw	a0,4(s0)
    11ec:	c004                	sw	s1,0(s0)
    11ee:	85a6                	mv	a1,s1
    11f0:	361000ef          	jal	ra,1d50 <__lesf2>
    11f4:	00a04d63          	bgtz	a0,120e <DS18B20_ReadRealtimeTemp+0x3e>
    11f8:	241c                	lbu	a5,8(s0)
    11fa:	e789                	bnez	a5,1204 <DS18B20_ReadRealtimeTemp+0x34>
    11fc:	445c                	lw	a5,12(s0)
    11fe:	c399                	beqz	a5,1204 <DS18B20_ReadRealtimeTemp+0x34>
    1200:	8526                	mv	a0,s1
    1202:	9782                	jalr	a5
    1204:	4785                	li	a5,1
    1206:	a41c                	sb	a5,8(s0)
    1208:	4501                	li	a0,0
    120a:	80aff06f          	j	214 <__riscv_restore_0>
    120e:	00040423          	sb	zero,8(s0)
    1212:	bfdd                	j	1208 <DS18B20_ReadRealtimeTemp+0x38>
    1214:	4509                	li	a0,2
    1216:	bfd5                	j	120a <DS18B20_ReadRealtimeTemp+0x3a>

00001218 <Heater_Start>:
    1218:	fd9fe2ef          	jal	t0,1f0 <__riscv_save_0>
    121c:	40011537          	lui	a0,0x40011
    1220:	45c1                	li	a1,16
    1222:	80050513          	addi	a0,a0,-2048 # 40010800 <_eusrstack+0x20008800>
    1226:	df4ff0ef          	jal	ra,81a <GPIO_ResetBits>
    122a:	febfe06f          	j	214 <__riscv_restore_0>

0000122e <Heater_Stop>:
    122e:	fc3fe2ef          	jal	t0,1f0 <__riscv_save_0>
    1232:	40011537          	lui	a0,0x40011
    1236:	45c1                	li	a1,16
    1238:	80050513          	addi	a0,a0,-2048 # 40010800 <_eusrstack+0x20008800>
    123c:	ddaff0ef          	jal	ra,816 <GPIO_SetBits>
    1240:	fd5fe06f          	j	214 <__riscv_restore_0>

00001244 <Heater_Init>:
    1244:	fadfe2ef          	jal	t0,1f0 <__riscv_save_0>
    1248:	1141                	addi	sp,sp,-16
    124a:	4585                	li	a1,1
    124c:	4511                	li	a0,4
    124e:	c202                	sw	zero,4(sp)
    1250:	c402                	sw	zero,8(sp)
    1252:	c602                	sw	zero,12(sp)
    1254:	f94ff0ef          	jal	ra,9e8 <RCC_APB2PeriphClockCmd>
    1258:	47c1                	li	a5,16
    125a:	827c                	sh	a5,4(sp)
    125c:	40011537          	lui	a0,0x40011
    1260:	47c1                	li	a5,16
    1262:	004c                	addi	a1,sp,4
    1264:	c63e                	sw	a5,12(sp)
    1266:	80050513          	addi	a0,a0,-2048 # 40010800 <_eusrstack+0x20008800>
    126a:	478d                	li	a5,3
    126c:	c43e                	sw	a5,8(sp)
    126e:	cdeff0ef          	jal	ra,74c <GPIO_Init>
    1272:	3f75                	jal	122e <Heater_Stop>
    1274:	0141                	addi	sp,sp,16
    1276:	f9ffe06f          	j	214 <__riscv_restore_0>

0000127a <key_init>:
    127a:	f77fe2ef          	jal	t0,1f0 <__riscv_save_0>
    127e:	1141                	addi	sp,sp,-16
    1280:	4585                	li	a1,1
    1282:	02000513          	li	a0,32
    1286:	f62ff0ef          	jal	ra,9e8 <RCC_APB2PeriphClockCmd>
    128a:	6485                	lui	s1,0x1
    128c:	40011537          	lui	a0,0x40011
    1290:	440d                	li	s0,3
    1292:	a0048793          	addi	a5,s1,-1536 # a00 <RCC_APB2PeriphClockCmd+0x18>
    1296:	4941                	li	s2,16
    1298:	004c                	addi	a1,sp,4
    129a:	40050513          	addi	a0,a0,1024 # 40011400 <_eusrstack+0x20009400>
    129e:	827c                	sh	a5,4(sp)
    12a0:	c64a                	sw	s2,12(sp)
    12a2:	c422                	sw	s0,8(sp)
    12a4:	ca8ff0ef          	jal	ra,74c <GPIO_Init>
    12a8:	4585                	li	a1,1
    12aa:	04000513          	li	a0,64
    12ae:	f3aff0ef          	jal	ra,9e8 <RCC_APB2PeriphClockCmd>
    12b2:	c64a                	sw	s2,12(sp)
    12b4:	40012937          	lui	s2,0x40012
    12b8:	77e9                	lui	a5,0xffffa
    12ba:	004c                	addi	a1,sp,4
    12bc:	80090513          	addi	a0,s2,-2048 # 40011800 <_eusrstack+0x20009800>
    12c0:	827c                	sh	a5,4(sp)
    12c2:	c422                	sw	s0,8(sp)
    12c4:	c88ff0ef          	jal	ra,74c <GPIO_Init>
    12c8:	4585                	li	a1,1
    12ca:	04000513          	li	a0,64
    12ce:	f1aff0ef          	jal	ra,9e8 <RCC_APB2PeriphClockCmd>
    12d2:	a8048493          	addi	s1,s1,-1408
    12d6:	8264                	sh	s1,4(sp)
    12d8:	004c                	addi	a1,sp,4
    12da:	04800493          	li	s1,72
    12de:	80090513          	addi	a0,s2,-2048
    12e2:	c626                	sw	s1,12(sp)
    12e4:	c422                	sw	s0,8(sp)
    12e6:	c66ff0ef          	jal	ra,74c <GPIO_Init>
    12ea:	4585                	li	a1,1
    12ec:	4541                	li	a0,16
    12ee:	efaff0ef          	jal	ra,9e8 <RCC_APB2PeriphClockCmd>
    12f2:	02000793          	li	a5,32
    12f6:	004c                	addi	a1,sp,4
    12f8:	40011537          	lui	a0,0x40011
    12fc:	827c                	sh	a5,4(sp)
    12fe:	c626                	sw	s1,12(sp)
    1300:	c422                	sw	s0,8(sp)
    1302:	c4aff0ef          	jal	ra,74c <GPIO_Init>
    1306:	0141                	addi	sp,sp,16
    1308:	f0dfe06f          	j	214 <__riscv_restore_0>

0000130c <key_read>:
    130c:	ee5fe2ef          	jal	t0,1f0 <__riscv_save_0>
    1310:	6405                	lui	s0,0x1
    1312:	400114b7          	lui	s1,0x40011
    1316:	80040593          	addi	a1,s0,-2048 # 800 <__stack_size>
    131a:	40048513          	addi	a0,s1,1024 # 40011400 <_eusrstack+0x20009400>
    131e:	cfcff0ef          	jal	ra,81a <GPIO_ResetBits>
    1322:	40048513          	addi	a0,s1,1024
    1326:	20000593          	li	a1,512
    132a:	400124b7          	lui	s1,0x40012
    132e:	ce8ff0ef          	jal	ra,816 <GPIO_SetBits>
    1332:	65a1                	lui	a1,0x8
    1334:	80048513          	addi	a0,s1,-2048 # 40011800 <_eusrstack+0x20009800>
    1338:	cdeff0ef          	jal	ra,816 <GPIO_SetBits>
    133c:	6589                	lui	a1,0x2
    133e:	80048513          	addi	a0,s1,-2048
    1342:	cd4ff0ef          	jal	ra,816 <GPIO_SetBits>
    1346:	80040593          	addi	a1,s0,-2048
    134a:	80048513          	addi	a0,s1,-2048
    134e:	cbeff0ef          	jal	ra,80c <GPIO_ReadInputDataBit>
    1352:	00153413          	seqz	s0,a0
    1356:	20000593          	li	a1,512
    135a:	80048513          	addi	a0,s1,-2048
    135e:	caeff0ef          	jal	ra,80c <GPIO_ReadInputDataBit>
    1362:	040a                	slli	s0,s0,0x2
    1364:	e111                	bnez	a0,1368 <key_read+0x5c>
    1366:	440d                	li	s0,3
    1368:	40012537          	lui	a0,0x40012
    136c:	08000593          	li	a1,128
    1370:	80050513          	addi	a0,a0,-2048 # 40011800 <_eusrstack+0x20009800>
    1374:	c98ff0ef          	jal	ra,80c <GPIO_ReadInputDataBit>
    1378:	e111                	bnez	a0,137c <key_read+0x70>
    137a:	4409                	li	s0,2
    137c:	02000593          	li	a1,32
    1380:	40011537          	lui	a0,0x40011
    1384:	c88ff0ef          	jal	ra,80c <GPIO_ReadInputDataBit>
    1388:	e111                	bnez	a0,138c <key_read+0x80>
    138a:	4405                	li	s0,1
    138c:	400114b7          	lui	s1,0x40011
    1390:	6905                	lui	s2,0x1
    1392:	40048513          	addi	a0,s1,1024 # 40011400 <_eusrstack+0x20009400>
    1396:	80090593          	addi	a1,s2,-2048 # 800 <__stack_size>
    139a:	c7cff0ef          	jal	ra,816 <GPIO_SetBits>
    139e:	40048513          	addi	a0,s1,1024
    13a2:	20000593          	li	a1,512
    13a6:	c74ff0ef          	jal	ra,81a <GPIO_ResetBits>
    13aa:	400124b7          	lui	s1,0x40012
    13ae:	65a1                	lui	a1,0x8
    13b0:	80048513          	addi	a0,s1,-2048 # 40011800 <_eusrstack+0x20009800>
    13b4:	c62ff0ef          	jal	ra,816 <GPIO_SetBits>
    13b8:	6589                	lui	a1,0x2
    13ba:	80048513          	addi	a0,s1,-2048
    13be:	c58ff0ef          	jal	ra,816 <GPIO_SetBits>
    13c2:	80090593          	addi	a1,s2,-2048
    13c6:	80048513          	addi	a0,s1,-2048
    13ca:	c42ff0ef          	jal	ra,80c <GPIO_ReadInputDataBit>
    13ce:	e111                	bnez	a0,13d2 <key_read+0xc6>
    13d0:	4421                	li	s0,8
    13d2:	40012537          	lui	a0,0x40012
    13d6:	20000593          	li	a1,512
    13da:	80050513          	addi	a0,a0,-2048 # 40011800 <_eusrstack+0x20009800>
    13de:	c2eff0ef          	jal	ra,80c <GPIO_ReadInputDataBit>
    13e2:	e111                	bnez	a0,13e6 <key_read+0xda>
    13e4:	441d                	li	s0,7
    13e6:	40012537          	lui	a0,0x40012
    13ea:	08000593          	li	a1,128
    13ee:	80050513          	addi	a0,a0,-2048 # 40011800 <_eusrstack+0x20009800>
    13f2:	c1aff0ef          	jal	ra,80c <GPIO_ReadInputDataBit>
    13f6:	e111                	bnez	a0,13fa <key_read+0xee>
    13f8:	4419                	li	s0,6
    13fa:	02000593          	li	a1,32
    13fe:	40011537          	lui	a0,0x40011
    1402:	c0aff0ef          	jal	ra,80c <GPIO_ReadInputDataBit>
    1406:	e111                	bnez	a0,140a <key_read+0xfe>
    1408:	4415                	li	s0,5
    140a:	400114b7          	lui	s1,0x40011
    140e:	6905                	lui	s2,0x1
    1410:	40048513          	addi	a0,s1,1024 # 40011400 <_eusrstack+0x20009400>
    1414:	80090593          	addi	a1,s2,-2048 # 800 <__stack_size>
    1418:	bfeff0ef          	jal	ra,816 <GPIO_SetBits>
    141c:	40048513          	addi	a0,s1,1024
    1420:	20000593          	li	a1,512
    1424:	bf2ff0ef          	jal	ra,816 <GPIO_SetBits>
    1428:	400124b7          	lui	s1,0x40012
    142c:	65a1                	lui	a1,0x8
    142e:	80048513          	addi	a0,s1,-2048 # 40011800 <_eusrstack+0x20009800>
    1432:	be8ff0ef          	jal	ra,81a <GPIO_ResetBits>
    1436:	6589                	lui	a1,0x2
    1438:	80048513          	addi	a0,s1,-2048
    143c:	bdaff0ef          	jal	ra,816 <GPIO_SetBits>
    1440:	80090593          	addi	a1,s2,-2048
    1444:	80048513          	addi	a0,s1,-2048
    1448:	bc4ff0ef          	jal	ra,80c <GPIO_ReadInputDataBit>
    144c:	e111                	bnez	a0,1450 <key_read+0x144>
    144e:	4431                	li	s0,12
    1450:	40012537          	lui	a0,0x40012
    1454:	20000593          	li	a1,512
    1458:	80050513          	addi	a0,a0,-2048 # 40011800 <_eusrstack+0x20009800>
    145c:	bb0ff0ef          	jal	ra,80c <GPIO_ReadInputDataBit>
    1460:	e111                	bnez	a0,1464 <key_read+0x158>
    1462:	442d                	li	s0,11
    1464:	40012537          	lui	a0,0x40012
    1468:	08000593          	li	a1,128
    146c:	80050513          	addi	a0,a0,-2048 # 40011800 <_eusrstack+0x20009800>
    1470:	b9cff0ef          	jal	ra,80c <GPIO_ReadInputDataBit>
    1474:	e111                	bnez	a0,1478 <key_read+0x16c>
    1476:	4429                	li	s0,10
    1478:	02000593          	li	a1,32
    147c:	40011537          	lui	a0,0x40011
    1480:	b8cff0ef          	jal	ra,80c <GPIO_ReadInputDataBit>
    1484:	e111                	bnez	a0,1488 <key_read+0x17c>
    1486:	4425                	li	s0,9
    1488:	400114b7          	lui	s1,0x40011
    148c:	6905                	lui	s2,0x1
    148e:	40048513          	addi	a0,s1,1024 # 40011400 <_eusrstack+0x20009400>
    1492:	80090593          	addi	a1,s2,-2048 # 800 <__stack_size>
    1496:	b80ff0ef          	jal	ra,816 <GPIO_SetBits>
    149a:	40048513          	addi	a0,s1,1024
    149e:	20000593          	li	a1,512
    14a2:	b74ff0ef          	jal	ra,816 <GPIO_SetBits>
    14a6:	400124b7          	lui	s1,0x40012
    14aa:	65a1                	lui	a1,0x8
    14ac:	80048513          	addi	a0,s1,-2048 # 40011800 <_eusrstack+0x20009800>
    14b0:	b66ff0ef          	jal	ra,816 <GPIO_SetBits>
    14b4:	6589                	lui	a1,0x2
    14b6:	80048513          	addi	a0,s1,-2048
    14ba:	b60ff0ef          	jal	ra,81a <GPIO_ResetBits>
    14be:	80090593          	addi	a1,s2,-2048
    14c2:	80048513          	addi	a0,s1,-2048
    14c6:	b46ff0ef          	jal	ra,80c <GPIO_ReadInputDataBit>
    14ca:	e111                	bnez	a0,14ce <key_read+0x1c2>
    14cc:	4441                	li	s0,16
    14ce:	40012537          	lui	a0,0x40012
    14d2:	20000593          	li	a1,512
    14d6:	80050513          	addi	a0,a0,-2048 # 40011800 <_eusrstack+0x20009800>
    14da:	b32ff0ef          	jal	ra,80c <GPIO_ReadInputDataBit>
    14de:	e111                	bnez	a0,14e2 <key_read+0x1d6>
    14e0:	443d                	li	s0,15
    14e2:	40012537          	lui	a0,0x40012
    14e6:	08000593          	li	a1,128
    14ea:	80050513          	addi	a0,a0,-2048 # 40011800 <_eusrstack+0x20009800>
    14ee:	b1eff0ef          	jal	ra,80c <GPIO_ReadInputDataBit>
    14f2:	e111                	bnez	a0,14f6 <key_read+0x1ea>
    14f4:	4439                	li	s0,14
    14f6:	02000593          	li	a1,32
    14fa:	40011537          	lui	a0,0x40011
    14fe:	b0eff0ef          	jal	ra,80c <GPIO_ReadInputDataBit>
    1502:	e111                	bnez	a0,1506 <key_read+0x1fa>
    1504:	4435                	li	s0,13
    1506:	8522                	mv	a0,s0
    1508:	d0dfe06f          	j	214 <__riscv_restore_0>

0000150c <SPI_LCD_Init>:
    150c:	ccbfe2ef          	jal	t0,1d6 <__riscv_save_4>
    1510:	1101                	addi	sp,sp,-32
    1512:	4585                	li	a1,1
    1514:	02c00513          	li	a0,44
    1518:	cd0ff0ef          	jal	ra,9e8 <RCC_APB2PeriphClockCmd>
    151c:	4585                	li	a1,1
    151e:	6521                	lui	a0,0x8
    1520:	40011437          	lui	s0,0x40011
    1524:	ce2ff0ef          	jal	ra,a06 <RCC_APB1PeriphClockCmd>
    1528:	03800793          	li	a5,56
    152c:	4941                	li	s2,16
    152e:	448d                	li	s1,3
    1530:	858a                	mv	a1,sp
    1532:	40040513          	addi	a0,s0,1024 # 40011400 <_eusrstack+0x20009400>
    1536:	807c                	sh	a5,0(sp)
    1538:	c44a                	sw	s2,8(sp)
    153a:	c226                	sw	s1,4(sp)
    153c:	a10ff0ef          	jal	ra,74c <GPIO_Init>
    1540:	40040513          	addi	a0,s0,1024
    1544:	45a1                	li	a1,8
    1546:	ad0ff0ef          	jal	ra,816 <GPIO_SetBits>
    154a:	40040513          	addi	a0,s0,1024
    154e:	45c1                	li	a1,16
    1550:	ac6ff0ef          	jal	ra,816 <GPIO_SetBits>
    1554:	40040513          	addi	a0,s0,1024
    1558:	02000593          	li	a1,32
    155c:	abaff0ef          	jal	ra,816 <GPIO_SetBits>
    1560:	77e1                	lui	a5,0xffff8
    1562:	858a                	mv	a1,sp
    1564:	80040513          	addi	a0,s0,-2048
    1568:	807c                	sh	a5,0(sp)
    156a:	c44a                	sw	s2,8(sp)
    156c:	c226                	sw	s1,4(sp)
    156e:	9deff0ef          	jal	ra,74c <GPIO_Init>
    1572:	80040513          	addi	a0,s0,-2048
    1576:	65a1                	lui	a1,0x8
    1578:	a9eff0ef          	jal	ra,816 <GPIO_SetBits>
    157c:	47a1                	li	a5,8
    157e:	49e1                	li	s3,24
    1580:	858a                	mv	a1,sp
    1582:	c0040513          	addi	a0,s0,-1024
    1586:	807c                	sh	a5,0(sp)
    1588:	c44e                	sw	s3,8(sp)
    158a:	c226                	sw	s1,4(sp)
    158c:	9c0ff0ef          	jal	ra,74c <GPIO_Init>
    1590:	858a                	mv	a1,sp
    1592:	c0040513          	addi	a0,s0,-1024
    1596:	01211023          	sh	s2,0(sp)
    159a:	c44a                	sw	s2,8(sp)
    159c:	9b0ff0ef          	jal	ra,74c <GPIO_Init>
    15a0:	02000793          	li	a5,32
    15a4:	858a                	mv	a1,sp
    15a6:	c0040513          	addi	a0,s0,-1024
    15aa:	807c                	sh	a5,0(sp)
    15ac:	c44e                	sw	s3,8(sp)
    15ae:	c226                	sw	s1,4(sp)
    15b0:	99cff0ef          	jal	ra,74c <GPIO_Init>
    15b4:	010407b7          	lui	a5,0x1040
    15b8:	c63e                	sw	a5,12(sp)
    15ba:	020007b7          	lui	a5,0x2000
    15be:	40004437          	lui	s0,0x40004
    15c2:	ca3e                	sw	a5,20(sp)
    15c4:	47a1                	li	a5,8
    15c6:	cc3e                	sw	a5,24(sp)
    15c8:	006c                	addi	a1,sp,12
    15ca:	479d                	li	a5,7
    15cc:	c0040513          	addi	a0,s0,-1024 # 40003c00 <_eusrstack+0x1fffbc00>
    15d0:	86fc                	sh	a5,28(sp)
    15d2:	c802                	sw	zero,16(sp)
    15d4:	c50ff0ef          	jal	ra,a24 <SPI_Init>
    15d8:	4585                	li	a1,1
    15da:	c0040513          	addi	a0,s0,-1024
    15de:	c84ff0ef          	jal	ra,a62 <SPI_Cmd>
    15e2:	6105                	addi	sp,sp,32
    15e4:	c27fe06f          	j	20a <__riscv_restore_4>

000015e8 <SPI3_IRQHandler>:
    15e8:	30200073          	mret

000015ec <spi_readwrite>:
    15ec:	bebfe2ef          	jal	t0,1d6 <__riscv_save_4>
    15f0:	400044b7          	lui	s1,0x40004
    15f4:	892a                	mv	s2,a0
    15f6:	0c900413          	li	s0,201
    15fa:	c0048993          	addi	s3,s1,-1024 # 40003c00 <_eusrstack+0x1fffbc00>
    15fe:	4589                	li	a1,2
    1600:	854e                	mv	a0,s3
    1602:	c82ff0ef          	jal	ra,a84 <SPI_I2S_GetFlagStatus>
    1606:	c905                	beqz	a0,1636 <spi_readwrite+0x4a>
    1608:	85ca                	mv	a1,s2
    160a:	c0048513          	addi	a0,s1,-1024
    160e:	400044b7          	lui	s1,0x40004
    1612:	c6aff0ef          	jal	ra,a7c <SPI_I2S_SendData>
    1616:	0c900413          	li	s0,201
    161a:	c0048913          	addi	s2,s1,-1024 # 40003c00 <_eusrstack+0x1fffbc00>
    161e:	4585                	li	a1,1
    1620:	854a                	mv	a0,s2
    1622:	c62ff0ef          	jal	ra,a84 <SPI_I2S_GetFlagStatus>
    1626:	cd19                	beqz	a0,1644 <spi_readwrite+0x58>
    1628:	c0048513          	addi	a0,s1,-1024
    162c:	c54ff0ef          	jal	ra,a80 <SPI_I2S_ReceiveData>
    1630:	0ff57513          	andi	a0,a0,255
    1634:	a031                	j	1640 <spi_readwrite+0x54>
    1636:	147d                	addi	s0,s0,-1
    1638:	0ff47413          	andi	s0,s0,255
    163c:	f069                	bnez	s0,15fe <spi_readwrite+0x12>
    163e:	4501                	li	a0,0
    1640:	bcbfe06f          	j	20a <__riscv_restore_4>
    1644:	147d                	addi	s0,s0,-1
    1646:	0ff47413          	andi	s0,s0,255
    164a:	f871                	bnez	s0,161e <spi_readwrite+0x32>
    164c:	bfcd                	j	163e <spi_readwrite+0x52>

0000164e <LCD_WR_DATA8>:
    164e:	ba3fe2ef          	jal	t0,1f0 <__riscv_save_0>
    1652:	40011437          	lui	s0,0x40011
    1656:	84aa                	mv	s1,a0
    1658:	45c1                	li	a1,16
    165a:	40040513          	addi	a0,s0,1024 # 40011400 <_eusrstack+0x20009400>
    165e:	9bcff0ef          	jal	ra,81a <GPIO_ResetBits>
    1662:	45a1                	li	a1,8
    1664:	40040513          	addi	a0,s0,1024
    1668:	9aeff0ef          	jal	ra,816 <GPIO_SetBits>
    166c:	8526                	mv	a0,s1
    166e:	3fbd                	jal	15ec <spi_readwrite>
    1670:	45c1                	li	a1,16
    1672:	40040513          	addi	a0,s0,1024
    1676:	9a0ff0ef          	jal	ra,816 <GPIO_SetBits>
    167a:	b9bfe06f          	j	214 <__riscv_restore_0>

0000167e <LCD_WR_DATA>:
    167e:	b73fe2ef          	jal	t0,1f0 <__riscv_save_0>
    1682:	40011437          	lui	s0,0x40011
    1686:	84aa                	mv	s1,a0
    1688:	45c1                	li	a1,16
    168a:	40040513          	addi	a0,s0,1024 # 40011400 <_eusrstack+0x20009400>
    168e:	98cff0ef          	jal	ra,81a <GPIO_ResetBits>
    1692:	45a1                	li	a1,8
    1694:	40040513          	addi	a0,s0,1024
    1698:	97eff0ef          	jal	ra,816 <GPIO_SetBits>
    169c:	0084d513          	srli	a0,s1,0x8
    16a0:	37b1                	jal	15ec <spi_readwrite>
    16a2:	0ff4f513          	andi	a0,s1,255
    16a6:	3799                	jal	15ec <spi_readwrite>
    16a8:	45c1                	li	a1,16
    16aa:	40040513          	addi	a0,s0,1024
    16ae:	968ff0ef          	jal	ra,816 <GPIO_SetBits>
    16b2:	b63fe06f          	j	214 <__riscv_restore_0>

000016b6 <LCD_WR_REG>:
    16b6:	b3bfe2ef          	jal	t0,1f0 <__riscv_save_0>
    16ba:	40011437          	lui	s0,0x40011
    16be:	84aa                	mv	s1,a0
    16c0:	45c1                	li	a1,16
    16c2:	40040513          	addi	a0,s0,1024 # 40011400 <_eusrstack+0x20009400>
    16c6:	954ff0ef          	jal	ra,81a <GPIO_ResetBits>
    16ca:	45a1                	li	a1,8
    16cc:	40040513          	addi	a0,s0,1024
    16d0:	94aff0ef          	jal	ra,81a <GPIO_ResetBits>
    16d4:	8526                	mv	a0,s1
    16d6:	3f19                	jal	15ec <spi_readwrite>
    16d8:	45c1                	li	a1,16
    16da:	40040513          	addi	a0,s0,1024
    16de:	938ff0ef          	jal	ra,816 <GPIO_SetBits>
    16e2:	b33fe06f          	j	214 <__riscv_restore_0>

000016e6 <LCD_Address_Set>:
    16e6:	b0bfe2ef          	jal	t0,1f0 <__riscv_save_0>
    16ea:	1141                	addi	sp,sp,-16
    16ec:	842a                	mv	s0,a0
    16ee:	02a00513          	li	a0,42
    16f2:	c236                	sw	a3,4(sp)
    16f4:	c62e                	sw	a1,12(sp)
    16f6:	c432                	sw	a2,8(sp)
    16f8:	3f7d                	jal	16b6 <LCD_WR_REG>
    16fa:	00240513          	addi	a0,s0,2
    16fe:	0542                	slli	a0,a0,0x10
    1700:	8141                	srli	a0,a0,0x10
    1702:	3fb5                	jal	167e <LCD_WR_DATA>
    1704:	4622                	lw	a2,8(sp)
    1706:	0609                	addi	a2,a2,2
    1708:	01061513          	slli	a0,a2,0x10
    170c:	8141                	srli	a0,a0,0x10
    170e:	3f85                	jal	167e <LCD_WR_DATA>
    1710:	02b00513          	li	a0,43
    1714:	374d                	jal	16b6 <LCD_WR_REG>
    1716:	45b2                	lw	a1,12(sp)
    1718:	058d                	addi	a1,a1,3
    171a:	01059513          	slli	a0,a1,0x10
    171e:	8141                	srli	a0,a0,0x10
    1720:	3fb9                	jal	167e <LCD_WR_DATA>
    1722:	4692                	lw	a3,4(sp)
    1724:	068d                	addi	a3,a3,3
    1726:	01069513          	slli	a0,a3,0x10
    172a:	8141                	srli	a0,a0,0x10
    172c:	3f89                	jal	167e <LCD_WR_DATA>
    172e:	02c00513          	li	a0,44
    1732:	3751                	jal	16b6 <LCD_WR_REG>
    1734:	0141                	addi	sp,sp,16
    1736:	adffe06f          	j	214 <__riscv_restore_0>

0000173a <LCD_Init>:
    173a:	ab7fe2ef          	jal	t0,1f0 <__riscv_save_0>
    173e:	33f9                	jal	150c <SPI_LCD_Init>
    1740:	40011437          	lui	s0,0x40011
    1744:	45c1                	li	a1,16
    1746:	c0040513          	addi	a0,s0,-1024 # 40010c00 <_eusrstack+0x20008c00>
    174a:	8d0ff0ef          	jal	ra,81a <GPIO_ResetBits>
    174e:	06400513          	li	a0,100
    1752:	2151                	jal	1bd6 <Delay_Ms>
    1754:	45c1                	li	a1,16
    1756:	c0040513          	addi	a0,s0,-1024
    175a:	8bcff0ef          	jal	ra,816 <GPIO_SetBits>
    175e:	06400513          	li	a0,100
    1762:	2995                	jal	1bd6 <Delay_Ms>
    1764:	02000593          	li	a1,32
    1768:	40040513          	addi	a0,s0,1024
    176c:	8aaff0ef          	jal	ra,816 <GPIO_SetBits>
    1770:	06400513          	li	a0,100
    1774:	218d                	jal	1bd6 <Delay_Ms>
    1776:	4545                	li	a0,17
    1778:	3f3d                	jal	16b6 <LCD_WR_REG>
    177a:	07800513          	li	a0,120
    177e:	29a1                	jal	1bd6 <Delay_Ms>
    1780:	0b100513          	li	a0,177
    1784:	3f0d                	jal	16b6 <LCD_WR_REG>
    1786:	4515                	li	a0,5
    1788:	35d9                	jal	164e <LCD_WR_DATA8>
    178a:	03c00513          	li	a0,60
    178e:	35c1                	jal	164e <LCD_WR_DATA8>
    1790:	03c00513          	li	a0,60
    1794:	3d6d                	jal	164e <LCD_WR_DATA8>
    1796:	0b200513          	li	a0,178
    179a:	3f31                	jal	16b6 <LCD_WR_REG>
    179c:	4515                	li	a0,5
    179e:	3d45                	jal	164e <LCD_WR_DATA8>
    17a0:	03c00513          	li	a0,60
    17a4:	356d                	jal	164e <LCD_WR_DATA8>
    17a6:	03c00513          	li	a0,60
    17aa:	3555                	jal	164e <LCD_WR_DATA8>
    17ac:	0b300513          	li	a0,179
    17b0:	3719                	jal	16b6 <LCD_WR_REG>
    17b2:	4515                	li	a0,5
    17b4:	3d69                	jal	164e <LCD_WR_DATA8>
    17b6:	03c00513          	li	a0,60
    17ba:	3d51                	jal	164e <LCD_WR_DATA8>
    17bc:	03c00513          	li	a0,60
    17c0:	3579                	jal	164e <LCD_WR_DATA8>
    17c2:	4515                	li	a0,5
    17c4:	3569                	jal	164e <LCD_WR_DATA8>
    17c6:	03c00513          	li	a0,60
    17ca:	3551                	jal	164e <LCD_WR_DATA8>
    17cc:	03c00513          	li	a0,60
    17d0:	3dbd                	jal	164e <LCD_WR_DATA8>
    17d2:	0b400513          	li	a0,180
    17d6:	35c5                	jal	16b6 <LCD_WR_REG>
    17d8:	450d                	li	a0,3
    17da:	3d95                	jal	164e <LCD_WR_DATA8>
    17dc:	03a00513          	li	a0,58
    17e0:	3dd9                	jal	16b6 <LCD_WR_REG>
    17e2:	4515                	li	a0,5
    17e4:	35ad                	jal	164e <LCD_WR_DATA8>
    17e6:	0c000513          	li	a0,192
    17ea:	35f1                	jal	16b6 <LCD_WR_REG>
    17ec:	0a200513          	li	a0,162
    17f0:	3db9                	jal	164e <LCD_WR_DATA8>
    17f2:	4509                	li	a0,2
    17f4:	3da9                	jal	164e <LCD_WR_DATA8>
    17f6:	08400513          	li	a0,132
    17fa:	3d91                	jal	164e <LCD_WR_DATA8>
    17fc:	0c100513          	li	a0,193
    1800:	3d5d                	jal	16b6 <LCD_WR_REG>
    1802:	0c500513          	li	a0,197
    1806:	35a1                	jal	164e <LCD_WR_DATA8>
    1808:	0c200513          	li	a0,194
    180c:	356d                	jal	16b6 <LCD_WR_REG>
    180e:	4535                	li	a0,13
    1810:	3d3d                	jal	164e <LCD_WR_DATA8>
    1812:	4501                	li	a0,0
    1814:	3d2d                	jal	164e <LCD_WR_DATA8>
    1816:	0c300513          	li	a0,195
    181a:	3d71                	jal	16b6 <LCD_WR_REG>
    181c:	08d00513          	li	a0,141
    1820:	353d                	jal	164e <LCD_WR_DATA8>
    1822:	02a00513          	li	a0,42
    1826:	3525                	jal	164e <LCD_WR_DATA8>
    1828:	0c400513          	li	a0,196
    182c:	3569                	jal	16b6 <LCD_WR_REG>
    182e:	08d00513          	li	a0,141
    1832:	3d31                	jal	164e <LCD_WR_DATA8>
    1834:	0ee00513          	li	a0,238
    1838:	3d19                	jal	164e <LCD_WR_DATA8>
    183a:	0c500513          	li	a0,197
    183e:	3da5                	jal	16b6 <LCD_WR_REG>
    1840:	4529                	li	a0,10
    1842:	3531                	jal	164e <LCD_WR_DATA8>
    1844:	03600513          	li	a0,54
    1848:	35bd                	jal	16b6 <LCD_WR_REG>
    184a:	0c800513          	li	a0,200
    184e:	3501                	jal	164e <LCD_WR_DATA8>
    1850:	0e000513          	li	a0,224
    1854:	358d                	jal	16b6 <LCD_WR_REG>
    1856:	4549                	li	a0,18
    1858:	3bdd                	jal	164e <LCD_WR_DATA8>
    185a:	4571                	li	a0,28
    185c:	3bcd                	jal	164e <LCD_WR_DATA8>
    185e:	4541                	li	a0,16
    1860:	33fd                	jal	164e <LCD_WR_DATA8>
    1862:	4561                	li	a0,24
    1864:	33ed                	jal	164e <LCD_WR_DATA8>
    1866:	03300513          	li	a0,51
    186a:	33d5                	jal	164e <LCD_WR_DATA8>
    186c:	02c00513          	li	a0,44
    1870:	3bf9                	jal	164e <LCD_WR_DATA8>
    1872:	02500513          	li	a0,37
    1876:	3be1                	jal	164e <LCD_WR_DATA8>
    1878:	02800513          	li	a0,40
    187c:	3bc9                	jal	164e <LCD_WR_DATA8>
    187e:	02800513          	li	a0,40
    1882:	33f1                	jal	164e <LCD_WR_DATA8>
    1884:	02700513          	li	a0,39
    1888:	33d9                	jal	164e <LCD_WR_DATA8>
    188a:	02f00513          	li	a0,47
    188e:	33c1                	jal	164e <LCD_WR_DATA8>
    1890:	03c00513          	li	a0,60
    1894:	3b6d                	jal	164e <LCD_WR_DATA8>
    1896:	4501                	li	a0,0
    1898:	3b5d                	jal	164e <LCD_WR_DATA8>
    189a:	450d                	li	a0,3
    189c:	3b4d                	jal	164e <LCD_WR_DATA8>
    189e:	450d                	li	a0,3
    18a0:	337d                	jal	164e <LCD_WR_DATA8>
    18a2:	4541                	li	a0,16
    18a4:	336d                	jal	164e <LCD_WR_DATA8>
    18a6:	0e100513          	li	a0,225
    18aa:	3531                	jal	16b6 <LCD_WR_REG>
    18ac:	4549                	li	a0,18
    18ae:	3345                	jal	164e <LCD_WR_DATA8>
    18b0:	4571                	li	a0,28
    18b2:	3b71                	jal	164e <LCD_WR_DATA8>
    18b4:	4541                	li	a0,16
    18b6:	3b61                	jal	164e <LCD_WR_DATA8>
    18b8:	4561                	li	a0,24
    18ba:	3b51                	jal	164e <LCD_WR_DATA8>
    18bc:	02d00513          	li	a0,45
    18c0:	3379                	jal	164e <LCD_WR_DATA8>
    18c2:	02800513          	li	a0,40
    18c6:	3361                	jal	164e <LCD_WR_DATA8>
    18c8:	02300513          	li	a0,35
    18cc:	3349                	jal	164e <LCD_WR_DATA8>
    18ce:	02800513          	li	a0,40
    18d2:	3bb5                	jal	164e <LCD_WR_DATA8>
    18d4:	02800513          	li	a0,40
    18d8:	3b9d                	jal	164e <LCD_WR_DATA8>
    18da:	02600513          	li	a0,38
    18de:	3b85                	jal	164e <LCD_WR_DATA8>
    18e0:	02f00513          	li	a0,47
    18e4:	33ad                	jal	164e <LCD_WR_DATA8>
    18e6:	03b00513          	li	a0,59
    18ea:	3395                	jal	164e <LCD_WR_DATA8>
    18ec:	4501                	li	a0,0
    18ee:	3385                	jal	164e <LCD_WR_DATA8>
    18f0:	450d                	li	a0,3
    18f2:	3bb1                	jal	164e <LCD_WR_DATA8>
    18f4:	450d                	li	a0,3
    18f6:	3ba1                	jal	164e <LCD_WR_DATA8>
    18f8:	4541                	li	a0,16
    18fa:	3b91                	jal	164e <LCD_WR_DATA8>
    18fc:	02000513          	li	a0,32
    1900:	3b5d                	jal	16b6 <LCD_WR_REG>
    1902:	454d                	li	a0,19
    1904:	3b4d                	jal	16b6 <LCD_WR_REG>
    1906:	02a00513          	li	a0,42
    190a:	3375                	jal	16b6 <LCD_WR_REG>
    190c:	4501                	li	a0,0
    190e:	3381                	jal	164e <LCD_WR_DATA8>
    1910:	4501                	li	a0,0
    1912:	3b35                	jal	164e <LCD_WR_DATA8>
    1914:	4501                	li	a0,0
    1916:	3b25                	jal	164e <LCD_WR_DATA8>
    1918:	07f00513          	li	a0,127
    191c:	3b0d                	jal	164e <LCD_WR_DATA8>
    191e:	02b00513          	li	a0,43
    1922:	3b51                	jal	16b6 <LCD_WR_REG>
    1924:	4501                	li	a0,0
    1926:	3325                	jal	164e <LCD_WR_DATA8>
    1928:	4501                	li	a0,0
    192a:	3315                	jal	164e <LCD_WR_DATA8>
    192c:	4501                	li	a0,0
    192e:	3305                	jal	164e <LCD_WR_DATA8>
    1930:	07f00513          	li	a0,127
    1934:	3b29                	jal	164e <LCD_WR_DATA8>
    1936:	02900513          	li	a0,41
    193a:	3bb5                	jal	16b6 <LCD_WR_REG>
    193c:	8d9fe06f          	j	214 <__riscv_restore_0>

00001940 <LCD_Fill>:
    1940:	897fe2ef          	jal	t0,1d6 <__riscv_save_4>
    1944:	89b2                	mv	s3,a2
    1946:	8936                	mv	s2,a3
    1948:	167d                	addi	a2,a2,-1
    194a:	16fd                	addi	a3,a3,-1
    194c:	06c2                	slli	a3,a3,0x10
    194e:	0642                	slli	a2,a2,0x10
    1950:	82c1                	srli	a3,a3,0x10
    1952:	8241                	srli	a2,a2,0x10
    1954:	8a2a                	mv	s4,a0
    1956:	842e                	mv	s0,a1
    1958:	8aba                	mv	s5,a4
    195a:	d8dff0ef          	jal	ra,16e6 <LCD_Address_Set>
    195e:	03246063          	bltu	s0,s2,197e <LCD_Fill+0x3e>
    1962:	8a9fe06f          	j	20a <__riscv_restore_4>
    1966:	0485                	addi	s1,s1,1
    1968:	8556                	mv	a0,s5
    196a:	04c2                	slli	s1,s1,0x10
    196c:	d13ff0ef          	jal	ra,167e <LCD_WR_DATA>
    1970:	80c1                	srli	s1,s1,0x10
    1972:	ff34eae3          	bltu	s1,s3,1966 <LCD_Fill+0x26>
    1976:	0405                	addi	s0,s0,1
    1978:	0442                	slli	s0,s0,0x10
    197a:	8041                	srli	s0,s0,0x10
    197c:	b7cd                	j	195e <LCD_Fill+0x1e>
    197e:	84d2                	mv	s1,s4
    1980:	bfcd                	j	1972 <LCD_Fill+0x32>

00001982 <TIM2_PWM_Init>:
    1982:	86ffe2ef          	jal	t0,1f0 <__riscv_save_0>
    1986:	7179                	addi	sp,sp,-48
    1988:	4585                	li	a1,1
    198a:	4505                	li	a0,1
    198c:	87aff0ef          	jal	ra,a06 <RCC_APB1PeriphClockCmd>
    1990:	05f00793          	li	a5,95
    1994:	c43e                	sw	a5,8(sp)
    1996:	6795                	lui	a5,0x5
    1998:	e1f78793          	addi	a5,a5,-481 # 4e1f <_data_lma+0x129f>
    199c:	002c                	addi	a1,sp,8
    199e:	40000537          	lui	a0,0x40000
    19a2:	c63e                	sw	a5,12(sp)
    19a4:	8eaff0ef          	jal	ra,a8e <TIM_TimeBaseInit>
    19a8:	67c1                	lui	a5,0x10
    19aa:	06078793          	addi	a5,a5,96 # 10060 <_data_lma+0xc4e0>
    19ae:	d03e                	sw	a5,32(sp)
    19b0:	100c                	addi	a1,sp,32
    19b2:	5dc00793          	li	a5,1500
    19b6:	40000537          	lui	a0,0x40000
    19ba:	02f11323          	sh	a5,38(sp)
    19be:	02011423          	sh	zero,40(sp)
    19c2:	976ff0ef          	jal	ra,b38 <TIM_OC1Init>
    19c6:	45a1                	li	a1,8
    19c8:	40000537          	lui	a0,0x40000
    19cc:	a32ff0ef          	jal	ra,bfe <TIM_OC1PreloadConfig>
    19d0:	4585                	li	a1,1
    19d2:	40000537          	lui	a0,0x40000
    19d6:	a0eff0ef          	jal	ra,be4 <TIM_ARRPreloadConfig>
    19da:	4585                	li	a1,1
    19dc:	40000537          	lui	a0,0x40000
    19e0:	9daff0ef          	jal	ra,bba <TIM_Cmd>
    19e4:	4585                	li	a1,1
    19e6:	4511                	li	a0,4
    19e8:	800ff0ef          	jal	ra,9e8 <RCC_APB2PeriphClockCmd>
    19ec:	4785                	li	a5,1
    19ee:	82fc                	sh	a5,20(sp)
    19f0:	40011537          	lui	a0,0x40011
    19f4:	47e1                	li	a5,24
    19f6:	ce3e                	sw	a5,28(sp)
    19f8:	084c                	addi	a1,sp,20
    19fa:	478d                	li	a5,3
    19fc:	80050513          	addi	a0,a0,-2048 # 40010800 <_eusrstack+0x20008800>
    1a00:	cc3e                	sw	a5,24(sp)
    1a02:	d4bfe0ef          	jal	ra,74c <GPIO_Init>
    1a06:	6145                	addi	sp,sp,48
    1a08:	80dfe06f          	j	214 <__riscv_restore_0>

00001a0c <stir_360>:
    1a0c:	fe4fe2ef          	jal	t0,1f0 <__riscv_save_0>
    1a10:	4785                	li	a5,1
    1a12:	02f51963          	bne	a0,a5,1a44 <stir_360+0x38>
    1a16:	06400713          	li	a4,100
    1a1a:	0ff5f793          	andi	a5,a1,255
    1a1e:	00b77463          	bgeu	a4,a1,1a26 <stir_360+0x1a>
    1a22:	06400793          	li	a5,100
    1a26:	45a9                	li	a1,10
    1a28:	02b785b3          	mul	a1,a5,a1
    1a2c:	5dc00793          	li	a5,1500
    1a30:	8f8d                	sub	a5,a5,a1
    1a32:	07c2                	slli	a5,a5,0x10
    1a34:	83c1                	srli	a5,a5,0x10
    1a36:	85be                	mv	a1,a5
    1a38:	40000537          	lui	a0,0x40000
    1a3c:	9d0ff0ef          	jal	ra,c0c <TIM_SetCompare1>
    1a40:	fd4fe06f          	j	214 <__riscv_restore_0>
    1a44:	4709                	li	a4,2
    1a46:	5dc00793          	li	a5,1500
    1a4a:	fee516e3          	bne	a0,a4,1a36 <stir_360+0x2a>
    1a4e:	06400713          	li	a4,100
    1a52:	0ff5f793          	andi	a5,a1,255
    1a56:	00b77463          	bgeu	a4,a1,1a5e <stir_360+0x52>
    1a5a:	06400793          	li	a5,100
    1a5e:	45a9                	li	a1,10
    1a60:	02b787b3          	mul	a5,a5,a1
    1a64:	5dc78793          	addi	a5,a5,1500
    1a68:	b7f9                	j	1a36 <stir_360+0x2a>

00001a6a <stir>:
    1a6a:	f86fe2ef          	jal	t0,1f0 <__riscv_save_0>
    1a6e:	c519                	beqz	a0,1a7c <stir+0x12>
    1a70:	03200593          	li	a1,50
    1a74:	4505                	li	a0,1
    1a76:	3f59                	jal	1a0c <stir_360>
    1a78:	f9cfe06f          	j	214 <__riscv_restore_0>
    1a7c:	5dc00593          	li	a1,1500
    1a80:	40000537          	lui	a0,0x40000
    1a84:	988ff0ef          	jal	ra,c0c <TIM_SetCompare1>
    1a88:	bfc5                	j	1a78 <stir+0xe>

00001a8a <WaterLevel_Init>:
    1a8a:	f66fe2ef          	jal	t0,1f0 <__riscv_save_0>
    1a8e:	1141                	addi	sp,sp,-16
    1a90:	4585                	li	a1,1
    1a92:	4541                	li	a0,16
    1a94:	c202                	sw	zero,4(sp)
    1a96:	c402                	sw	zero,8(sp)
    1a98:	c602                	sw	zero,12(sp)
    1a9a:	f4ffe0ef          	jal	ra,9e8 <RCC_APB2PeriphClockCmd>
    1a9e:	47c1                	li	a5,16
    1aa0:	827c                	sh	a5,4(sp)
    1aa2:	478d                	li	a5,3
    1aa4:	c43e                	sw	a5,8(sp)
    1aa6:	004c                	addi	a1,sp,4
    1aa8:	04800793          	li	a5,72
    1aac:	40011537          	lui	a0,0x40011
    1ab0:	c63e                	sw	a5,12(sp)
    1ab2:	c9bfe0ef          	jal	ra,74c <GPIO_Init>
    1ab6:	0141                	addi	sp,sp,16
    1ab8:	f5cfe06f          	j	214 <__riscv_restore_0>

00001abc <WaterLevel_Detect>:
    1abc:	f34fe2ef          	jal	t0,1f0 <__riscv_save_0>
    1ac0:	45c1                	li	a1,16
    1ac2:	40011537          	lui	a0,0x40011
    1ac6:	d47fe0ef          	jal	ra,80c <GPIO_ReadInputDataBit>
    1aca:	00153513          	seqz	a0,a0
    1ace:	f46fe06f          	j	214 <__riscv_restore_0>

00001ad2 <WaterPump_Init>:
    1ad2:	f1efe2ef          	jal	t0,1f0 <__riscv_save_0>
    1ad6:	1141                	addi	sp,sp,-16
    1ad8:	4585                	li	a1,1
    1ada:	4541                	li	a0,16
    1adc:	f0dfe0ef          	jal	ra,9e8 <RCC_APB2PeriphClockCmd>
    1ae0:	6789                	lui	a5,0x2
    1ae2:	440d                	li	s0,3
    1ae4:	44c1                	li	s1,16
    1ae6:	004c                	addi	a1,sp,4
    1ae8:	40011537          	lui	a0,0x40011
    1aec:	827c                	sh	a5,4(sp)
    1aee:	c626                	sw	s1,12(sp)
    1af0:	c422                	sw	s0,8(sp)
    1af2:	c5bfe0ef          	jal	ra,74c <GPIO_Init>
    1af6:	6589                	lui	a1,0x2
    1af8:	40011537          	lui	a0,0x40011
    1afc:	d1ffe0ef          	jal	ra,81a <GPIO_ResetBits>
    1b00:	4585                	li	a1,1
    1b02:	4541                	li	a0,16
    1b04:	ee5fe0ef          	jal	ra,9e8 <RCC_APB2PeriphClockCmd>
    1b08:	6785                	lui	a5,0x1
    1b0a:	80078793          	addi	a5,a5,-2048 # 800 <__stack_size>
    1b0e:	004c                	addi	a1,sp,4
    1b10:	40011537          	lui	a0,0x40011
    1b14:	827c                	sh	a5,4(sp)
    1b16:	c626                	sw	s1,12(sp)
    1b18:	c422                	sw	s0,8(sp)
    1b1a:	c33fe0ef          	jal	ra,74c <GPIO_Init>
    1b1e:	6585                	lui	a1,0x1
    1b20:	80058593          	addi	a1,a1,-2048 # 800 <__stack_size>
    1b24:	40011537          	lui	a0,0x40011
    1b28:	cf3fe0ef          	jal	ra,81a <GPIO_ResetBits>
    1b2c:	86018793          	addi	a5,gp,-1952 # 20000110 <g_pump_ctrl>
    1b30:	4705                	li	a4,1
    1b32:	0007a023          	sw	zero,0(a5)
    1b36:	a3da                	sh	a4,4(a5)
    1b38:	0007a423          	sw	zero,8(a5)
    1b3c:	0007a623          	sw	zero,12(a5)
    1b40:	0007a823          	sw	zero,16(a5)
    1b44:	abda                	sh	a4,20(a5)
    1b46:	0007ac23          	sw	zero,24(a5)
    1b4a:	0007ae23          	sw	zero,28(a5)
    1b4e:	0141                	addi	sp,sp,16
    1b50:	ec4fe06f          	j	214 <__riscv_restore_0>

00001b54 <WaterPump_Control>:
    1b54:	4705                	li	a4,1
    1b56:	04a76a63          	bltu	a4,a0,1baa <WaterPump_Control+0x56>
    1b5a:	e96fe2ef          	jal	t0,1f0 <__riscv_save_0>
    1b5e:	86018793          	addi	a5,gp,-1952 # 20000110 <g_pump_ctrl>
    1b62:	00451493          	slli	s1,a0,0x4
    1b66:	97a6                	add	a5,a5,s1
    1b68:	23dc                	lbu	a5,4(a5)
    1b6a:	86018413          	addi	s0,gp,-1952 # 20000110 <g_pump_ctrl>
    1b6e:	cf99                	beqz	a5,1b8c <WaterPump_Control+0x38>
    1b70:	02e59063          	bne	a1,a4,1b90 <WaterPump_Control+0x3c>
    1b74:	6589                	lui	a1,0x2
    1b76:	c501                	beqz	a0,1b7e <WaterPump_Control+0x2a>
    1b78:	6585                	lui	a1,0x1
    1b7a:	80058593          	addi	a1,a1,-2048 # 800 <__stack_size>
    1b7e:	40011537          	lui	a0,0x40011
    1b82:	c95fe0ef          	jal	ra,816 <GPIO_SetBits>
    1b86:	9426                	add	s0,s0,s1
    1b88:	4785                	li	a5,1
    1b8a:	c01c                	sw	a5,0(s0)
    1b8c:	e88fe06f          	j	214 <__riscv_restore_0>
    1b90:	6589                	lui	a1,0x2
    1b92:	c501                	beqz	a0,1b9a <WaterPump_Control+0x46>
    1b94:	6585                	lui	a1,0x1
    1b96:	80058593          	addi	a1,a1,-2048 # 800 <__stack_size>
    1b9a:	40011537          	lui	a0,0x40011
    1b9e:	9426                	add	s0,s0,s1
    1ba0:	c7bfe0ef          	jal	ra,81a <GPIO_ResetBits>
    1ba4:	00042023          	sw	zero,0(s0)
    1ba8:	b7d5                	j	1b8c <WaterPump_Control+0x38>
    1baa:	8082                	ret

00001bac <Delay_Init>:
    1bac:	200007b7          	lui	a5,0x20000
    1bb0:	0b07a783          	lw	a5,176(a5) # 200000b0 <SystemCoreClock>
    1bb4:	007a1737          	lui	a4,0x7a1
    1bb8:	20070713          	addi	a4,a4,512 # 7a1200 <_data_lma+0x79d680>
    1bbc:	02e7d7b3          	divu	a5,a5,a4
    1bc0:	0ff7f793          	andi	a5,a5,255
    1bc4:	82f18723          	sb	a5,-2002(gp) # 200000de <p_us>
    1bc8:	3e800713          	li	a4,1000
    1bcc:	02e787b3          	mul	a5,a5,a4
    1bd0:	82f19623          	sh	a5,-2004(gp) # 200000dc <p_ms>
    1bd4:	8082                	ret

00001bd6 <Delay_Ms>:
    1bd6:	e000f7b7          	lui	a5,0xe000f
    1bda:	43d8                	lw	a4,4(a5)
    1bdc:	4681                	li	a3,0
    1bde:	9b79                	andi	a4,a4,-2
    1be0:	c3d8                	sw	a4,4(a5)
    1be2:	82c1d703          	lhu	a4,-2004(gp) # 200000dc <p_ms>
    1be6:	02a70633          	mul	a2,a4,a0
    1bea:	cb90                	sw	a2,16(a5)
    1bec:	cbd4                	sw	a3,20(a5)
    1bee:	4398                	lw	a4,0(a5)
    1bf0:	01076713          	ori	a4,a4,16
    1bf4:	c398                	sw	a4,0(a5)
    1bf6:	4398                	lw	a4,0(a5)
    1bf8:	02176713          	ori	a4,a4,33
    1bfc:	c398                	sw	a4,0(a5)
    1bfe:	43d8                	lw	a4,4(a5)
    1c00:	8b05                	andi	a4,a4,1
    1c02:	df75                	beqz	a4,1bfe <Delay_Ms+0x28>
    1c04:	4398                	lw	a4,0(a5)
    1c06:	9b79                	andi	a4,a4,-2
    1c08:	c398                	sw	a4,0(a5)
    1c0a:	8082                	ret

00001c0c <USART_Printf_Init>:
    1c0c:	de4fe2ef          	jal	t0,1f0 <__riscv_save_0>
    1c10:	842a                	mv	s0,a0
    1c12:	6511                	lui	a0,0x4
    1c14:	1101                	addi	sp,sp,-32
    1c16:	4585                	li	a1,1
    1c18:	0511                	addi	a0,a0,4
    1c1a:	dcffe0ef          	jal	ra,9e8 <RCC_APB2PeriphClockCmd>
    1c1e:	20000793          	li	a5,512
    1c22:	827c                	sh	a5,4(sp)
    1c24:	40011537          	lui	a0,0x40011
    1c28:	478d                	li	a5,3
    1c2a:	c43e                	sw	a5,8(sp)
    1c2c:	004c                	addi	a1,sp,4
    1c2e:	47e1                	li	a5,24
    1c30:	80050513          	addi	a0,a0,-2048 # 40010800 <_eusrstack+0x20008800>
    1c34:	c63e                	sw	a5,12(sp)
    1c36:	b17fe0ef          	jal	ra,74c <GPIO_Init>
    1c3a:	c822                	sw	s0,16(sp)
    1c3c:	40014437          	lui	s0,0x40014
    1c40:	000807b7          	lui	a5,0x80
    1c44:	080c                	addi	a1,sp,16
    1c46:	80040513          	addi	a0,s0,-2048 # 40013800 <_eusrstack+0x2000b800>
    1c4a:	cc3e                	sw	a5,24(sp)
    1c4c:	ca02                	sw	zero,20(sp)
    1c4e:	00011e23          	sh	zero,28(sp)
    1c52:	fe3fe0ef          	jal	ra,c34 <USART_Init>
    1c56:	4585                	li	a1,1
    1c58:	80040513          	addi	a0,s0,-2048
    1c5c:	866ff0ef          	jal	ra,cc2 <USART_Cmd>
    1c60:	6105                	addi	sp,sp,32
    1c62:	db2fe06f          	j	214 <__riscv_restore_0>

00001c66 <_write>:
    1c66:	d70fe2ef          	jal	t0,1d6 <__riscv_save_4>
    1c6a:	400144b7          	lui	s1,0x40014
    1c6e:	89ae                	mv	s3,a1
    1c70:	8932                	mv	s2,a2
    1c72:	4401                	li	s0,0
    1c74:	80048493          	addi	s1,s1,-2048 # 40013800 <_eusrstack+0x2000b800>
    1c78:	01244563          	blt	s0,s2,1c82 <_write+0x1c>
    1c7c:	854a                	mv	a0,s2
    1c7e:	d8cfe06f          	j	20a <__riscv_restore_4>
    1c82:	04000593          	li	a1,64
    1c86:	8526                	mv	a0,s1
    1c88:	858ff0ef          	jal	ra,ce0 <USART_GetFlagStatus>
    1c8c:	d97d                	beqz	a0,1c82 <_write+0x1c>
    1c8e:	008987b3          	add	a5,s3,s0
    1c92:	00078583          	lb	a1,0(a5) # 80000 <_data_lma+0x7c480>
    1c96:	8526                	mv	a0,s1
    1c98:	0405                	addi	s0,s0,1
    1c9a:	05c2                	slli	a1,a1,0x10
    1c9c:	81c1                	srli	a1,a1,0x10
    1c9e:	83aff0ef          	jal	ra,cd8 <USART_SendData>
    1ca2:	bfd9                	j	1c78 <_write+0x12>

00001ca4 <_sbrk>:
    1ca4:	80818713          	addi	a4,gp,-2040 # 200000b8 <curbrk.5274>
    1ca8:	431c                	lw	a5,0(a4)
    1caa:	88418693          	addi	a3,gp,-1916 # 20000134 <_ebss>
    1cae:	953e                	add	a0,a0,a5
    1cb0:	00d56b63          	bltu	a0,a3,1cc6 <_sbrk+0x22>
    1cb4:	200086b7          	lui	a3,0x20008
    1cb8:	80068693          	addi	a3,a3,-2048 # 20007800 <_heap_end>
    1cbc:	00a6e563          	bltu	a3,a0,1cc6 <_sbrk+0x22>
    1cc0:	c308                	sw	a0,0(a4)
    1cc2:	853e                	mv	a0,a5
    1cc4:	8082                	ret
    1cc6:	57fd                	li	a5,-1
    1cc8:	bfed                	j	1cc2 <_sbrk+0x1e>

00001cca <__gesf2>:
    1cca:	01755693          	srli	a3,a0,0x17
    1cce:	008007b7          	lui	a5,0x800
    1cd2:	17fd                	addi	a5,a5,-1
    1cd4:	0175d613          	srli	a2,a1,0x17
    1cd8:	0ff6f693          	andi	a3,a3,255
    1cdc:	0ff00813          	li	a6,255
    1ce0:	00a7f8b3          	and	a7,a5,a0
    1ce4:	01f55713          	srli	a4,a0,0x1f
    1ce8:	8fed                	and	a5,a5,a1
    1cea:	0ff67613          	andi	a2,a2,255
    1cee:	81fd                	srli	a1,a1,0x1f
    1cf0:	03068163          	beq	a3,a6,1d12 <__gesf2+0x48>
    1cf4:	01060c63          	beq	a2,a6,1d0c <__gesf2+0x42>
    1cf8:	e28d                	bnez	a3,1d1a <__gesf2+0x50>
    1cfa:	e211                	bnez	a2,1cfe <__gesf2+0x34>
    1cfc:	c3b1                	beqz	a5,1d40 <__gesf2+0x76>
    1cfe:	02088e63          	beqz	a7,1d3a <__gesf2+0x70>
    1d02:	04b70563          	beq	a4,a1,1d4c <__gesf2+0x82>
    1d06:	4505                	li	a0,1
    1d08:	e71d                	bnez	a4,1d36 <__gesf2+0x6c>
    1d0a:	8082                	ret
    1d0c:	d7f5                	beqz	a5,1cf8 <__gesf2+0x2e>
    1d0e:	5579                	li	a0,-2
    1d10:	8082                	ret
    1d12:	fe089ee3          	bnez	a7,1d0e <__gesf2+0x44>
    1d16:	02d60963          	beq	a2,a3,1d48 <__gesf2+0x7e>
    1d1a:	e211                	bnez	a2,1d1e <__gesf2+0x54>
    1d1c:	d7ed                	beqz	a5,1d06 <__gesf2+0x3c>
    1d1e:	feb714e3          	bne	a4,a1,1d06 <__gesf2+0x3c>
    1d22:	fed642e3          	blt	a2,a3,1d06 <__gesf2+0x3c>
    1d26:	00c6c763          	blt	a3,a2,1d34 <__gesf2+0x6a>
    1d2a:	fd17eee3          	bltu	a5,a7,1d06 <__gesf2+0x3c>
    1d2e:	4501                	li	a0,0
    1d30:	fcf8fde3          	bgeu	a7,a5,1d0a <__gesf2+0x40>
    1d34:	e701                	bnez	a4,1d3c <__gesf2+0x72>
    1d36:	557d                	li	a0,-1
    1d38:	8082                	ret
    1d3a:	ddf5                	beqz	a1,1d36 <__gesf2+0x6c>
    1d3c:	4505                	li	a0,1
    1d3e:	8082                	ret
    1d40:	4501                	li	a0,0
    1d42:	fc0892e3          	bnez	a7,1d06 <__gesf2+0x3c>
    1d46:	8082                	ret
    1d48:	dbf9                	beqz	a5,1d1e <__gesf2+0x54>
    1d4a:	b7d1                	j	1d0e <__gesf2+0x44>
    1d4c:	4681                	li	a3,0
    1d4e:	bfe1                	j	1d26 <__gesf2+0x5c>

00001d50 <__lesf2>:
    1d50:	01755693          	srli	a3,a0,0x17
    1d54:	008007b7          	lui	a5,0x800
    1d58:	17fd                	addi	a5,a5,-1
    1d5a:	0175d613          	srli	a2,a1,0x17
    1d5e:	0ff6f693          	andi	a3,a3,255
    1d62:	0ff00813          	li	a6,255
    1d66:	00a7f8b3          	and	a7,a5,a0
    1d6a:	01f55713          	srli	a4,a0,0x1f
    1d6e:	8fed                	and	a5,a5,a1
    1d70:	0ff67613          	andi	a2,a2,255
    1d74:	81fd                	srli	a1,a1,0x1f
    1d76:	03068763          	beq	a3,a6,1da4 <__lesf2+0x54>
    1d7a:	01060963          	beq	a2,a6,1d8c <__lesf2+0x3c>
    1d7e:	ea85                	bnez	a3,1dae <__lesf2+0x5e>
    1d80:	ea11                	bnez	a2,1d94 <__lesf2+0x44>
    1d82:	eb89                	bnez	a5,1d94 <__lesf2+0x44>
    1d84:	4501                	li	a0,0
    1d86:	00089b63          	bnez	a7,1d9c <__lesf2+0x4c>
    1d8a:	8082                	ret
    1d8c:	4509                	li	a0,2
    1d8e:	fff5                	bnez	a5,1d8a <__lesf2+0x3a>
    1d90:	dae5                	beqz	a3,1d80 <__lesf2+0x30>
    1d92:	a831                	j	1dae <__lesf2+0x5e>
    1d94:	02088c63          	beqz	a7,1dcc <__lesf2+0x7c>
    1d98:	04b70063          	beq	a4,a1,1dd8 <__lesf2+0x88>
    1d9c:	4505                	li	a0,1
    1d9e:	d775                	beqz	a4,1d8a <__lesf2+0x3a>
    1da0:	557d                	li	a0,-1
    1da2:	8082                	ret
    1da4:	4509                	li	a0,2
    1da6:	02089863          	bnez	a7,1dd6 <__lesf2+0x86>
    1daa:	02d60463          	beq	a2,a3,1dd2 <__lesf2+0x82>
    1dae:	e211                	bnez	a2,1db2 <__lesf2+0x62>
    1db0:	d7f5                	beqz	a5,1d9c <__lesf2+0x4c>
    1db2:	feb715e3          	bne	a4,a1,1d9c <__lesf2+0x4c>
    1db6:	fed643e3          	blt	a2,a3,1d9c <__lesf2+0x4c>
    1dba:	00c6c763          	blt	a3,a2,1dc8 <__lesf2+0x78>
    1dbe:	fd17efe3          	bltu	a5,a7,1d9c <__lesf2+0x4c>
    1dc2:	4501                	li	a0,0
    1dc4:	fcf8f3e3          	bgeu	a7,a5,1d8a <__lesf2+0x3a>
    1dc8:	e319                	bnez	a4,1dce <__lesf2+0x7e>
    1dca:	bfd9                	j	1da0 <__lesf2+0x50>
    1dcc:	d9f1                	beqz	a1,1da0 <__lesf2+0x50>
    1dce:	4505                	li	a0,1
    1dd0:	8082                	ret
    1dd2:	d3e5                	beqz	a5,1db2 <__lesf2+0x62>
    1dd4:	8082                	ret
    1dd6:	8082                	ret
    1dd8:	4681                	li	a3,0
    1dda:	b7c5                	j	1dba <__lesf2+0x6a>

00001ddc <__mulsf3>:
    1ddc:	7179                	addi	sp,sp,-48
    1dde:	d422                	sw	s0,40(sp)
    1de0:	01755413          	srli	s0,a0,0x17
    1de4:	ce4e                	sw	s3,28(sp)
    1de6:	cc52                	sw	s4,24(sp)
    1de8:	00951993          	slli	s3,a0,0x9
    1dec:	d606                	sw	ra,44(sp)
    1dee:	d226                	sw	s1,36(sp)
    1df0:	d04a                	sw	s2,32(sp)
    1df2:	ca56                	sw	s5,20(sp)
    1df4:	c85a                	sw	s6,16(sp)
    1df6:	0ff47413          	andi	s0,s0,255
    1dfa:	0099d993          	srli	s3,s3,0x9
    1dfe:	01f55a13          	srli	s4,a0,0x1f
    1e02:	c469                	beqz	s0,1ecc <__mulsf3+0xf0>
    1e04:	0ff00793          	li	a5,255
    1e08:	0ef40863          	beq	s0,a5,1ef8 <__mulsf3+0x11c>
    1e0c:	00399793          	slli	a5,s3,0x3
    1e10:	04000737          	lui	a4,0x4000
    1e14:	00e7e9b3          	or	s3,a5,a4
    1e18:	f8140413          	addi	s0,s0,-127
    1e1c:	4481                	li	s1,0
    1e1e:	4b01                	li	s6,0
    1e20:	0175d713          	srli	a4,a1,0x17
    1e24:	00959a93          	slli	s5,a1,0x9
    1e28:	0ff77713          	andi	a4,a4,255
    1e2c:	009ada93          	srli	s5,s5,0x9
    1e30:	01f5d913          	srli	s2,a1,0x1f
    1e34:	cf45                	beqz	a4,1eec <__mulsf3+0x110>
    1e36:	0ff00793          	li	a5,255
    1e3a:	02f70c63          	beq	a4,a5,1e72 <__mulsf3+0x96>
    1e3e:	0a8e                	slli	s5,s5,0x3
    1e40:	f8170713          	addi	a4,a4,-127 # 3ffff81 <_data_lma+0x3ffc401>
    1e44:	040007b7          	lui	a5,0x4000
    1e48:	00faeab3          	or	s5,s5,a5
    1e4c:	943a                	add	s0,s0,a4
    1e4e:	4601                	li	a2,0
    1e50:	012a4533          	xor	a0,s4,s2
    1e54:	47bd                	li	a5,15
    1e56:	86aa                	mv	a3,a0
    1e58:	00140593          	addi	a1,s0,1
    1e5c:	1097e063          	bltu	a5,s1,1f5c <__mulsf3+0x180>
    1e60:	00002717          	auipc	a4,0x2
    1e64:	b4470713          	addi	a4,a4,-1212 # 39a4 <_read+0x210>
    1e68:	048a                	slli	s1,s1,0x2
    1e6a:	94ba                	add	s1,s1,a4
    1e6c:	409c                	lw	a5,0(s1)
    1e6e:	97ba                	add	a5,a5,a4
    1e70:	8782                	jr	a5
    1e72:	0ff40413          	addi	s0,s0,255
    1e76:	0c0a9663          	bnez	s5,1f42 <__mulsf3+0x166>
    1e7a:	0024e493          	ori	s1,s1,2
    1e7e:	4609                	li	a2,2
    1e80:	bfc1                	j	1e50 <__mulsf3+0x74>
    1e82:	4501                	li	a0,0
    1e84:	0ff00713          	li	a4,255
    1e88:	004007b7          	lui	a5,0x400
    1e8c:	50b2                	lw	ra,44(sp)
    1e8e:	5422                	lw	s0,40(sp)
    1e90:	07a6                	slli	a5,a5,0x9
    1e92:	075e                	slli	a4,a4,0x17
    1e94:	83a5                	srli	a5,a5,0x9
    1e96:	057e                	slli	a0,a0,0x1f
    1e98:	8fd9                	or	a5,a5,a4
    1e9a:	5492                	lw	s1,36(sp)
    1e9c:	5902                	lw	s2,32(sp)
    1e9e:	49f2                	lw	s3,28(sp)
    1ea0:	4a62                	lw	s4,24(sp)
    1ea2:	4ad2                	lw	s5,20(sp)
    1ea4:	4b42                	lw	s6,16(sp)
    1ea6:	8d5d                	or	a0,a0,a5
    1ea8:	6145                	addi	sp,sp,48
    1eaa:	8082                	ret
    1eac:	86ca                	mv	a3,s2
    1eae:	89d6                	mv	s3,s5
    1eb0:	8b32                	mv	s6,a2
    1eb2:	4789                	li	a5,2
    1eb4:	08fb0f63          	beq	s6,a5,1f52 <__mulsf3+0x176>
    1eb8:	478d                	li	a5,3
    1eba:	fcfb04e3          	beq	s6,a5,1e82 <__mulsf3+0xa6>
    1ebe:	4785                	li	a5,1
    1ec0:	8536                	mv	a0,a3
    1ec2:	1afb1063          	bne	s6,a5,2062 <__mulsf3+0x286>
    1ec6:	4701                	li	a4,0
    1ec8:	4781                	li	a5,0
    1eca:	b7c9                	j	1e8c <__mulsf3+0xb0>
    1ecc:	04099d63          	bnez	s3,1f26 <__mulsf3+0x14a>
    1ed0:	0175d713          	srli	a4,a1,0x17
    1ed4:	00959a93          	slli	s5,a1,0x9
    1ed8:	0ff77713          	andi	a4,a4,255
    1edc:	4491                	li	s1,4
    1ede:	4401                	li	s0,0
    1ee0:	4b05                	li	s6,1
    1ee2:	009ada93          	srli	s5,s5,0x9
    1ee6:	01f5d913          	srli	s2,a1,0x1f
    1eea:	f731                	bnez	a4,1e36 <__mulsf3+0x5a>
    1eec:	000a9d63          	bnez	s5,1f06 <__mulsf3+0x12a>
    1ef0:	0014e493          	ori	s1,s1,1
    1ef4:	4605                	li	a2,1
    1ef6:	bfa9                	j	1e50 <__mulsf3+0x74>
    1ef8:	02099263          	bnez	s3,1f1c <__mulsf3+0x140>
    1efc:	44a1                	li	s1,8
    1efe:	0ff00413          	li	s0,255
    1f02:	4b09                	li	s6,2
    1f04:	bf31                	j	1e20 <__mulsf3+0x44>
    1f06:	8556                	mv	a0,s5
    1f08:	2db9                	jal	2566 <__clzsi2>
    1f0a:	ffb50793          	addi	a5,a0,-5
    1f0e:	8c09                	sub	s0,s0,a0
    1f10:	00fa9ab3          	sll	s5,s5,a5
    1f14:	f8a40413          	addi	s0,s0,-118
    1f18:	4601                	li	a2,0
    1f1a:	bf1d                	j	1e50 <__mulsf3+0x74>
    1f1c:	44b1                	li	s1,12
    1f1e:	0ff00413          	li	s0,255
    1f22:	4b0d                	li	s6,3
    1f24:	bdf5                	j	1e20 <__mulsf3+0x44>
    1f26:	854e                	mv	a0,s3
    1f28:	c62e                	sw	a1,12(sp)
    1f2a:	2d35                	jal	2566 <__clzsi2>
    1f2c:	ffb50793          	addi	a5,a0,-5
    1f30:	f8a00413          	li	s0,-118
    1f34:	00f999b3          	sll	s3,s3,a5
    1f38:	8c09                	sub	s0,s0,a0
    1f3a:	4481                	li	s1,0
    1f3c:	4b01                	li	s6,0
    1f3e:	45b2                	lw	a1,12(sp)
    1f40:	b5c5                	j	1e20 <__mulsf3+0x44>
    1f42:	0034e493          	ori	s1,s1,3
    1f46:	460d                	li	a2,3
    1f48:	b721                	j	1e50 <__mulsf3+0x74>
    1f4a:	4789                	li	a5,2
    1f4c:	86d2                	mv	a3,s4
    1f4e:	f6fb15e3          	bne	s6,a5,1eb8 <__mulsf3+0xdc>
    1f52:	8536                	mv	a0,a3
    1f54:	0ff00713          	li	a4,255
    1f58:	4781                	li	a5,0
    1f5a:	bf0d                	j	1e8c <__mulsf3+0xb0>
    1f5c:	6341                	lui	t1,0x10
    1f5e:	fff30693          	addi	a3,t1,-1 # ffff <_data_lma+0xc47f>
    1f62:	0109d613          	srli	a2,s3,0x10
    1f66:	010ad893          	srli	a7,s5,0x10
    1f6a:	00d9f7b3          	and	a5,s3,a3
    1f6e:	00dafab3          	and	s5,s5,a3
    1f72:	03578833          	mul	a6,a5,s5
    1f76:	02f889b3          	mul	s3,a7,a5
    1f7a:	01085713          	srli	a4,a6,0x10
    1f7e:	03560ab3          	mul	s5,a2,s5
    1f82:	99d6                	add	s3,s3,s5
    1f84:	974e                	add	a4,a4,s3
    1f86:	03160633          	mul	a2,a2,a7
    1f8a:	01577363          	bgeu	a4,s5,1f90 <__mulsf3+0x1b4>
    1f8e:	961a                	add	a2,a2,t1
    1f90:	67c1                	lui	a5,0x10
    1f92:	17fd                	addi	a5,a5,-1
    1f94:	00f776b3          	and	a3,a4,a5
    1f98:	00f87833          	and	a6,a6,a5
    1f9c:	06c2                	slli	a3,a3,0x10
    1f9e:	96c2                	add	a3,a3,a6
    1fa0:	00669993          	slli	s3,a3,0x6
    1fa4:	01075793          	srli	a5,a4,0x10
    1fa8:	013039b3          	snez	s3,s3
    1fac:	82e9                	srli	a3,a3,0x1a
    1fae:	97b2                	add	a5,a5,a2
    1fb0:	079a                	slli	a5,a5,0x6
    1fb2:	00d9e6b3          	or	a3,s3,a3
    1fb6:	00d7e9b3          	or	s3,a5,a3
    1fba:	00499793          	slli	a5,s3,0x4
    1fbe:	0007d963          	bgez	a5,1fd0 <__mulsf3+0x1f4>
    1fc2:	0019d713          	srli	a4,s3,0x1
    1fc6:	0019f793          	andi	a5,s3,1
    1fca:	00f769b3          	or	s3,a4,a5
    1fce:	842e                	mv	s0,a1
    1fd0:	07f40713          	addi	a4,s0,127
    1fd4:	04e05063          	blez	a4,2014 <__mulsf3+0x238>
    1fd8:	0079f793          	andi	a5,s3,7
    1fdc:	c799                	beqz	a5,1fea <__mulsf3+0x20e>
    1fde:	00f9f793          	andi	a5,s3,15
    1fe2:	4691                	li	a3,4
    1fe4:	00d78363          	beq	a5,a3,1fea <__mulsf3+0x20e>
    1fe8:	0991                	addi	s3,s3,4
    1fea:	00499793          	slli	a5,s3,0x4
    1fee:	0007d963          	bgez	a5,2000 <__mulsf3+0x224>
    1ff2:	f80007b7          	lui	a5,0xf8000
    1ff6:	17fd                	addi	a5,a5,-1
    1ff8:	00f9f9b3          	and	s3,s3,a5
    1ffc:	08040713          	addi	a4,s0,128
    2000:	0fe00793          	li	a5,254
    2004:	04e7cb63          	blt	a5,a4,205a <__mulsf3+0x27e>
    2008:	00699793          	slli	a5,s3,0x6
    200c:	83a5                	srli	a5,a5,0x9
    200e:	0ff77713          	andi	a4,a4,255
    2012:	bdad                	j	1e8c <__mulsf3+0xb0>
    2014:	4785                	li	a5,1
    2016:	40e786b3          	sub	a3,a5,a4
    201a:	c711                	beqz	a4,2026 <__mulsf3+0x24a>
    201c:	466d                	li	a2,27
    201e:	4701                	li	a4,0
    2020:	4781                	li	a5,0
    2022:	e6d645e3          	blt	a2,a3,1e8c <__mulsf3+0xb0>
    2026:	09e40713          	addi	a4,s0,158
    202a:	00e99733          	sll	a4,s3,a4
    202e:	00e03733          	snez	a4,a4
    2032:	00d9d7b3          	srl	a5,s3,a3
    2036:	8fd9                	or	a5,a5,a4
    2038:	0077f713          	andi	a4,a5,7
    203c:	c719                	beqz	a4,204a <__mulsf3+0x26e>
    203e:	00f7f713          	andi	a4,a5,15
    2042:	4691                	li	a3,4
    2044:	00d70363          	beq	a4,a3,204a <__mulsf3+0x26e>
    2048:	0791                	addi	a5,a5,4
    204a:	00579713          	slli	a4,a5,0x5
    204e:	00074c63          	bltz	a4,2066 <__mulsf3+0x28a>
    2052:	079a                	slli	a5,a5,0x6
    2054:	83a5                	srli	a5,a5,0x9
    2056:	4701                	li	a4,0
    2058:	bd15                	j	1e8c <__mulsf3+0xb0>
    205a:	0ff00713          	li	a4,255
    205e:	4781                	li	a5,0
    2060:	b535                	j	1e8c <__mulsf3+0xb0>
    2062:	842e                	mv	s0,a1
    2064:	b7b5                	j	1fd0 <__mulsf3+0x1f4>
    2066:	4705                	li	a4,1
    2068:	4781                	li	a5,0
    206a:	b50d                	j	1e8c <__mulsf3+0xb0>

0000206c <__subsf3>:
    206c:	00800737          	lui	a4,0x800
    2070:	1141                	addi	sp,sp,-16
    2072:	177d                	addi	a4,a4,-1
    2074:	01755693          	srli	a3,a0,0x17
    2078:	0175d813          	srli	a6,a1,0x17
    207c:	00a777b3          	and	a5,a4,a0
    2080:	0ff6f693          	andi	a3,a3,255
    2084:	01f55e93          	srli	t4,a0,0x1f
    2088:	00b77633          	and	a2,a4,a1
    208c:	c226                	sw	s1,4(sp)
    208e:	c04a                	sw	s2,0(sp)
    2090:	0ff87813          	andi	a6,a6,255
    2094:	c606                	sw	ra,12(sp)
    2096:	c422                	sw	s0,8(sp)
    2098:	0ff00313          	li	t1,255
    209c:	8e3e                	mv	t3,a5
    209e:	8936                	mv	s2,a3
    20a0:	84f6                	mv	s1,t4
    20a2:	00379f13          	slli	t5,a5,0x3
    20a6:	81fd                	srli	a1,a1,0x1f
    20a8:	00361513          	slli	a0,a2,0x3
    20ac:	410688b3          	sub	a7,a3,a6
    20b0:	10680263          	beq	a6,t1,21b4 <__subsf3+0x148>
    20b4:	0015c593          	xori	a1,a1,1
    20b8:	10be8c63          	beq	t4,a1,21d0 <__subsf3+0x164>
    20bc:	0d105463          	blez	a7,2184 <__subsf3+0x118>
    20c0:	10081063          	bnez	a6,21c0 <__subsf3+0x154>
    20c4:	1e050463          	beqz	a0,22ac <__subsf3+0x240>
    20c8:	18fd                	addi	a7,a7,-1
    20ca:	40af07b3          	sub	a5,t5,a0
    20ce:	02088663          	beqz	a7,20fa <__subsf3+0x8e>
    20d2:	0ff00793          	li	a5,255
    20d6:	16f68863          	beq	a3,a5,2246 <__subsf3+0x1da>
    20da:	47ed                	li	a5,27
    20dc:	2917c763          	blt	a5,a7,236a <__subsf3+0x2fe>
    20e0:	02000713          	li	a4,32
    20e4:	41170733          	sub	a4,a4,a7
    20e8:	00e51733          	sll	a4,a0,a4
    20ec:	011557b3          	srl	a5,a0,a7
    20f0:	00e03733          	snez	a4,a4
    20f4:	8fd9                	or	a5,a5,a4
    20f6:	40ff07b3          	sub	a5,t5,a5
    20fa:	00579713          	slli	a4,a5,0x5
    20fe:	1a075163          	bgez	a4,22a0 <__subsf3+0x234>
    2102:	04000437          	lui	s0,0x4000
    2106:	147d                	addi	s0,s0,-1
    2108:	8c7d                	and	s0,s0,a5
    210a:	8522                	mv	a0,s0
    210c:	29a9                	jal	2566 <__clzsi2>
    210e:	156d                	addi	a0,a0,-5
    2110:	00a417b3          	sll	a5,s0,a0
    2114:	1b254e63          	blt	a0,s2,22d0 <__subsf3+0x264>
    2118:	41250533          	sub	a0,a0,s2
    211c:	00150413          	addi	s0,a0,1
    2120:	02000713          	li	a4,32
    2124:	8f01                	sub	a4,a4,s0
    2126:	00e79733          	sll	a4,a5,a4
    212a:	00e03733          	snez	a4,a4
    212e:	0087d7b3          	srl	a5,a5,s0
    2132:	8fd9                	or	a5,a5,a4
    2134:	4901                	li	s2,0
    2136:	0077f713          	andi	a4,a5,7
    213a:	c719                	beqz	a4,2148 <__subsf3+0xdc>
    213c:	00f7f713          	andi	a4,a5,15
    2140:	4691                	li	a3,4
    2142:	00d70363          	beq	a4,a3,2148 <__subsf3+0xdc>
    2146:	0791                	addi	a5,a5,4
    2148:	00579713          	slli	a4,a5,0x5
    214c:	14075e63          	bgez	a4,22a8 <__subsf3+0x23c>
    2150:	00190693          	addi	a3,s2,1
    2154:	0ff00713          	li	a4,255
    2158:	0014fe93          	andi	t4,s1,1
    215c:	16e68663          	beq	a3,a4,22c8 <__subsf3+0x25c>
    2160:	079a                	slli	a5,a5,0x6
    2162:	83a5                	srli	a5,a5,0x9
    2164:	0ff6f693          	andi	a3,a3,255
    2168:	07a6                	slli	a5,a5,0x9
    216a:	40b2                	lw	ra,12(sp)
    216c:	4422                	lw	s0,8(sp)
    216e:	0097d513          	srli	a0,a5,0x9
    2172:	06de                	slli	a3,a3,0x17
    2174:	01fe9793          	slli	a5,t4,0x1f
    2178:	8d55                	or	a0,a0,a3
    217a:	4492                	lw	s1,4(sp)
    217c:	4902                	lw	s2,0(sp)
    217e:	8d5d                	or	a0,a0,a5
    2180:	0141                	addi	sp,sp,16
    2182:	8082                	ret
    2184:	0a089263          	bnez	a7,2228 <__subsf3+0x1bc>
    2188:	00168713          	addi	a4,a3,1
    218c:	0fe77713          	andi	a4,a4,254
    2190:	14071b63          	bnez	a4,22e6 <__subsf3+0x27a>
    2194:	22069463          	bnez	a3,23bc <__subsf3+0x350>
    2198:	200f0263          	beqz	t5,239c <__subsf3+0x330>
    219c:	d571                	beqz	a0,2168 <__subsf3+0xfc>
    219e:	40af07b3          	sub	a5,t5,a0
    21a2:	00579713          	slli	a4,a5,0x5
    21a6:	22075563          	bgez	a4,23d0 <__subsf3+0x364>
    21aa:	41e507b3          	sub	a5,a0,t5
    21ae:	4901                	li	s2,0
    21b0:	84ae                	mv	s1,a1
    21b2:	b751                	j	2136 <__subsf3+0xca>
    21b4:	f00500e3          	beqz	a0,20b4 <__subsf3+0x48>
    21b8:	0abe8a63          	beq	t4,a1,226c <__subsf3+0x200>
    21bc:	fd1054e3          	blez	a7,2184 <__subsf3+0x118>
    21c0:	0ff00793          	li	a5,255
    21c4:	08f68163          	beq	a3,a5,2246 <__subsf3+0x1da>
    21c8:	040007b7          	lui	a5,0x4000
    21cc:	8d5d                	or	a0,a0,a5
    21ce:	b731                	j	20da <__subsf3+0x6e>
    21d0:	09105e63          	blez	a7,226c <__subsf3+0x200>
    21d4:	08080163          	beqz	a6,2256 <__subsf3+0x1ea>
    21d8:	0ff00793          	li	a5,255
    21dc:	06f68563          	beq	a3,a5,2246 <__subsf3+0x1da>
    21e0:	040007b7          	lui	a5,0x4000
    21e4:	8d5d                	or	a0,a0,a5
    21e6:	47ed                	li	a5,27
    21e8:	1f17c063          	blt	a5,a7,23c8 <__subsf3+0x35c>
    21ec:	02000713          	li	a4,32
    21f0:	41170733          	sub	a4,a4,a7
    21f4:	00e51733          	sll	a4,a0,a4
    21f8:	011557b3          	srl	a5,a0,a7
    21fc:	00e03733          	snez	a4,a4
    2200:	8fd9                	or	a5,a5,a4
    2202:	97fa                	add	a5,a5,t5
    2204:	00579713          	slli	a4,a5,0x5
    2208:	08075c63          	bgez	a4,22a0 <__subsf3+0x234>
    220c:	0905                	addi	s2,s2,1
    220e:	0ff00713          	li	a4,255
    2212:	0ae90b63          	beq	s2,a4,22c8 <__subsf3+0x25c>
    2216:	7e0006b7          	lui	a3,0x7e000
    221a:	0017d713          	srli	a4,a5,0x1
    221e:	16fd                	addi	a3,a3,-1
    2220:	8b85                	andi	a5,a5,1
    2222:	8f75                	and	a4,a4,a3
    2224:	8fd9                	or	a5,a5,a4
    2226:	bf01                	j	2136 <__subsf3+0xca>
    2228:	40d80733          	sub	a4,a6,a3
    222c:	10069563          	bnez	a3,2336 <__subsf3+0x2ca>
    2230:	140f0d63          	beqz	t5,238a <__subsf3+0x31e>
    2234:	177d                	addi	a4,a4,-1
    2236:	1a070363          	beqz	a4,23dc <__subsf3+0x370>
    223a:	0ff00793          	li	a5,255
    223e:	10f81463          	bne	a6,a5,2346 <__subsf3+0x2da>
    2242:	84ae                	mv	s1,a1
    2244:	8e32                	mv	t3,a2
    2246:	060e0f63          	beqz	t3,22c4 <__subsf3+0x258>
    224a:	4e81                	li	t4,0
    224c:	0ff00693          	li	a3,255
    2250:	004007b7          	lui	a5,0x400
    2254:	bf11                	j	2168 <__subsf3+0xfc>
    2256:	c939                	beqz	a0,22ac <__subsf3+0x240>
    2258:	18fd                	addi	a7,a7,-1
    225a:	00af07b3          	add	a5,t5,a0
    225e:	fa0883e3          	beqz	a7,2204 <__subsf3+0x198>
    2262:	0ff00793          	li	a5,255
    2266:	f8f690e3          	bne	a3,a5,21e6 <__subsf3+0x17a>
    226a:	bff1                	j	2246 <__subsf3+0x1da>
    226c:	08089963          	bnez	a7,22fe <__subsf3+0x292>
    2270:	00168913          	addi	s2,a3,1 # 7e000001 <_eusrstack+0x5dff8001>
    2274:	0fe97713          	andi	a4,s2,254
    2278:	12071663          	bnez	a4,23a4 <__subsf3+0x338>
    227c:	10069b63          	bnez	a3,2392 <__subsf3+0x326>
    2280:	160f0363          	beqz	t5,23e6 <__subsf3+0x37a>
    2284:	ee0502e3          	beqz	a0,2168 <__subsf3+0xfc>
    2288:	00af07b3          	add	a5,t5,a0
    228c:	00579713          	slli	a4,a5,0x5
    2290:	4901                	li	s2,0
    2292:	00075763          	bgez	a4,22a0 <__subsf3+0x234>
    2296:	fc000737          	lui	a4,0xfc000
    229a:	177d                	addi	a4,a4,-1
    229c:	8ff9                	and	a5,a5,a4
    229e:	4905                	li	s2,1
    22a0:	0077f713          	andi	a4,a5,7
    22a4:	e8071ce3          	bnez	a4,213c <__subsf3+0xd0>
    22a8:	0037de13          	srli	t3,a5,0x3
    22ac:	0ff00793          	li	a5,255
    22b0:	f8f90be3          	beq	s2,a5,2246 <__subsf3+0x1da>
    22b4:	009e1793          	slli	a5,t3,0x9
    22b8:	83a5                	srli	a5,a5,0x9
    22ba:	0ff97693          	andi	a3,s2,255
    22be:	0014fe93          	andi	t4,s1,1
    22c2:	b55d                	j	2168 <__subsf3+0xfc>
    22c4:	0014fe93          	andi	t4,s1,1
    22c8:	0ff00693          	li	a3,255
    22cc:	4781                	li	a5,0
    22ce:	bd69                	j	2168 <__subsf3+0xfc>
    22d0:	fc000737          	lui	a4,0xfc000
    22d4:	177d                	addi	a4,a4,-1
    22d6:	8ff9                	and	a5,a5,a4
    22d8:	0077f713          	andi	a4,a5,7
    22dc:	40a90933          	sub	s2,s2,a0
    22e0:	e4071ee3          	bnez	a4,213c <__subsf3+0xd0>
    22e4:	b7d1                	j	22a8 <__subsf3+0x23c>
    22e6:	40af0433          	sub	s0,t5,a0
    22ea:	00541793          	slli	a5,s0,0x5
    22ee:	0c07c363          	bltz	a5,23b4 <__subsf3+0x348>
    22f2:	e0041ce3          	bnez	s0,210a <__subsf3+0x9e>
    22f6:	4e81                	li	t4,0
    22f8:	4681                	li	a3,0
    22fa:	4781                	li	a5,0
    22fc:	b5b5                	j	2168 <__subsf3+0xfc>
    22fe:	40d80733          	sub	a4,a6,a3
    2302:	c6b5                	beqz	a3,236e <__subsf3+0x302>
    2304:	0ff00793          	li	a5,255
    2308:	f2f80ee3          	beq	a6,a5,2244 <__subsf3+0x1d8>
    230c:	040007b7          	lui	a5,0x4000
    2310:	00ff6f33          	or	t5,t5,a5
    2314:	47ed                	li	a5,27
    2316:	0ce7cd63          	blt	a5,a4,23f0 <__subsf3+0x384>
    231a:	02000793          	li	a5,32
    231e:	8f99                	sub	a5,a5,a4
    2320:	00ff17b3          	sll	a5,t5,a5
    2324:	00ef5733          	srl	a4,t5,a4
    2328:	00f037b3          	snez	a5,a5
    232c:	8f5d                	or	a4,a4,a5
    232e:	00a707b3          	add	a5,a4,a0
    2332:	8942                	mv	s2,a6
    2334:	bdc1                	j	2204 <__subsf3+0x198>
    2336:	0ff00793          	li	a5,255
    233a:	f0f804e3          	beq	a6,a5,2242 <__subsf3+0x1d6>
    233e:	040007b7          	lui	a5,0x4000
    2342:	00ff6f33          	or	t5,t5,a5
    2346:	47ed                	li	a5,27
    2348:	08e7c263          	blt	a5,a4,23cc <__subsf3+0x360>
    234c:	02000793          	li	a5,32
    2350:	8f99                	sub	a5,a5,a4
    2352:	00ff17b3          	sll	a5,t5,a5
    2356:	00ef5733          	srl	a4,t5,a4
    235a:	00f037b3          	snez	a5,a5
    235e:	8fd9                	or	a5,a5,a4
    2360:	40f507b3          	sub	a5,a0,a5
    2364:	8942                	mv	s2,a6
    2366:	84ae                	mv	s1,a1
    2368:	bb49                	j	20fa <__subsf3+0x8e>
    236a:	4785                	li	a5,1
    236c:	b369                	j	20f6 <__subsf3+0x8a>
    236e:	060f0e63          	beqz	t5,23ea <__subsf3+0x37e>
    2372:	177d                	addi	a4,a4,-1
    2374:	00af07b3          	add	a5,t5,a0
    2378:	8942                	mv	s2,a6
    237a:	e80705e3          	beqz	a4,2204 <__subsf3+0x198>
    237e:	0ff00793          	li	a5,255
    2382:	f8f819e3          	bne	a6,a5,2314 <__subsf3+0x2a8>
    2386:	8e32                	mv	t3,a2
    2388:	bd7d                	j	2246 <__subsf3+0x1da>
    238a:	8e32                	mv	t3,a2
    238c:	8942                	mv	s2,a6
    238e:	84ae                	mv	s1,a1
    2390:	bf31                	j	22ac <__subsf3+0x240>
    2392:	ea0f09e3          	beqz	t5,2244 <__subsf3+0x1d8>
    2396:	ea0508e3          	beqz	a0,2246 <__subsf3+0x1da>
    239a:	bd45                	j	224a <__subsf3+0x1de>
    239c:	cd0d                	beqz	a0,23d6 <__subsf3+0x36a>
    239e:	8eae                	mv	t4,a1
    23a0:	87b2                	mv	a5,a2
    23a2:	b3d9                	j	2168 <__subsf3+0xfc>
    23a4:	0ff00793          	li	a5,255
    23a8:	f2f900e3          	beq	s2,a5,22c8 <__subsf3+0x25c>
    23ac:	00af07b3          	add	a5,t5,a0
    23b0:	8385                	srli	a5,a5,0x1
    23b2:	b5fd                	j	22a0 <__subsf3+0x234>
    23b4:	41e50433          	sub	s0,a0,t5
    23b8:	84ae                	mv	s1,a1
    23ba:	bb81                	j	210a <__subsf3+0x9e>
    23bc:	fc0f1de3          	bnez	t5,2396 <__subsf3+0x32a>
    23c0:	e80505e3          	beqz	a0,224a <__subsf3+0x1de>
    23c4:	84ae                	mv	s1,a1
    23c6:	bdbd                	j	2244 <__subsf3+0x1d8>
    23c8:	4785                	li	a5,1
    23ca:	bd25                	j	2202 <__subsf3+0x196>
    23cc:	4785                	li	a5,1
    23ce:	bf49                	j	2360 <__subsf3+0x2f4>
    23d0:	4901                	li	s2,0
    23d2:	ec0797e3          	bnez	a5,22a0 <__subsf3+0x234>
    23d6:	4e81                	li	t4,0
    23d8:	4781                	li	a5,0
    23da:	b379                	j	2168 <__subsf3+0xfc>
    23dc:	41e507b3          	sub	a5,a0,t5
    23e0:	8942                	mv	s2,a6
    23e2:	84ae                	mv	s1,a1
    23e4:	bb19                	j	20fa <__subsf3+0x8e>
    23e6:	87b2                	mv	a5,a2
    23e8:	b341                	j	2168 <__subsf3+0xfc>
    23ea:	8e32                	mv	t3,a2
    23ec:	8942                	mv	s2,a6
    23ee:	bd7d                	j	22ac <__subsf3+0x240>
    23f0:	4705                	li	a4,1
    23f2:	bf35                	j	232e <__subsf3+0x2c2>

000023f4 <__floatsisf>:
    23f4:	1141                	addi	sp,sp,-16
    23f6:	c606                	sw	ra,12(sp)
    23f8:	c422                	sw	s0,8(sp)
    23fa:	c226                	sw	s1,4(sp)
    23fc:	cd0d                	beqz	a0,2436 <__floatsisf+0x42>
    23fe:	41f55793          	srai	a5,a0,0x1f
    2402:	00a7c433          	xor	s0,a5,a0
    2406:	8c1d                	sub	s0,s0,a5
    2408:	84aa                	mv	s1,a0
    240a:	8522                	mv	a0,s0
    240c:	2aa9                	jal	2566 <__clzsi2>
    240e:	09e00793          	li	a5,158
    2412:	40a78733          	sub	a4,a5,a0
    2416:	09600793          	li	a5,150
    241a:	80fd                	srli	s1,s1,0x1f
    241c:	02e7cc63          	blt	a5,a4,2454 <__floatsisf+0x60>
    2420:	46a1                	li	a3,8
    2422:	0ff77793          	andi	a5,a4,255
    2426:	00a6d563          	bge	a3,a0,2430 <__floatsisf+0x3c>
    242a:	1561                	addi	a0,a0,-8
    242c:	00a41433          	sll	s0,s0,a0
    2430:	0426                	slli	s0,s0,0x9
    2432:	8025                	srli	s0,s0,0x9
    2434:	a021                	j	243c <__floatsisf+0x48>
    2436:	4481                	li	s1,0
    2438:	4781                	li	a5,0
    243a:	4401                	li	s0,0
    243c:	0426                	slli	s0,s0,0x9
    243e:	00945513          	srli	a0,s0,0x9
    2442:	40b2                	lw	ra,12(sp)
    2444:	4422                	lw	s0,8(sp)
    2446:	07de                	slli	a5,a5,0x17
    2448:	04fe                	slli	s1,s1,0x1f
    244a:	8d5d                	or	a0,a0,a5
    244c:	8d45                	or	a0,a0,s1
    244e:	4492                	lw	s1,4(sp)
    2450:	0141                	addi	sp,sp,16
    2452:	8082                	ret
    2454:	09900793          	li	a5,153
    2458:	00e7dd63          	bge	a5,a4,2472 <__floatsisf+0x7e>
    245c:	01b50793          	addi	a5,a0,27
    2460:	4695                	li	a3,5
    2462:	00f417b3          	sll	a5,s0,a5
    2466:	8e89                	sub	a3,a3,a0
    2468:	00d45433          	srl	s0,s0,a3
    246c:	00f037b3          	snez	a5,a5
    2470:	8c5d                	or	s0,s0,a5
    2472:	4795                	li	a5,5
    2474:	00a7d663          	bge	a5,a0,2480 <__floatsisf+0x8c>
    2478:	ffb50793          	addi	a5,a0,-5
    247c:	00f41433          	sll	s0,s0,a5
    2480:	fc0006b7          	lui	a3,0xfc000
    2484:	16fd                	addi	a3,a3,-1
    2486:	00747793          	andi	a5,s0,7
    248a:	00d47633          	and	a2,s0,a3
    248e:	c385                	beqz	a5,24ae <__floatsisf+0xba>
    2490:	00f47793          	andi	a5,s0,15
    2494:	4591                	li	a1,4
    2496:	00b78c63          	beq	a5,a1,24ae <__floatsisf+0xba>
    249a:	0611                	addi	a2,a2,4
    249c:	00561793          	slli	a5,a2,0x5
    24a0:	0007d763          	bgez	a5,24ae <__floatsisf+0xba>
    24a4:	09f00793          	li	a5,159
    24a8:	8e75                	and	a2,a2,a3
    24aa:	40a78733          	sub	a4,a5,a0
    24ae:	00661413          	slli	s0,a2,0x6
    24b2:	8025                	srli	s0,s0,0x9
    24b4:	0ff77793          	andi	a5,a4,255
    24b8:	b751                	j	243c <__floatsisf+0x48>

000024ba <__extendsfdf2>:
    24ba:	01755713          	srli	a4,a0,0x17
    24be:	0ff77713          	andi	a4,a4,255
    24c2:	1141                	addi	sp,sp,-16
    24c4:	00170793          	addi	a5,a4,1 # fc000001 <_eusrstack+0xdbff8001>
    24c8:	c422                	sw	s0,8(sp)
    24ca:	c226                	sw	s1,4(sp)
    24cc:	00951413          	slli	s0,a0,0x9
    24d0:	c606                	sw	ra,12(sp)
    24d2:	0fe7f793          	andi	a5,a5,254
    24d6:	8025                	srli	s0,s0,0x9
    24d8:	01f55493          	srli	s1,a0,0x1f
    24dc:	c785                	beqz	a5,2504 <__extendsfdf2+0x4a>
    24de:	00345793          	srli	a5,s0,0x3
    24e2:	38070713          	addi	a4,a4,896
    24e6:	0476                	slli	s0,s0,0x1d
    24e8:	07b2                	slli	a5,a5,0xc
    24ea:	0752                	slli	a4,a4,0x14
    24ec:	83b1                	srli	a5,a5,0xc
    24ee:	01f49513          	slli	a0,s1,0x1f
    24f2:	8fd9                	or	a5,a5,a4
    24f4:	8fc9                	or	a5,a5,a0
    24f6:	40b2                	lw	ra,12(sp)
    24f8:	8522                	mv	a0,s0
    24fa:	4422                	lw	s0,8(sp)
    24fc:	4492                	lw	s1,4(sp)
    24fe:	85be                	mv	a1,a5
    2500:	0141                	addi	sp,sp,16
    2502:	8082                	ret
    2504:	eb05                	bnez	a4,2534 <__extendsfdf2+0x7a>
    2506:	c439                	beqz	s0,2554 <__extendsfdf2+0x9a>
    2508:	8522                	mv	a0,s0
    250a:	28b1                	jal	2566 <__clzsi2>
    250c:	47a9                	li	a5,10
    250e:	04a7c663          	blt	a5,a0,255a <__extendsfdf2+0xa0>
    2512:	472d                	li	a4,11
    2514:	8f09                	sub	a4,a4,a0
    2516:	01550793          	addi	a5,a0,21
    251a:	00e45733          	srl	a4,s0,a4
    251e:	00f41433          	sll	s0,s0,a5
    2522:	00c71793          	slli	a5,a4,0xc
    2526:	38900713          	li	a4,905
    252a:	8f09                	sub	a4,a4,a0
    252c:	83b1                	srli	a5,a5,0xc
    252e:	7ff77713          	andi	a4,a4,2047
    2532:	bf5d                	j	24e8 <__extendsfdf2+0x2e>
    2534:	cc01                	beqz	s0,254c <__extendsfdf2+0x92>
    2536:	00345713          	srli	a4,s0,0x3
    253a:	000807b7          	lui	a5,0x80
    253e:	8fd9                	or	a5,a5,a4
    2540:	07b2                	slli	a5,a5,0xc
    2542:	0476                	slli	s0,s0,0x1d
    2544:	83b1                	srli	a5,a5,0xc
    2546:	7ff00713          	li	a4,2047
    254a:	bf79                	j	24e8 <__extendsfdf2+0x2e>
    254c:	7ff00713          	li	a4,2047
    2550:	4781                	li	a5,0
    2552:	bf59                	j	24e8 <__extendsfdf2+0x2e>
    2554:	4701                	li	a4,0
    2556:	4781                	li	a5,0
    2558:	bf41                	j	24e8 <__extendsfdf2+0x2e>
    255a:	ff550713          	addi	a4,a0,-11
    255e:	00e41733          	sll	a4,s0,a4
    2562:	4401                	li	s0,0
    2564:	bf7d                	j	2522 <__extendsfdf2+0x68>

00002566 <__clzsi2>:
    2566:	67c1                	lui	a5,0x10
    2568:	02f57c63          	bgeu	a0,a5,25a0 <__clzsi2+0x3a>
    256c:	0ff00793          	li	a5,255
    2570:	02000713          	li	a4,32
    2574:	00a7eb63          	bltu	a5,a0,258a <__clzsi2+0x24>
    2578:	00001797          	auipc	a5,0x1
    257c:	46c78793          	addi	a5,a5,1132 # 39e4 <__clz_tab>
    2580:	97aa                	add	a5,a5,a0
    2582:	2388                	lbu	a0,0(a5)
    2584:	40a70533          	sub	a0,a4,a0
    2588:	8082                	ret
    258a:	8121                	srli	a0,a0,0x8
    258c:	00001797          	auipc	a5,0x1
    2590:	45878793          	addi	a5,a5,1112 # 39e4 <__clz_tab>
    2594:	97aa                	add	a5,a5,a0
    2596:	2388                	lbu	a0,0(a5)
    2598:	4761                	li	a4,24
    259a:	40a70533          	sub	a0,a4,a0
    259e:	8082                	ret
    25a0:	010007b7          	lui	a5,0x1000
    25a4:	00f56d63          	bltu	a0,a5,25be <__clzsi2+0x58>
    25a8:	8161                	srli	a0,a0,0x18
    25aa:	00001797          	auipc	a5,0x1
    25ae:	43a78793          	addi	a5,a5,1082 # 39e4 <__clz_tab>
    25b2:	97aa                	add	a5,a5,a0
    25b4:	2388                	lbu	a0,0(a5)
    25b6:	4721                	li	a4,8
    25b8:	40a70533          	sub	a0,a4,a0
    25bc:	8082                	ret
    25be:	8141                	srli	a0,a0,0x10
    25c0:	00001797          	auipc	a5,0x1
    25c4:	42478793          	addi	a5,a5,1060 # 39e4 <__clz_tab>
    25c8:	97aa                	add	a5,a5,a0
    25ca:	2388                	lbu	a0,0(a5)
    25cc:	4741                	li	a4,16
    25ce:	40a70533          	sub	a0,a4,a0
    25d2:	8082                	ret

000025d4 <iprintf>:
    25d4:	7139                	addi	sp,sp,-64
    25d6:	da3e                	sw	a5,52(sp)
    25d8:	d22e                	sw	a1,36(sp)
    25da:	d432                	sw	a2,40(sp)
    25dc:	d636                	sw	a3,44(sp)
    25de:	d83a                	sw	a4,48(sp)
    25e0:	dc42                	sw	a6,56(sp)
    25e2:	de46                	sw	a7,60(sp)
    25e4:	80c18793          	addi	a5,gp,-2036 # 200000bc <_impure_ptr>
    25e8:	cc22                	sw	s0,24(sp)
    25ea:	4380                	lw	s0,0(a5)
    25ec:	ca26                	sw	s1,20(sp)
    25ee:	ce06                	sw	ra,28(sp)
    25f0:	84aa                	mv	s1,a0
    25f2:	c409                	beqz	s0,25fc <iprintf+0x28>
    25f4:	4c1c                	lw	a5,24(s0)
    25f6:	e399                	bnez	a5,25fc <iprintf+0x28>
    25f8:	8522                	mv	a0,s0
    25fa:	29fd                	jal	2af8 <__sinit>
    25fc:	440c                	lw	a1,8(s0)
    25fe:	1054                	addi	a3,sp,36
    2600:	8626                	mv	a2,s1
    2602:	8522                	mv	a0,s0
    2604:	c636                	sw	a3,12(sp)
    2606:	139000ef          	jal	ra,2f3e <_vfiprintf_r>
    260a:	40f2                	lw	ra,28(sp)
    260c:	4462                	lw	s0,24(sp)
    260e:	44d2                	lw	s1,20(sp)
    2610:	6121                	addi	sp,sp,64
    2612:	8082                	ret

00002614 <_puts_r>:
    2614:	1101                	addi	sp,sp,-32
    2616:	ca26                	sw	s1,20(sp)
    2618:	c84a                	sw	s2,16(sp)
    261a:	ce06                	sw	ra,28(sp)
    261c:	cc22                	sw	s0,24(sp)
    261e:	c64e                	sw	s3,12(sp)
    2620:	c452                	sw	s4,8(sp)
    2622:	84aa                	mv	s1,a0
    2624:	892e                	mv	s2,a1
    2626:	c501                	beqz	a0,262e <_puts_r+0x1a>
    2628:	4d1c                	lw	a5,24(a0)
    262a:	e391                	bnez	a5,262e <_puts_r+0x1a>
    262c:	21f1                	jal	2af8 <__sinit>
    262e:	4c9c                	lw	a5,24(s1)
    2630:	4480                	lw	s0,8(s1)
    2632:	e399                	bnez	a5,2638 <_puts_r+0x24>
    2634:	8526                	mv	a0,s1
    2636:	21c9                	jal	2af8 <__sinit>
    2638:	00001797          	auipc	a5,0x1
    263c:	4cc78793          	addi	a5,a5,1228 # 3b04 <__sf_fake_stdin>
    2640:	02f41b63          	bne	s0,a5,2676 <_puts_r+0x62>
    2644:	40c0                	lw	s0,4(s1)
    2646:	245e                	lhu	a5,12(s0)
    2648:	8ba1                	andi	a5,a5,8
    264a:	c7b1                	beqz	a5,2696 <_puts_r+0x82>
    264c:	481c                	lw	a5,16(s0)
    264e:	c7a1                	beqz	a5,2696 <_puts_r+0x82>
    2650:	59fd                	li	s3,-1
    2652:	4a29                	li	s4,10
    2654:	441c                	lw	a5,8(s0)
    2656:	00094583          	lbu	a1,0(s2)
    265a:	17fd                	addi	a5,a5,-1
    265c:	e9b1                	bnez	a1,26b0 <_puts_r+0x9c>
    265e:	c41c                	sw	a5,8(s0)
    2660:	0607dd63          	bgez	a5,26da <_puts_r+0xc6>
    2664:	8622                	mv	a2,s0
    2666:	45a9                	li	a1,10
    2668:	8526                	mv	a0,s1
    266a:	2069                	jal	26f4 <__swbuf_r>
    266c:	57fd                	li	a5,-1
    266e:	02f50863          	beq	a0,a5,269e <_puts_r+0x8a>
    2672:	4529                	li	a0,10
    2674:	a035                	j	26a0 <_puts_r+0x8c>
    2676:	00001797          	auipc	a5,0x1
    267a:	4ae78793          	addi	a5,a5,1198 # 3b24 <__sf_fake_stdout>
    267e:	00f41463          	bne	s0,a5,2686 <_puts_r+0x72>
    2682:	4480                	lw	s0,8(s1)
    2684:	b7c9                	j	2646 <_puts_r+0x32>
    2686:	00001797          	auipc	a5,0x1
    268a:	45e78793          	addi	a5,a5,1118 # 3ae4 <__sf_fake_stderr>
    268e:	faf41ce3          	bne	s0,a5,2646 <_puts_r+0x32>
    2692:	44c0                	lw	s0,12(s1)
    2694:	bf4d                	j	2646 <_puts_r+0x32>
    2696:	85a2                	mv	a1,s0
    2698:	8526                	mv	a0,s1
    269a:	2a19                	jal	27b0 <__swsetup_r>
    269c:	d955                	beqz	a0,2650 <_puts_r+0x3c>
    269e:	557d                	li	a0,-1
    26a0:	40f2                	lw	ra,28(sp)
    26a2:	4462                	lw	s0,24(sp)
    26a4:	44d2                	lw	s1,20(sp)
    26a6:	4942                	lw	s2,16(sp)
    26a8:	49b2                	lw	s3,12(sp)
    26aa:	4a22                	lw	s4,8(sp)
    26ac:	6105                	addi	sp,sp,32
    26ae:	8082                	ret
    26b0:	c41c                	sw	a5,8(s0)
    26b2:	0905                	addi	s2,s2,1
    26b4:	0007d763          	bgez	a5,26c2 <_puts_r+0xae>
    26b8:	4c18                	lw	a4,24(s0)
    26ba:	00e7ca63          	blt	a5,a4,26ce <_puts_r+0xba>
    26be:	01458863          	beq	a1,s4,26ce <_puts_r+0xba>
    26c2:	401c                	lw	a5,0(s0)
    26c4:	00178713          	addi	a4,a5,1
    26c8:	c018                	sw	a4,0(s0)
    26ca:	a38c                	sb	a1,0(a5)
    26cc:	b761                	j	2654 <_puts_r+0x40>
    26ce:	8622                	mv	a2,s0
    26d0:	8526                	mv	a0,s1
    26d2:	200d                	jal	26f4 <__swbuf_r>
    26d4:	f93510e3          	bne	a0,s3,2654 <_puts_r+0x40>
    26d8:	b7d9                	j	269e <_puts_r+0x8a>
    26da:	401c                	lw	a5,0(s0)
    26dc:	00178713          	addi	a4,a5,1
    26e0:	c018                	sw	a4,0(s0)
    26e2:	4729                	li	a4,10
    26e4:	a398                	sb	a4,0(a5)
    26e6:	b771                	j	2672 <_puts_r+0x5e>

000026e8 <puts>:
    26e8:	80c18793          	addi	a5,gp,-2036 # 200000bc <_impure_ptr>
    26ec:	85aa                	mv	a1,a0
    26ee:	4388                	lw	a0,0(a5)
    26f0:	f25ff06f          	j	2614 <_puts_r>

000026f4 <__swbuf_r>:
    26f4:	1101                	addi	sp,sp,-32
    26f6:	cc22                	sw	s0,24(sp)
    26f8:	ca26                	sw	s1,20(sp)
    26fa:	c84a                	sw	s2,16(sp)
    26fc:	ce06                	sw	ra,28(sp)
    26fe:	c64e                	sw	s3,12(sp)
    2700:	84aa                	mv	s1,a0
    2702:	892e                	mv	s2,a1
    2704:	8432                	mv	s0,a2
    2706:	c501                	beqz	a0,270e <__swbuf_r+0x1a>
    2708:	4d1c                	lw	a5,24(a0)
    270a:	e391                	bnez	a5,270e <__swbuf_r+0x1a>
    270c:	26f5                	jal	2af8 <__sinit>
    270e:	00001797          	auipc	a5,0x1
    2712:	3f678793          	addi	a5,a5,1014 # 3b04 <__sf_fake_stdin>
    2716:	06f41763          	bne	s0,a5,2784 <__swbuf_r+0x90>
    271a:	40c0                	lw	s0,4(s1)
    271c:	4c1c                	lw	a5,24(s0)
    271e:	c41c                	sw	a5,8(s0)
    2720:	245e                	lhu	a5,12(s0)
    2722:	8ba1                	andi	a5,a5,8
    2724:	c3c1                	beqz	a5,27a4 <__swbuf_r+0xb0>
    2726:	481c                	lw	a5,16(s0)
    2728:	cfb5                	beqz	a5,27a4 <__swbuf_r+0xb0>
    272a:	481c                	lw	a5,16(s0)
    272c:	4008                	lw	a0,0(s0)
    272e:	0ff97993          	andi	s3,s2,255
    2732:	0ff97913          	andi	s2,s2,255
    2736:	8d1d                	sub	a0,a0,a5
    2738:	485c                	lw	a5,20(s0)
    273a:	00f54663          	blt	a0,a5,2746 <__swbuf_r+0x52>
    273e:	85a2                	mv	a1,s0
    2740:	8526                	mv	a0,s1
    2742:	2c69                	jal	29dc <_fflush_r>
    2744:	e525                	bnez	a0,27ac <__swbuf_r+0xb8>
    2746:	441c                	lw	a5,8(s0)
    2748:	0505                	addi	a0,a0,1
    274a:	17fd                	addi	a5,a5,-1
    274c:	c41c                	sw	a5,8(s0)
    274e:	401c                	lw	a5,0(s0)
    2750:	00178713          	addi	a4,a5,1
    2754:	c018                	sw	a4,0(s0)
    2756:	01378023          	sb	s3,0(a5)
    275a:	485c                	lw	a5,20(s0)
    275c:	00a78863          	beq	a5,a0,276c <__swbuf_r+0x78>
    2760:	245e                	lhu	a5,12(s0)
    2762:	8b85                	andi	a5,a5,1
    2764:	cb81                	beqz	a5,2774 <__swbuf_r+0x80>
    2766:	47a9                	li	a5,10
    2768:	00f91663          	bne	s2,a5,2774 <__swbuf_r+0x80>
    276c:	85a2                	mv	a1,s0
    276e:	8526                	mv	a0,s1
    2770:	24b5                	jal	29dc <_fflush_r>
    2772:	ed0d                	bnez	a0,27ac <__swbuf_r+0xb8>
    2774:	40f2                	lw	ra,28(sp)
    2776:	4462                	lw	s0,24(sp)
    2778:	854a                	mv	a0,s2
    277a:	44d2                	lw	s1,20(sp)
    277c:	4942                	lw	s2,16(sp)
    277e:	49b2                	lw	s3,12(sp)
    2780:	6105                	addi	sp,sp,32
    2782:	8082                	ret
    2784:	00001797          	auipc	a5,0x1
    2788:	3a078793          	addi	a5,a5,928 # 3b24 <__sf_fake_stdout>
    278c:	00f41463          	bne	s0,a5,2794 <__swbuf_r+0xa0>
    2790:	4480                	lw	s0,8(s1)
    2792:	b769                	j	271c <__swbuf_r+0x28>
    2794:	00001797          	auipc	a5,0x1
    2798:	35078793          	addi	a5,a5,848 # 3ae4 <__sf_fake_stderr>
    279c:	f8f410e3          	bne	s0,a5,271c <__swbuf_r+0x28>
    27a0:	44c0                	lw	s0,12(s1)
    27a2:	bfad                	j	271c <__swbuf_r+0x28>
    27a4:	85a2                	mv	a1,s0
    27a6:	8526                	mv	a0,s1
    27a8:	2021                	jal	27b0 <__swsetup_r>
    27aa:	d141                	beqz	a0,272a <__swbuf_r+0x36>
    27ac:	597d                	li	s2,-1
    27ae:	b7d9                	j	2774 <__swbuf_r+0x80>

000027b0 <__swsetup_r>:
    27b0:	1141                	addi	sp,sp,-16
    27b2:	80c18793          	addi	a5,gp,-2036 # 200000bc <_impure_ptr>
    27b6:	c226                	sw	s1,4(sp)
    27b8:	4384                	lw	s1,0(a5)
    27ba:	c422                	sw	s0,8(sp)
    27bc:	c04a                	sw	s2,0(sp)
    27be:	c606                	sw	ra,12(sp)
    27c0:	892a                	mv	s2,a0
    27c2:	842e                	mv	s0,a1
    27c4:	c489                	beqz	s1,27ce <__swsetup_r+0x1e>
    27c6:	4c9c                	lw	a5,24(s1)
    27c8:	e399                	bnez	a5,27ce <__swsetup_r+0x1e>
    27ca:	8526                	mv	a0,s1
    27cc:	2635                	jal	2af8 <__sinit>
    27ce:	00001797          	auipc	a5,0x1
    27d2:	33678793          	addi	a5,a5,822 # 3b04 <__sf_fake_stdin>
    27d6:	02f41b63          	bne	s0,a5,280c <__swsetup_r+0x5c>
    27da:	40c0                	lw	s0,4(s1)
    27dc:	00c41703          	lh	a4,12(s0) # 400000c <_data_lma+0x3ffc48c>
    27e0:	01071793          	slli	a5,a4,0x10
    27e4:	83c1                	srli	a5,a5,0x10
    27e6:	0087f693          	andi	a3,a5,8
    27ea:	eaad                	bnez	a3,285c <__swsetup_r+0xac>
    27ec:	0107f693          	andi	a3,a5,16
    27f0:	ee95                	bnez	a3,282c <__swsetup_r+0x7c>
    27f2:	47a5                	li	a5,9
    27f4:	00f92023          	sw	a5,0(s2)
    27f8:	04076713          	ori	a4,a4,64
    27fc:	a45a                	sh	a4,12(s0)
    27fe:	557d                	li	a0,-1
    2800:	40b2                	lw	ra,12(sp)
    2802:	4422                	lw	s0,8(sp)
    2804:	4492                	lw	s1,4(sp)
    2806:	4902                	lw	s2,0(sp)
    2808:	0141                	addi	sp,sp,16
    280a:	8082                	ret
    280c:	00001797          	auipc	a5,0x1
    2810:	31878793          	addi	a5,a5,792 # 3b24 <__sf_fake_stdout>
    2814:	00f41463          	bne	s0,a5,281c <__swsetup_r+0x6c>
    2818:	4480                	lw	s0,8(s1)
    281a:	b7c9                	j	27dc <__swsetup_r+0x2c>
    281c:	00001797          	auipc	a5,0x1
    2820:	2c878793          	addi	a5,a5,712 # 3ae4 <__sf_fake_stderr>
    2824:	faf41ce3          	bne	s0,a5,27dc <__swsetup_r+0x2c>
    2828:	44c0                	lw	s0,12(s1)
    282a:	bf4d                	j	27dc <__swsetup_r+0x2c>
    282c:	8b91                	andi	a5,a5,4
    282e:	c39d                	beqz	a5,2854 <__swsetup_r+0xa4>
    2830:	584c                	lw	a1,52(s0)
    2832:	c989                	beqz	a1,2844 <__swsetup_r+0x94>
    2834:	04440793          	addi	a5,s0,68
    2838:	00f58463          	beq	a1,a5,2840 <__swsetup_r+0x90>
    283c:	854a                	mv	a0,s2
    283e:	2b29                	jal	2d58 <_free_r>
    2840:	02042a23          	sw	zero,52(s0)
    2844:	245e                	lhu	a5,12(s0)
    2846:	00042223          	sw	zero,4(s0)
    284a:	fdb7f793          	andi	a5,a5,-37
    284e:	a45e                	sh	a5,12(s0)
    2850:	481c                	lw	a5,16(s0)
    2852:	c01c                	sw	a5,0(s0)
    2854:	245e                	lhu	a5,12(s0)
    2856:	0087e793          	ori	a5,a5,8
    285a:	a45e                	sh	a5,12(s0)
    285c:	481c                	lw	a5,16(s0)
    285e:	eb99                	bnez	a5,2874 <__swsetup_r+0xc4>
    2860:	245e                	lhu	a5,12(s0)
    2862:	20000713          	li	a4,512
    2866:	2807f793          	andi	a5,a5,640
    286a:	00e78563          	beq	a5,a4,2874 <__swsetup_r+0xc4>
    286e:	85a2                	mv	a1,s0
    2870:	854a                	mv	a0,s2
    2872:	2991                	jal	2cc6 <__smakebuf_r>
    2874:	245e                	lhu	a5,12(s0)
    2876:	0017f713          	andi	a4,a5,1
    287a:	c31d                	beqz	a4,28a0 <__swsetup_r+0xf0>
    287c:	485c                	lw	a5,20(s0)
    287e:	00042423          	sw	zero,8(s0)
    2882:	40f007b3          	neg	a5,a5
    2886:	cc1c                	sw	a5,24(s0)
    2888:	481c                	lw	a5,16(s0)
    288a:	4501                	li	a0,0
    288c:	fbb5                	bnez	a5,2800 <__swsetup_r+0x50>
    288e:	00c41783          	lh	a5,12(s0)
    2892:	0807f713          	andi	a4,a5,128
    2896:	d72d                	beqz	a4,2800 <__swsetup_r+0x50>
    2898:	0407e793          	ori	a5,a5,64
    289c:	a45e                	sh	a5,12(s0)
    289e:	b785                	j	27fe <__swsetup_r+0x4e>
    28a0:	8b89                	andi	a5,a5,2
    28a2:	4701                	li	a4,0
    28a4:	e391                	bnez	a5,28a8 <__swsetup_r+0xf8>
    28a6:	4858                	lw	a4,20(s0)
    28a8:	c418                	sw	a4,8(s0)
    28aa:	bff9                	j	2888 <__swsetup_r+0xd8>

000028ac <__sflush_r>:
    28ac:	25de                	lhu	a5,12(a1)
    28ae:	1101                	addi	sp,sp,-32
    28b0:	cc22                	sw	s0,24(sp)
    28b2:	ca26                	sw	s1,20(sp)
    28b4:	ce06                	sw	ra,28(sp)
    28b6:	c84a                	sw	s2,16(sp)
    28b8:	c64e                	sw	s3,12(sp)
    28ba:	0087f713          	andi	a4,a5,8
    28be:	84aa                	mv	s1,a0
    28c0:	842e                	mv	s0,a1
    28c2:	eb79                	bnez	a4,2998 <__sflush_r+0xec>
    28c4:	41d8                	lw	a4,4(a1)
    28c6:	00e04d63          	bgtz	a4,28e0 <__sflush_r+0x34>
    28ca:	41b8                	lw	a4,64(a1)
    28cc:	00e04a63          	bgtz	a4,28e0 <__sflush_r+0x34>
    28d0:	4501                	li	a0,0
    28d2:	40f2                	lw	ra,28(sp)
    28d4:	4462                	lw	s0,24(sp)
    28d6:	44d2                	lw	s1,20(sp)
    28d8:	4942                	lw	s2,16(sp)
    28da:	49b2                	lw	s3,12(sp)
    28dc:	6105                	addi	sp,sp,32
    28de:	8082                	ret
    28e0:	5458                	lw	a4,44(s0)
    28e2:	d77d                	beqz	a4,28d0 <__sflush_r+0x24>
    28e4:	0004a903          	lw	s2,0(s1)
    28e8:	01379693          	slli	a3,a5,0x13
    28ec:	0004a023          	sw	zero,0(s1)
    28f0:	0606db63          	bgez	a3,2966 <__sflush_r+0xba>
    28f4:	4870                	lw	a2,84(s0)
    28f6:	245e                	lhu	a5,12(s0)
    28f8:	8b91                	andi	a5,a5,4
    28fa:	c799                	beqz	a5,2908 <__sflush_r+0x5c>
    28fc:	405c                	lw	a5,4(s0)
    28fe:	8e1d                	sub	a2,a2,a5
    2900:	585c                	lw	a5,52(s0)
    2902:	c399                	beqz	a5,2908 <__sflush_r+0x5c>
    2904:	403c                	lw	a5,64(s0)
    2906:	8e1d                	sub	a2,a2,a5
    2908:	545c                	lw	a5,44(s0)
    290a:	500c                	lw	a1,32(s0)
    290c:	4681                	li	a3,0
    290e:	8526                	mv	a0,s1
    2910:	9782                	jalr	a5
    2912:	57fd                	li	a5,-1
    2914:	245a                	lhu	a4,12(s0)
    2916:	00f51d63          	bne	a0,a5,2930 <__sflush_r+0x84>
    291a:	4094                	lw	a3,0(s1)
    291c:	47f5                	li	a5,29
    291e:	06d7e863          	bltu	a5,a3,298e <__sflush_r+0xe2>
    2922:	204007b7          	lui	a5,0x20400
    2926:	0785                	addi	a5,a5,1
    2928:	00d7d7b3          	srl	a5,a5,a3
    292c:	8b85                	andi	a5,a5,1
    292e:	c3a5                	beqz	a5,298e <__sflush_r+0xe2>
    2930:	481c                	lw	a5,16(s0)
    2932:	00042223          	sw	zero,4(s0)
    2936:	c01c                	sw	a5,0(s0)
    2938:	01371793          	slli	a5,a4,0x13
    293c:	0007d863          	bgez	a5,294c <__sflush_r+0xa0>
    2940:	57fd                	li	a5,-1
    2942:	00f51463          	bne	a0,a5,294a <__sflush_r+0x9e>
    2946:	409c                	lw	a5,0(s1)
    2948:	e391                	bnez	a5,294c <__sflush_r+0xa0>
    294a:	c868                	sw	a0,84(s0)
    294c:	584c                	lw	a1,52(s0)
    294e:	0124a023          	sw	s2,0(s1)
    2952:	ddbd                	beqz	a1,28d0 <__sflush_r+0x24>
    2954:	04440793          	addi	a5,s0,68
    2958:	00f58463          	beq	a1,a5,2960 <__sflush_r+0xb4>
    295c:	8526                	mv	a0,s1
    295e:	2eed                	jal	2d58 <_free_r>
    2960:	02042a23          	sw	zero,52(s0)
    2964:	b7b5                	j	28d0 <__sflush_r+0x24>
    2966:	500c                	lw	a1,32(s0)
    2968:	4601                	li	a2,0
    296a:	4685                	li	a3,1
    296c:	8526                	mv	a0,s1
    296e:	9702                	jalr	a4
    2970:	57fd                	li	a5,-1
    2972:	862a                	mv	a2,a0
    2974:	f8f511e3          	bne	a0,a5,28f6 <__sflush_r+0x4a>
    2978:	409c                	lw	a5,0(s1)
    297a:	dfb5                	beqz	a5,28f6 <__sflush_r+0x4a>
    297c:	4775                	li	a4,29
    297e:	00e78563          	beq	a5,a4,2988 <__sflush_r+0xdc>
    2982:	4759                	li	a4,22
    2984:	04e79363          	bne	a5,a4,29ca <__sflush_r+0x11e>
    2988:	0124a023          	sw	s2,0(s1)
    298c:	b791                	j	28d0 <__sflush_r+0x24>
    298e:	04076713          	ori	a4,a4,64
    2992:	a45a                	sh	a4,12(s0)
    2994:	557d                	li	a0,-1
    2996:	bf35                	j	28d2 <__sflush_r+0x26>
    2998:	0105a983          	lw	s3,16(a1)
    299c:	f2098ae3          	beqz	s3,28d0 <__sflush_r+0x24>
    29a0:	0005a903          	lw	s2,0(a1)
    29a4:	8b8d                	andi	a5,a5,3
    29a6:	0135a023          	sw	s3,0(a1)
    29aa:	41390933          	sub	s2,s2,s3
    29ae:	4701                	li	a4,0
    29b0:	e391                	bnez	a5,29b4 <__sflush_r+0x108>
    29b2:	49d8                	lw	a4,20(a1)
    29b4:	c418                	sw	a4,8(s0)
    29b6:	f1205de3          	blez	s2,28d0 <__sflush_r+0x24>
    29ba:	541c                	lw	a5,40(s0)
    29bc:	500c                	lw	a1,32(s0)
    29be:	86ca                	mv	a3,s2
    29c0:	864e                	mv	a2,s3
    29c2:	8526                	mv	a0,s1
    29c4:	9782                	jalr	a5
    29c6:	00a04763          	bgtz	a0,29d4 <__sflush_r+0x128>
    29ca:	245e                	lhu	a5,12(s0)
    29cc:	0407e793          	ori	a5,a5,64
    29d0:	a45e                	sh	a5,12(s0)
    29d2:	b7c9                	j	2994 <__sflush_r+0xe8>
    29d4:	99aa                	add	s3,s3,a0
    29d6:	40a90933          	sub	s2,s2,a0
    29da:	bff1                	j	29b6 <__sflush_r+0x10a>

000029dc <_fflush_r>:
    29dc:	499c                	lw	a5,16(a1)
    29de:	c3a5                	beqz	a5,2a3e <_fflush_r+0x62>
    29e0:	1101                	addi	sp,sp,-32
    29e2:	cc22                	sw	s0,24(sp)
    29e4:	ce06                	sw	ra,28(sp)
    29e6:	842a                	mv	s0,a0
    29e8:	c511                	beqz	a0,29f4 <_fflush_r+0x18>
    29ea:	4d1c                	lw	a5,24(a0)
    29ec:	e781                	bnez	a5,29f4 <_fflush_r+0x18>
    29ee:	c62e                	sw	a1,12(sp)
    29f0:	2221                	jal	2af8 <__sinit>
    29f2:	45b2                	lw	a1,12(sp)
    29f4:	00001797          	auipc	a5,0x1
    29f8:	11078793          	addi	a5,a5,272 # 3b04 <__sf_fake_stdin>
    29fc:	00f59c63          	bne	a1,a5,2a14 <_fflush_r+0x38>
    2a00:	404c                	lw	a1,4(s0)
    2a02:	00c59783          	lh	a5,12(a1)
    2a06:	c79d                	beqz	a5,2a34 <_fflush_r+0x58>
    2a08:	8522                	mv	a0,s0
    2a0a:	4462                	lw	s0,24(sp)
    2a0c:	40f2                	lw	ra,28(sp)
    2a0e:	6105                	addi	sp,sp,32
    2a10:	e9dff06f          	j	28ac <__sflush_r>
    2a14:	00001797          	auipc	a5,0x1
    2a18:	11078793          	addi	a5,a5,272 # 3b24 <__sf_fake_stdout>
    2a1c:	00f59463          	bne	a1,a5,2a24 <_fflush_r+0x48>
    2a20:	440c                	lw	a1,8(s0)
    2a22:	b7c5                	j	2a02 <_fflush_r+0x26>
    2a24:	00001797          	auipc	a5,0x1
    2a28:	0c078793          	addi	a5,a5,192 # 3ae4 <__sf_fake_stderr>
    2a2c:	fcf59be3          	bne	a1,a5,2a02 <_fflush_r+0x26>
    2a30:	444c                	lw	a1,12(s0)
    2a32:	bfc1                	j	2a02 <_fflush_r+0x26>
    2a34:	40f2                	lw	ra,28(sp)
    2a36:	4462                	lw	s0,24(sp)
    2a38:	4501                	li	a0,0
    2a3a:	6105                	addi	sp,sp,32
    2a3c:	8082                	ret
    2a3e:	4501                	li	a0,0
    2a40:	8082                	ret

00002a42 <std>:
    2a42:	1141                	addi	sp,sp,-16
    2a44:	c422                	sw	s0,8(sp)
    2a46:	c606                	sw	ra,12(sp)
    2a48:	842a                	mv	s0,a0
    2a4a:	a54e                	sh	a1,12(a0)
    2a4c:	a572                	sh	a2,14(a0)
    2a4e:	00052023          	sw	zero,0(a0)
    2a52:	00052223          	sw	zero,4(a0)
    2a56:	00052423          	sw	zero,8(a0)
    2a5a:	06052223          	sw	zero,100(a0)
    2a5e:	00052823          	sw	zero,16(a0)
    2a62:	00052a23          	sw	zero,20(a0)
    2a66:	00052c23          	sw	zero,24(a0)
    2a6a:	4621                	li	a2,8
    2a6c:	4581                	li	a1,0
    2a6e:	05c50513          	addi	a0,a0,92
    2a72:	faefd0ef          	jal	ra,220 <memset>
    2a76:	00001797          	auipc	a5,0x1
    2a7a:	b2878793          	addi	a5,a5,-1240 # 359e <__sread>
    2a7e:	d05c                	sw	a5,36(s0)
    2a80:	00001797          	auipc	a5,0x1
    2a84:	b4a78793          	addi	a5,a5,-1206 # 35ca <__swrite>
    2a88:	d41c                	sw	a5,40(s0)
    2a8a:	00001797          	auipc	a5,0x1
    2a8e:	b8878793          	addi	a5,a5,-1144 # 3612 <__sseek>
    2a92:	d45c                	sw	a5,44(s0)
    2a94:	00001797          	auipc	a5,0x1
    2a98:	bae78793          	addi	a5,a5,-1106 # 3642 <__sclose>
    2a9c:	d000                	sw	s0,32(s0)
    2a9e:	d81c                	sw	a5,48(s0)
    2aa0:	40b2                	lw	ra,12(sp)
    2aa2:	4422                	lw	s0,8(sp)
    2aa4:	0141                	addi	sp,sp,16
    2aa6:	8082                	ret

00002aa8 <_cleanup_r>:
    2aa8:	00000597          	auipc	a1,0x0
    2aac:	f3458593          	addi	a1,a1,-204 # 29dc <_fflush_r>
    2ab0:	aa91                	j	2c04 <_fwalk_reent>

00002ab2 <__sfmoreglue>:
    2ab2:	1141                	addi	sp,sp,-16
    2ab4:	c226                	sw	s1,4(sp)
    2ab6:	06800613          	li	a2,104
    2aba:	fff58493          	addi	s1,a1,-1
    2abe:	02c484b3          	mul	s1,s1,a2
    2ac2:	c04a                	sw	s2,0(sp)
    2ac4:	892e                	mv	s2,a1
    2ac6:	c422                	sw	s0,8(sp)
    2ac8:	c606                	sw	ra,12(sp)
    2aca:	07448593          	addi	a1,s1,116
    2ace:	2e0d                	jal	2e00 <_malloc_r>
    2ad0:	842a                	mv	s0,a0
    2ad2:	cd01                	beqz	a0,2aea <__sfmoreglue+0x38>
    2ad4:	00052023          	sw	zero,0(a0)
    2ad8:	01252223          	sw	s2,4(a0)
    2adc:	0531                	addi	a0,a0,12
    2ade:	c408                	sw	a0,8(s0)
    2ae0:	06848613          	addi	a2,s1,104
    2ae4:	4581                	li	a1,0
    2ae6:	f3afd0ef          	jal	ra,220 <memset>
    2aea:	8522                	mv	a0,s0
    2aec:	40b2                	lw	ra,12(sp)
    2aee:	4422                	lw	s0,8(sp)
    2af0:	4492                	lw	s1,4(sp)
    2af2:	4902                	lw	s2,0(sp)
    2af4:	0141                	addi	sp,sp,16
    2af6:	8082                	ret

00002af8 <__sinit>:
    2af8:	4d1c                	lw	a5,24(a0)
    2afa:	e7a5                	bnez	a5,2b62 <__sinit+0x6a>
    2afc:	1141                	addi	sp,sp,-16
    2afe:	c606                	sw	ra,12(sp)
    2b00:	c422                	sw	s0,8(sp)
    2b02:	00000797          	auipc	a5,0x0
    2b06:	fa678793          	addi	a5,a5,-90 # 2aa8 <_cleanup_r>
    2b0a:	d51c                	sw	a5,40(a0)
    2b0c:	81018793          	addi	a5,gp,-2032 # 200000c0 <_global_impure_ptr>
    2b10:	439c                	lw	a5,0(a5)
    2b12:	04052423          	sw	zero,72(a0)
    2b16:	04052623          	sw	zero,76(a0)
    2b1a:	04052823          	sw	zero,80(a0)
    2b1e:	00f51463          	bne	a0,a5,2b26 <__sinit+0x2e>
    2b22:	4785                	li	a5,1
    2b24:	cd1c                	sw	a5,24(a0)
    2b26:	842a                	mv	s0,a0
    2b28:	2835                	jal	2b64 <__sfp>
    2b2a:	c048                	sw	a0,4(s0)
    2b2c:	8522                	mv	a0,s0
    2b2e:	281d                	jal	2b64 <__sfp>
    2b30:	c408                	sw	a0,8(s0)
    2b32:	8522                	mv	a0,s0
    2b34:	2805                	jal	2b64 <__sfp>
    2b36:	c448                	sw	a0,12(s0)
    2b38:	4048                	lw	a0,4(s0)
    2b3a:	4601                	li	a2,0
    2b3c:	4591                	li	a1,4
    2b3e:	f05ff0ef          	jal	ra,2a42 <std>
    2b42:	4408                	lw	a0,8(s0)
    2b44:	4605                	li	a2,1
    2b46:	45a5                	li	a1,9
    2b48:	efbff0ef          	jal	ra,2a42 <std>
    2b4c:	4448                	lw	a0,12(s0)
    2b4e:	4609                	li	a2,2
    2b50:	45c9                	li	a1,18
    2b52:	ef1ff0ef          	jal	ra,2a42 <std>
    2b56:	4785                	li	a5,1
    2b58:	cc1c                	sw	a5,24(s0)
    2b5a:	40b2                	lw	ra,12(sp)
    2b5c:	4422                	lw	s0,8(sp)
    2b5e:	0141                	addi	sp,sp,16
    2b60:	8082                	ret
    2b62:	8082                	ret

00002b64 <__sfp>:
    2b64:	1141                	addi	sp,sp,-16
    2b66:	81018793          	addi	a5,gp,-2032 # 200000c0 <_global_impure_ptr>
    2b6a:	c226                	sw	s1,4(sp)
    2b6c:	4384                	lw	s1,0(a5)
    2b6e:	c04a                	sw	s2,0(sp)
    2b70:	c606                	sw	ra,12(sp)
    2b72:	4c9c                	lw	a5,24(s1)
    2b74:	c422                	sw	s0,8(sp)
    2b76:	892a                	mv	s2,a0
    2b78:	e781                	bnez	a5,2b80 <__sfp+0x1c>
    2b7a:	8526                	mv	a0,s1
    2b7c:	f7dff0ef          	jal	ra,2af8 <__sinit>
    2b80:	04848493          	addi	s1,s1,72
    2b84:	4480                	lw	s0,8(s1)
    2b86:	40dc                	lw	a5,4(s1)
    2b88:	17fd                	addi	a5,a5,-1
    2b8a:	0007d663          	bgez	a5,2b96 <__sfp+0x32>
    2b8e:	409c                	lw	a5,0(s1)
    2b90:	cfb9                	beqz	a5,2bee <__sfp+0x8a>
    2b92:	4084                	lw	s1,0(s1)
    2b94:	bfc5                	j	2b84 <__sfp+0x20>
    2b96:	00c41703          	lh	a4,12(s0)
    2b9a:	e739                	bnez	a4,2be8 <__sfp+0x84>
    2b9c:	77c1                	lui	a5,0xffff0
    2b9e:	0785                	addi	a5,a5,1
    2ba0:	06042223          	sw	zero,100(s0)
    2ba4:	00042023          	sw	zero,0(s0)
    2ba8:	00042223          	sw	zero,4(s0)
    2bac:	00042423          	sw	zero,8(s0)
    2bb0:	c45c                	sw	a5,12(s0)
    2bb2:	00042823          	sw	zero,16(s0)
    2bb6:	00042a23          	sw	zero,20(s0)
    2bba:	00042c23          	sw	zero,24(s0)
    2bbe:	4621                	li	a2,8
    2bc0:	4581                	li	a1,0
    2bc2:	05c40513          	addi	a0,s0,92
    2bc6:	e5afd0ef          	jal	ra,220 <memset>
    2bca:	02042a23          	sw	zero,52(s0)
    2bce:	02042c23          	sw	zero,56(s0)
    2bd2:	04042423          	sw	zero,72(s0)
    2bd6:	04042623          	sw	zero,76(s0)
    2bda:	8522                	mv	a0,s0
    2bdc:	40b2                	lw	ra,12(sp)
    2bde:	4422                	lw	s0,8(sp)
    2be0:	4492                	lw	s1,4(sp)
    2be2:	4902                	lw	s2,0(sp)
    2be4:	0141                	addi	sp,sp,16
    2be6:	8082                	ret
    2be8:	06840413          	addi	s0,s0,104
    2bec:	bf71                	j	2b88 <__sfp+0x24>
    2bee:	4591                	li	a1,4
    2bf0:	854a                	mv	a0,s2
    2bf2:	ec1ff0ef          	jal	ra,2ab2 <__sfmoreglue>
    2bf6:	c088                	sw	a0,0(s1)
    2bf8:	fd49                	bnez	a0,2b92 <__sfp+0x2e>
    2bfa:	47b1                	li	a5,12
    2bfc:	00f92023          	sw	a5,0(s2)
    2c00:	4401                	li	s0,0
    2c02:	bfe1                	j	2bda <__sfp+0x76>

00002c04 <_fwalk_reent>:
    2c04:	7179                	addi	sp,sp,-48
    2c06:	d422                	sw	s0,40(sp)
    2c08:	d04a                	sw	s2,32(sp)
    2c0a:	cc52                	sw	s4,24(sp)
    2c0c:	ca56                	sw	s5,20(sp)
    2c0e:	c85a                	sw	s6,16(sp)
    2c10:	c65e                	sw	s7,12(sp)
    2c12:	d606                	sw	ra,44(sp)
    2c14:	d226                	sw	s1,36(sp)
    2c16:	ce4e                	sw	s3,28(sp)
    2c18:	8a2a                	mv	s4,a0
    2c1a:	8aae                	mv	s5,a1
    2c1c:	04850413          	addi	s0,a0,72
    2c20:	4901                	li	s2,0
    2c22:	4b05                	li	s6,1
    2c24:	5bfd                	li	s7,-1
    2c26:	ec09                	bnez	s0,2c40 <_fwalk_reent+0x3c>
    2c28:	50b2                	lw	ra,44(sp)
    2c2a:	5422                	lw	s0,40(sp)
    2c2c:	854a                	mv	a0,s2
    2c2e:	5492                	lw	s1,36(sp)
    2c30:	5902                	lw	s2,32(sp)
    2c32:	49f2                	lw	s3,28(sp)
    2c34:	4a62                	lw	s4,24(sp)
    2c36:	4ad2                	lw	s5,20(sp)
    2c38:	4b42                	lw	s6,16(sp)
    2c3a:	4bb2                	lw	s7,12(sp)
    2c3c:	6145                	addi	sp,sp,48
    2c3e:	8082                	ret
    2c40:	4404                	lw	s1,8(s0)
    2c42:	00442983          	lw	s3,4(s0)
    2c46:	19fd                	addi	s3,s3,-1
    2c48:	0009d463          	bgez	s3,2c50 <_fwalk_reent+0x4c>
    2c4c:	4000                	lw	s0,0(s0)
    2c4e:	bfe1                	j	2c26 <_fwalk_reent+0x22>
    2c50:	24de                	lhu	a5,12(s1)
    2c52:	00fb7b63          	bgeu	s6,a5,2c68 <_fwalk_reent+0x64>
    2c56:	00e49783          	lh	a5,14(s1)
    2c5a:	01778763          	beq	a5,s7,2c68 <_fwalk_reent+0x64>
    2c5e:	85a6                	mv	a1,s1
    2c60:	8552                	mv	a0,s4
    2c62:	9a82                	jalr	s5
    2c64:	00a96933          	or	s2,s2,a0
    2c68:	06848493          	addi	s1,s1,104
    2c6c:	bfe9                	j	2c46 <_fwalk_reent+0x42>

00002c6e <__swhatbuf_r>:
    2c6e:	7119                	addi	sp,sp,-128
    2c70:	daa6                	sw	s1,116(sp)
    2c72:	84ae                	mv	s1,a1
    2c74:	00e59583          	lh	a1,14(a1)
    2c78:	dca2                	sw	s0,120(sp)
    2c7a:	de86                	sw	ra,124(sp)
    2c7c:	8432                	mv	s0,a2
    2c7e:	0005db63          	bgez	a1,2c94 <__swhatbuf_r+0x26>
    2c82:	24de                	lhu	a5,12(s1)
    2c84:	0006a023          	sw	zero,0(a3) # fc000000 <_eusrstack+0xdbff8000>
    2c88:	0807f793          	andi	a5,a5,128
    2c8c:	e785                	bnez	a5,2cb4 <__swhatbuf_r+0x46>
    2c8e:	40000793          	li	a5,1024
    2c92:	a01d                	j	2cb8 <__swhatbuf_r+0x4a>
    2c94:	0830                	addi	a2,sp,24
    2c96:	c636                	sw	a3,12(sp)
    2c98:	207000ef          	jal	ra,369e <_fstat_r>
    2c9c:	46b2                	lw	a3,12(sp)
    2c9e:	fe0542e3          	bltz	a0,2c82 <__swhatbuf_r+0x14>
    2ca2:	4772                	lw	a4,28(sp)
    2ca4:	67bd                	lui	a5,0xf
    2ca6:	8ff9                	and	a5,a5,a4
    2ca8:	7779                	lui	a4,0xffffe
    2caa:	97ba                	add	a5,a5,a4
    2cac:	0017b793          	seqz	a5,a5
    2cb0:	c29c                	sw	a5,0(a3)
    2cb2:	bff1                	j	2c8e <__swhatbuf_r+0x20>
    2cb4:	04000793          	li	a5,64
    2cb8:	c01c                	sw	a5,0(s0)
    2cba:	50f6                	lw	ra,124(sp)
    2cbc:	5466                	lw	s0,120(sp)
    2cbe:	54d6                	lw	s1,116(sp)
    2cc0:	4501                	li	a0,0
    2cc2:	6109                	addi	sp,sp,128
    2cc4:	8082                	ret

00002cc6 <__smakebuf_r>:
    2cc6:	25de                	lhu	a5,12(a1)
    2cc8:	1101                	addi	sp,sp,-32
    2cca:	cc22                	sw	s0,24(sp)
    2ccc:	ce06                	sw	ra,28(sp)
    2cce:	ca26                	sw	s1,20(sp)
    2cd0:	c84a                	sw	s2,16(sp)
    2cd2:	8b89                	andi	a5,a5,2
    2cd4:	842e                	mv	s0,a1
    2cd6:	cf89                	beqz	a5,2cf0 <__smakebuf_r+0x2a>
    2cd8:	04740793          	addi	a5,s0,71
    2cdc:	c01c                	sw	a5,0(s0)
    2cde:	c81c                	sw	a5,16(s0)
    2ce0:	4785                	li	a5,1
    2ce2:	c85c                	sw	a5,20(s0)
    2ce4:	40f2                	lw	ra,28(sp)
    2ce6:	4462                	lw	s0,24(sp)
    2ce8:	44d2                	lw	s1,20(sp)
    2cea:	4942                	lw	s2,16(sp)
    2cec:	6105                	addi	sp,sp,32
    2cee:	8082                	ret
    2cf0:	0074                	addi	a3,sp,12
    2cf2:	0030                	addi	a2,sp,8
    2cf4:	84aa                	mv	s1,a0
    2cf6:	f79ff0ef          	jal	ra,2c6e <__swhatbuf_r>
    2cfa:	45a2                	lw	a1,8(sp)
    2cfc:	892a                	mv	s2,a0
    2cfe:	8526                	mv	a0,s1
    2d00:	2201                	jal	2e00 <_malloc_r>
    2d02:	e919                	bnez	a0,2d18 <__smakebuf_r+0x52>
    2d04:	00c41783          	lh	a5,12(s0)
    2d08:	2007f713          	andi	a4,a5,512
    2d0c:	ff61                	bnez	a4,2ce4 <__smakebuf_r+0x1e>
    2d0e:	9bf1                	andi	a5,a5,-4
    2d10:	0027e793          	ori	a5,a5,2
    2d14:	a45e                	sh	a5,12(s0)
    2d16:	b7c9                	j	2cd8 <__smakebuf_r+0x12>
    2d18:	00000797          	auipc	a5,0x0
    2d1c:	d9078793          	addi	a5,a5,-624 # 2aa8 <_cleanup_r>
    2d20:	d49c                	sw	a5,40(s1)
    2d22:	245e                	lhu	a5,12(s0)
    2d24:	c008                	sw	a0,0(s0)
    2d26:	c808                	sw	a0,16(s0)
    2d28:	0807e793          	ori	a5,a5,128
    2d2c:	a45e                	sh	a5,12(s0)
    2d2e:	47a2                	lw	a5,8(sp)
    2d30:	c85c                	sw	a5,20(s0)
    2d32:	47b2                	lw	a5,12(sp)
    2d34:	cf81                	beqz	a5,2d4c <__smakebuf_r+0x86>
    2d36:	00e41583          	lh	a1,14(s0)
    2d3a:	8526                	mv	a0,s1
    2d3c:	18d000ef          	jal	ra,36c8 <_isatty_r>
    2d40:	c511                	beqz	a0,2d4c <__smakebuf_r+0x86>
    2d42:	245e                	lhu	a5,12(s0)
    2d44:	9bf1                	andi	a5,a5,-4
    2d46:	0017e793          	ori	a5,a5,1
    2d4a:	a45e                	sh	a5,12(s0)
    2d4c:	245e                	lhu	a5,12(s0)
    2d4e:	00f96933          	or	s2,s2,a5
    2d52:	01241623          	sh	s2,12(s0)
    2d56:	b779                	j	2ce4 <__smakebuf_r+0x1e>

00002d58 <_free_r>:
    2d58:	c1dd                	beqz	a1,2dfe <_free_r+0xa6>
    2d5a:	ffc5a783          	lw	a5,-4(a1)
    2d5e:	1141                	addi	sp,sp,-16
    2d60:	c422                	sw	s0,8(sp)
    2d62:	c606                	sw	ra,12(sp)
    2d64:	c226                	sw	s1,4(sp)
    2d66:	ffc58413          	addi	s0,a1,-4
    2d6a:	0007d363          	bgez	a5,2d70 <_free_r+0x18>
    2d6e:	943e                	add	s0,s0,a5
    2d70:	84aa                	mv	s1,a0
    2d72:	1c3000ef          	jal	ra,3734 <__malloc_lock>
    2d76:	83018793          	addi	a5,gp,-2000 # 200000e0 <__malloc_free_list>
    2d7a:	439c                	lw	a5,0(a5)
    2d7c:	ef81                	bnez	a5,2d94 <_free_r+0x3c>
    2d7e:	00042223          	sw	zero,4(s0)
    2d82:	8281a823          	sw	s0,-2000(gp) # 200000e0 <__malloc_free_list>
    2d86:	4422                	lw	s0,8(sp)
    2d88:	40b2                	lw	ra,12(sp)
    2d8a:	8526                	mv	a0,s1
    2d8c:	4492                	lw	s1,4(sp)
    2d8e:	0141                	addi	sp,sp,16
    2d90:	1a70006f          	j	3736 <__malloc_unlock>
    2d94:	00f47e63          	bgeu	s0,a5,2db0 <_free_r+0x58>
    2d98:	4014                	lw	a3,0(s0)
    2d9a:	00d40733          	add	a4,s0,a3
    2d9e:	00e79663          	bne	a5,a4,2daa <_free_r+0x52>
    2da2:	4398                	lw	a4,0(a5)
    2da4:	43dc                	lw	a5,4(a5)
    2da6:	9736                	add	a4,a4,a3
    2da8:	c018                	sw	a4,0(s0)
    2daa:	c05c                	sw	a5,4(s0)
    2dac:	bfd9                	j	2d82 <_free_r+0x2a>
    2dae:	87ba                	mv	a5,a4
    2db0:	43d8                	lw	a4,4(a5)
    2db2:	c319                	beqz	a4,2db8 <_free_r+0x60>
    2db4:	fee47de3          	bgeu	s0,a4,2dae <_free_r+0x56>
    2db8:	4394                	lw	a3,0(a5)
    2dba:	00d78633          	add	a2,a5,a3
    2dbe:	00861f63          	bne	a2,s0,2ddc <_free_r+0x84>
    2dc2:	4010                	lw	a2,0(s0)
    2dc4:	96b2                	add	a3,a3,a2
    2dc6:	c394                	sw	a3,0(a5)
    2dc8:	00d78633          	add	a2,a5,a3
    2dcc:	fac71de3          	bne	a4,a2,2d86 <_free_r+0x2e>
    2dd0:	4310                	lw	a2,0(a4)
    2dd2:	4358                	lw	a4,4(a4)
    2dd4:	96b2                	add	a3,a3,a2
    2dd6:	c394                	sw	a3,0(a5)
    2dd8:	c3d8                	sw	a4,4(a5)
    2dda:	b775                	j	2d86 <_free_r+0x2e>
    2ddc:	00c47563          	bgeu	s0,a2,2de6 <_free_r+0x8e>
    2de0:	47b1                	li	a5,12
    2de2:	c09c                	sw	a5,0(s1)
    2de4:	b74d                	j	2d86 <_free_r+0x2e>
    2de6:	4010                	lw	a2,0(s0)
    2de8:	00c406b3          	add	a3,s0,a2
    2dec:	00d71663          	bne	a4,a3,2df8 <_free_r+0xa0>
    2df0:	4314                	lw	a3,0(a4)
    2df2:	4358                	lw	a4,4(a4)
    2df4:	96b2                	add	a3,a3,a2
    2df6:	c014                	sw	a3,0(s0)
    2df8:	c058                	sw	a4,4(s0)
    2dfa:	c3c0                	sw	s0,4(a5)
    2dfc:	b769                	j	2d86 <_free_r+0x2e>
    2dfe:	8082                	ret

00002e00 <_malloc_r>:
    2e00:	1101                	addi	sp,sp,-32
    2e02:	ca26                	sw	s1,20(sp)
    2e04:	00358493          	addi	s1,a1,3
    2e08:	98f1                	andi	s1,s1,-4
    2e0a:	ce06                	sw	ra,28(sp)
    2e0c:	cc22                	sw	s0,24(sp)
    2e0e:	c84a                	sw	s2,16(sp)
    2e10:	c64e                	sw	s3,12(sp)
    2e12:	04a1                	addi	s1,s1,8
    2e14:	47b1                	li	a5,12
    2e16:	04f4f363          	bgeu	s1,a5,2e5c <_malloc_r+0x5c>
    2e1a:	44b1                	li	s1,12
    2e1c:	04b4e263          	bltu	s1,a1,2e60 <_malloc_r+0x60>
    2e20:	892a                	mv	s2,a0
    2e22:	113000ef          	jal	ra,3734 <__malloc_lock>
    2e26:	83018793          	addi	a5,gp,-2000 # 200000e0 <__malloc_free_list>
    2e2a:	4398                	lw	a4,0(a5)
    2e2c:	843a                	mv	s0,a4
    2e2e:	e039                	bnez	s0,2e74 <_malloc_r+0x74>
    2e30:	83418793          	addi	a5,gp,-1996 # 200000e4 <__malloc_sbrk_start>
    2e34:	439c                	lw	a5,0(a5)
    2e36:	e791                	bnez	a5,2e42 <_malloc_r+0x42>
    2e38:	4581                	li	a1,0
    2e3a:	854a                	mv	a0,s2
    2e3c:	2f25                	jal	3574 <_sbrk_r>
    2e3e:	82a1aa23          	sw	a0,-1996(gp) # 200000e4 <__malloc_sbrk_start>
    2e42:	85a6                	mv	a1,s1
    2e44:	854a                	mv	a0,s2
    2e46:	273d                	jal	3574 <_sbrk_r>
    2e48:	59fd                	li	s3,-1
    2e4a:	07351963          	bne	a0,s3,2ebc <_malloc_r+0xbc>
    2e4e:	47b1                	li	a5,12
    2e50:	00f92023          	sw	a5,0(s2)
    2e54:	854a                	mv	a0,s2
    2e56:	0e1000ef          	jal	ra,3736 <__malloc_unlock>
    2e5a:	a029                	j	2e64 <_malloc_r+0x64>
    2e5c:	fc04d0e3          	bgez	s1,2e1c <_malloc_r+0x1c>
    2e60:	47b1                	li	a5,12
    2e62:	c11c                	sw	a5,0(a0)
    2e64:	4501                	li	a0,0
    2e66:	40f2                	lw	ra,28(sp)
    2e68:	4462                	lw	s0,24(sp)
    2e6a:	44d2                	lw	s1,20(sp)
    2e6c:	4942                	lw	s2,16(sp)
    2e6e:	49b2                	lw	s3,12(sp)
    2e70:	6105                	addi	sp,sp,32
    2e72:	8082                	ret
    2e74:	401c                	lw	a5,0(s0)
    2e76:	8f85                	sub	a5,a5,s1
    2e78:	0207cf63          	bltz	a5,2eb6 <_malloc_r+0xb6>
    2e7c:	46ad                	li	a3,11
    2e7e:	00f6f663          	bgeu	a3,a5,2e8a <_malloc_r+0x8a>
    2e82:	c01c                	sw	a5,0(s0)
    2e84:	943e                	add	s0,s0,a5
    2e86:	c004                	sw	s1,0(s0)
    2e88:	a031                	j	2e94 <_malloc_r+0x94>
    2e8a:	405c                	lw	a5,4(s0)
    2e8c:	02871363          	bne	a4,s0,2eb2 <_malloc_r+0xb2>
    2e90:	82f1a823          	sw	a5,-2000(gp) # 200000e0 <__malloc_free_list>
    2e94:	854a                	mv	a0,s2
    2e96:	0a1000ef          	jal	ra,3736 <__malloc_unlock>
    2e9a:	00b40513          	addi	a0,s0,11
    2e9e:	00440793          	addi	a5,s0,4
    2ea2:	9961                	andi	a0,a0,-8
    2ea4:	40f50733          	sub	a4,a0,a5
    2ea8:	df5d                	beqz	a4,2e66 <_malloc_r+0x66>
    2eaa:	943a                	add	s0,s0,a4
    2eac:	8f89                	sub	a5,a5,a0
    2eae:	c01c                	sw	a5,0(s0)
    2eb0:	bf5d                	j	2e66 <_malloc_r+0x66>
    2eb2:	c35c                	sw	a5,4(a4)
    2eb4:	b7c5                	j	2e94 <_malloc_r+0x94>
    2eb6:	8722                	mv	a4,s0
    2eb8:	4040                	lw	s0,4(s0)
    2eba:	bf95                	j	2e2e <_malloc_r+0x2e>
    2ebc:	00350413          	addi	s0,a0,3
    2ec0:	9871                	andi	s0,s0,-4
    2ec2:	fc8502e3          	beq	a0,s0,2e86 <_malloc_r+0x86>
    2ec6:	40a405b3          	sub	a1,s0,a0
    2eca:	854a                	mv	a0,s2
    2ecc:	2565                	jal	3574 <_sbrk_r>
    2ece:	fb351ce3          	bne	a0,s3,2e86 <_malloc_r+0x86>
    2ed2:	bfb5                	j	2e4e <_malloc_r+0x4e>

00002ed4 <__sfputc_r>:
    2ed4:	461c                	lw	a5,8(a2)
    2ed6:	17fd                	addi	a5,a5,-1
    2ed8:	c61c                	sw	a5,8(a2)
    2eda:	0007da63          	bgez	a5,2eee <__sfputc_r+0x1a>
    2ede:	4e18                	lw	a4,24(a2)
    2ee0:	00e7c563          	blt	a5,a4,2eea <__sfputc_r+0x16>
    2ee4:	47a9                	li	a5,10
    2ee6:	00f59463          	bne	a1,a5,2eee <__sfputc_r+0x1a>
    2eea:	80bff06f          	j	26f4 <__swbuf_r>
    2eee:	421c                	lw	a5,0(a2)
    2ef0:	852e                	mv	a0,a1
    2ef2:	00178713          	addi	a4,a5,1
    2ef6:	c218                	sw	a4,0(a2)
    2ef8:	a38c                	sb	a1,0(a5)
    2efa:	8082                	ret

00002efc <__sfputs_r>:
    2efc:	1101                	addi	sp,sp,-32
    2efe:	cc22                	sw	s0,24(sp)
    2f00:	ca26                	sw	s1,20(sp)
    2f02:	c84a                	sw	s2,16(sp)
    2f04:	c64e                	sw	s3,12(sp)
    2f06:	c452                	sw	s4,8(sp)
    2f08:	ce06                	sw	ra,28(sp)
    2f0a:	892a                	mv	s2,a0
    2f0c:	89ae                	mv	s3,a1
    2f0e:	8432                	mv	s0,a2
    2f10:	00d604b3          	add	s1,a2,a3
    2f14:	5a7d                	li	s4,-1
    2f16:	00941463          	bne	s0,s1,2f1e <__sfputs_r+0x22>
    2f1a:	4501                	li	a0,0
    2f1c:	a809                	j	2f2e <__sfputs_r+0x32>
    2f1e:	200c                	lbu	a1,0(s0)
    2f20:	864e                	mv	a2,s3
    2f22:	854a                	mv	a0,s2
    2f24:	fb1ff0ef          	jal	ra,2ed4 <__sfputc_r>
    2f28:	0405                	addi	s0,s0,1
    2f2a:	ff4516e3          	bne	a0,s4,2f16 <__sfputs_r+0x1a>
    2f2e:	40f2                	lw	ra,28(sp)
    2f30:	4462                	lw	s0,24(sp)
    2f32:	44d2                	lw	s1,20(sp)
    2f34:	4942                	lw	s2,16(sp)
    2f36:	49b2                	lw	s3,12(sp)
    2f38:	4a22                	lw	s4,8(sp)
    2f3a:	6105                	addi	sp,sp,32
    2f3c:	8082                	ret

00002f3e <_vfiprintf_r>:
    2f3e:	7135                	addi	sp,sp,-160
    2f40:	cd22                	sw	s0,152(sp)
    2f42:	cb26                	sw	s1,148(sp)
    2f44:	c94a                	sw	s2,144(sp)
    2f46:	c74e                	sw	s3,140(sp)
    2f48:	cf06                	sw	ra,156(sp)
    2f4a:	c552                	sw	s4,136(sp)
    2f4c:	c356                	sw	s5,132(sp)
    2f4e:	c15a                	sw	s6,128(sp)
    2f50:	dede                	sw	s7,124(sp)
    2f52:	dce2                	sw	s8,120(sp)
    2f54:	dae6                	sw	s9,116(sp)
    2f56:	89aa                	mv	s3,a0
    2f58:	84ae                	mv	s1,a1
    2f5a:	8932                	mv	s2,a2
    2f5c:	8436                	mv	s0,a3
    2f5e:	c509                	beqz	a0,2f68 <_vfiprintf_r+0x2a>
    2f60:	4d1c                	lw	a5,24(a0)
    2f62:	e399                	bnez	a5,2f68 <_vfiprintf_r+0x2a>
    2f64:	b95ff0ef          	jal	ra,2af8 <__sinit>
    2f68:	00001797          	auipc	a5,0x1
    2f6c:	b9c78793          	addi	a5,a5,-1124 # 3b04 <__sf_fake_stdin>
    2f70:	0cf49863          	bne	s1,a5,3040 <_vfiprintf_r+0x102>
    2f74:	0049a483          	lw	s1,4(s3)
    2f78:	24de                	lhu	a5,12(s1)
    2f7a:	8ba1                	andi	a5,a5,8
    2f7c:	c7e5                	beqz	a5,3064 <_vfiprintf_r+0x126>
    2f7e:	489c                	lw	a5,16(s1)
    2f80:	c3f5                	beqz	a5,3064 <_vfiprintf_r+0x126>
    2f82:	02000793          	li	a5,32
    2f86:	02f104a3          	sb	a5,41(sp)
    2f8a:	03000793          	li	a5,48
    2f8e:	d202                	sw	zero,36(sp)
    2f90:	02f10523          	sb	a5,42(sp)
    2f94:	c622                	sw	s0,12(sp)
    2f96:	02500b93          	li	s7,37
    2f9a:	00001a97          	auipc	s5,0x1
    2f9e:	baaa8a93          	addi	s5,s5,-1110 # 3b44 <__sf_fake_stdout+0x20>
    2fa2:	4c05                	li	s8,1
    2fa4:	4b29                	li	s6,10
    2fa6:	844a                	mv	s0,s2
    2fa8:	201c                	lbu	a5,0(s0)
    2faa:	c399                	beqz	a5,2fb0 <_vfiprintf_r+0x72>
    2fac:	0d779f63          	bne	a5,s7,308a <_vfiprintf_r+0x14c>
    2fb0:	41240cb3          	sub	s9,s0,s2
    2fb4:	000c8e63          	beqz	s9,2fd0 <_vfiprintf_r+0x92>
    2fb8:	86e6                	mv	a3,s9
    2fba:	864a                	mv	a2,s2
    2fbc:	85a6                	mv	a1,s1
    2fbe:	854e                	mv	a0,s3
    2fc0:	f3dff0ef          	jal	ra,2efc <__sfputs_r>
    2fc4:	57fd                	li	a5,-1
    2fc6:	1cf50f63          	beq	a0,a5,31a4 <_vfiprintf_r+0x266>
    2fca:	5692                	lw	a3,36(sp)
    2fcc:	96e6                	add	a3,a3,s9
    2fce:	d236                	sw	a3,36(sp)
    2fd0:	201c                	lbu	a5,0(s0)
    2fd2:	1c078963          	beqz	a5,31a4 <_vfiprintf_r+0x266>
    2fd6:	57fd                	li	a5,-1
    2fd8:	00140913          	addi	s2,s0,1
    2fdc:	c802                	sw	zero,16(sp)
    2fde:	ce02                	sw	zero,28(sp)
    2fe0:	ca3e                	sw	a5,20(sp)
    2fe2:	cc02                	sw	zero,24(sp)
    2fe4:	040109a3          	sb	zero,83(sp)
    2fe8:	d482                	sw	zero,104(sp)
    2fea:	00094583          	lbu	a1,0(s2)
    2fee:	4615                	li	a2,5
    2ff0:	8556                	mv	a0,s5
    2ff2:	272d                	jal	371c <memchr>
    2ff4:	00190413          	addi	s0,s2,1
    2ff8:	47c2                	lw	a5,16(sp)
    2ffa:	e951                	bnez	a0,308e <_vfiprintf_r+0x150>
    2ffc:	0107f713          	andi	a4,a5,16
    3000:	c709                	beqz	a4,300a <_vfiprintf_r+0xcc>
    3002:	02000713          	li	a4,32
    3006:	04e109a3          	sb	a4,83(sp)
    300a:	0087f713          	andi	a4,a5,8
    300e:	c709                	beqz	a4,3018 <_vfiprintf_r+0xda>
    3010:	02b00713          	li	a4,43
    3014:	04e109a3          	sb	a4,83(sp)
    3018:	00094683          	lbu	a3,0(s2)
    301c:	02a00713          	li	a4,42
    3020:	06e68f63          	beq	a3,a4,309e <_vfiprintf_r+0x160>
    3024:	47f2                	lw	a5,28(sp)
    3026:	844a                	mv	s0,s2
    3028:	4681                	li	a3,0
    302a:	4625                	li	a2,9
    302c:	2018                	lbu	a4,0(s0)
    302e:	00140593          	addi	a1,s0,1
    3032:	fd070713          	addi	a4,a4,-48 # ffffdfd0 <_eusrstack+0xdfff5fd0>
    3036:	0ae67763          	bgeu	a2,a4,30e4 <_vfiprintf_r+0x1a6>
    303a:	cab5                	beqz	a3,30ae <_vfiprintf_r+0x170>
    303c:	ce3e                	sw	a5,28(sp)
    303e:	a885                	j	30ae <_vfiprintf_r+0x170>
    3040:	00001797          	auipc	a5,0x1
    3044:	ae478793          	addi	a5,a5,-1308 # 3b24 <__sf_fake_stdout>
    3048:	00f49563          	bne	s1,a5,3052 <_vfiprintf_r+0x114>
    304c:	0089a483          	lw	s1,8(s3)
    3050:	b725                	j	2f78 <_vfiprintf_r+0x3a>
    3052:	00001797          	auipc	a5,0x1
    3056:	a9278793          	addi	a5,a5,-1390 # 3ae4 <__sf_fake_stderr>
    305a:	f0f49fe3          	bne	s1,a5,2f78 <_vfiprintf_r+0x3a>
    305e:	00c9a483          	lw	s1,12(s3)
    3062:	bf19                	j	2f78 <_vfiprintf_r+0x3a>
    3064:	85a6                	mv	a1,s1
    3066:	854e                	mv	a0,s3
    3068:	f48ff0ef          	jal	ra,27b0 <__swsetup_r>
    306c:	d919                	beqz	a0,2f82 <_vfiprintf_r+0x44>
    306e:	557d                	li	a0,-1
    3070:	40fa                	lw	ra,156(sp)
    3072:	446a                	lw	s0,152(sp)
    3074:	44da                	lw	s1,148(sp)
    3076:	494a                	lw	s2,144(sp)
    3078:	49ba                	lw	s3,140(sp)
    307a:	4a2a                	lw	s4,136(sp)
    307c:	4a9a                	lw	s5,132(sp)
    307e:	4b0a                	lw	s6,128(sp)
    3080:	5bf6                	lw	s7,124(sp)
    3082:	5c66                	lw	s8,120(sp)
    3084:	5cd6                	lw	s9,116(sp)
    3086:	610d                	addi	sp,sp,160
    3088:	8082                	ret
    308a:	0405                	addi	s0,s0,1
    308c:	bf31                	j	2fa8 <_vfiprintf_r+0x6a>
    308e:	41550533          	sub	a0,a0,s5
    3092:	00ac1533          	sll	a0,s8,a0
    3096:	8fc9                	or	a5,a5,a0
    3098:	c83e                	sw	a5,16(sp)
    309a:	8922                	mv	s2,s0
    309c:	b7b9                	j	2fea <_vfiprintf_r+0xac>
    309e:	4732                	lw	a4,12(sp)
    30a0:	00470693          	addi	a3,a4,4
    30a4:	4318                	lw	a4,0(a4)
    30a6:	c636                	sw	a3,12(sp)
    30a8:	02074763          	bltz	a4,30d6 <_vfiprintf_r+0x198>
    30ac:	ce3a                	sw	a4,28(sp)
    30ae:	2018                	lbu	a4,0(s0)
    30b0:	02e00793          	li	a5,46
    30b4:	04f71d63          	bne	a4,a5,310e <_vfiprintf_r+0x1d0>
    30b8:	3018                	lbu	a4,1(s0)
    30ba:	02a00793          	li	a5,42
    30be:	02f71b63          	bne	a4,a5,30f4 <_vfiprintf_r+0x1b6>
    30c2:	47b2                	lw	a5,12(sp)
    30c4:	0409                	addi	s0,s0,2
    30c6:	00478713          	addi	a4,a5,4
    30ca:	439c                	lw	a5,0(a5)
    30cc:	c63a                	sw	a4,12(sp)
    30ce:	0207c163          	bltz	a5,30f0 <_vfiprintf_r+0x1b2>
    30d2:	ca3e                	sw	a5,20(sp)
    30d4:	a82d                	j	310e <_vfiprintf_r+0x1d0>
    30d6:	40e00733          	neg	a4,a4
    30da:	0027e793          	ori	a5,a5,2
    30de:	ce3a                	sw	a4,28(sp)
    30e0:	c83e                	sw	a5,16(sp)
    30e2:	b7f1                	j	30ae <_vfiprintf_r+0x170>
    30e4:	036787b3          	mul	a5,a5,s6
    30e8:	4685                	li	a3,1
    30ea:	842e                	mv	s0,a1
    30ec:	97ba                	add	a5,a5,a4
    30ee:	bf3d                	j	302c <_vfiprintf_r+0xee>
    30f0:	57fd                	li	a5,-1
    30f2:	b7c5                	j	30d2 <_vfiprintf_r+0x194>
    30f4:	0405                	addi	s0,s0,1
    30f6:	ca02                	sw	zero,20(sp)
    30f8:	4681                	li	a3,0
    30fa:	4781                	li	a5,0
    30fc:	4625                	li	a2,9
    30fe:	2018                	lbu	a4,0(s0)
    3100:	00140593          	addi	a1,s0,1
    3104:	fd070713          	addi	a4,a4,-48
    3108:	06e67463          	bgeu	a2,a4,3170 <_vfiprintf_r+0x232>
    310c:	f2f9                	bnez	a3,30d2 <_vfiprintf_r+0x194>
    310e:	200c                	lbu	a1,0(s0)
    3110:	460d                	li	a2,3
    3112:	00001517          	auipc	a0,0x1
    3116:	a3a50513          	addi	a0,a0,-1478 # 3b4c <__sf_fake_stdout+0x28>
    311a:	2509                	jal	371c <memchr>
    311c:	cd11                	beqz	a0,3138 <_vfiprintf_r+0x1fa>
    311e:	00001797          	auipc	a5,0x1
    3122:	a2e78793          	addi	a5,a5,-1490 # 3b4c <__sf_fake_stdout+0x28>
    3126:	8d1d                	sub	a0,a0,a5
    3128:	04000793          	li	a5,64
    312c:	00a797b3          	sll	a5,a5,a0
    3130:	4542                	lw	a0,16(sp)
    3132:	0405                	addi	s0,s0,1
    3134:	8d5d                	or	a0,a0,a5
    3136:	c82a                	sw	a0,16(sp)
    3138:	200c                	lbu	a1,0(s0)
    313a:	4619                	li	a2,6
    313c:	00001517          	auipc	a0,0x1
    3140:	a1450513          	addi	a0,a0,-1516 # 3b50 <__sf_fake_stdout+0x2c>
    3144:	00140913          	addi	s2,s0,1
    3148:	02b10423          	sb	a1,40(sp)
    314c:	2bc1                	jal	371c <memchr>
    314e:	c135                	beqz	a0,31b2 <_vfiprintf_r+0x274>
    3150:	ffffd797          	auipc	a5,0xffffd
    3154:	eb078793          	addi	a5,a5,-336 # 0 <_sinit>
    3158:	e795                	bnez	a5,3184 <_vfiprintf_r+0x246>
    315a:	4742                	lw	a4,16(sp)
    315c:	47b2                	lw	a5,12(sp)
    315e:	10077713          	andi	a4,a4,256
    3162:	cf09                	beqz	a4,317c <_vfiprintf_r+0x23e>
    3164:	0791                	addi	a5,a5,4
    3166:	c63e                	sw	a5,12(sp)
    3168:	5792                	lw	a5,36(sp)
    316a:	97d2                	add	a5,a5,s4
    316c:	d23e                	sw	a5,36(sp)
    316e:	bd25                	j	2fa6 <_vfiprintf_r+0x68>
    3170:	036787b3          	mul	a5,a5,s6
    3174:	4685                	li	a3,1
    3176:	842e                	mv	s0,a1
    3178:	97ba                	add	a5,a5,a4
    317a:	b751                	j	30fe <_vfiprintf_r+0x1c0>
    317c:	079d                	addi	a5,a5,7
    317e:	9be1                	andi	a5,a5,-8
    3180:	07a1                	addi	a5,a5,8
    3182:	b7d5                	j	3166 <_vfiprintf_r+0x228>
    3184:	0078                	addi	a4,sp,12
    3186:	00000697          	auipc	a3,0x0
    318a:	d7668693          	addi	a3,a3,-650 # 2efc <__sfputs_r>
    318e:	8626                	mv	a2,s1
    3190:	080c                	addi	a1,sp,16
    3192:	854e                	mv	a0,s3
    3194:	00000097          	auipc	ra,0x0
    3198:	000000e7          	jalr	zero # 0 <_sinit>
    319c:	57fd                	li	a5,-1
    319e:	8a2a                	mv	s4,a0
    31a0:	fcf514e3          	bne	a0,a5,3168 <_vfiprintf_r+0x22a>
    31a4:	24de                	lhu	a5,12(s1)
    31a6:	0407f793          	andi	a5,a5,64
    31aa:	ec0792e3          	bnez	a5,306e <_vfiprintf_r+0x130>
    31ae:	5512                	lw	a0,36(sp)
    31b0:	b5c1                	j	3070 <_vfiprintf_r+0x132>
    31b2:	0078                	addi	a4,sp,12
    31b4:	00000697          	auipc	a3,0x0
    31b8:	d4868693          	addi	a3,a3,-696 # 2efc <__sfputs_r>
    31bc:	8626                	mv	a2,s1
    31be:	080c                	addi	a1,sp,16
    31c0:	854e                	mv	a0,s3
    31c2:	2a01                	jal	32d2 <_printf_i>
    31c4:	bfe1                	j	319c <_vfiprintf_r+0x25e>

000031c6 <_printf_common>:
    31c6:	7179                	addi	sp,sp,-48
    31c8:	ca56                	sw	s5,20(sp)
    31ca:	499c                	lw	a5,16(a1)
    31cc:	8aba                	mv	s5,a4
    31ce:	4598                	lw	a4,8(a1)
    31d0:	d422                	sw	s0,40(sp)
    31d2:	d226                	sw	s1,36(sp)
    31d4:	ce4e                	sw	s3,28(sp)
    31d6:	cc52                	sw	s4,24(sp)
    31d8:	d606                	sw	ra,44(sp)
    31da:	d04a                	sw	s2,32(sp)
    31dc:	c85a                	sw	s6,16(sp)
    31de:	c65e                	sw	s7,12(sp)
    31e0:	89aa                	mv	s3,a0
    31e2:	842e                	mv	s0,a1
    31e4:	84b2                	mv	s1,a2
    31e6:	8a36                	mv	s4,a3
    31e8:	00e7d363          	bge	a5,a4,31ee <_printf_common+0x28>
    31ec:	87ba                	mv	a5,a4
    31ee:	c09c                	sw	a5,0(s1)
    31f0:	04344703          	lbu	a4,67(s0)
    31f4:	c319                	beqz	a4,31fa <_printf_common+0x34>
    31f6:	0785                	addi	a5,a5,1
    31f8:	c09c                	sw	a5,0(s1)
    31fa:	401c                	lw	a5,0(s0)
    31fc:	0207f793          	andi	a5,a5,32
    3200:	c781                	beqz	a5,3208 <_printf_common+0x42>
    3202:	409c                	lw	a5,0(s1)
    3204:	0789                	addi	a5,a5,2
    3206:	c09c                	sw	a5,0(s1)
    3208:	00042903          	lw	s2,0(s0)
    320c:	00697913          	andi	s2,s2,6
    3210:	00091a63          	bnez	s2,3224 <_printf_common+0x5e>
    3214:	01940b13          	addi	s6,s0,25
    3218:	5bfd                	li	s7,-1
    321a:	445c                	lw	a5,12(s0)
    321c:	4098                	lw	a4,0(s1)
    321e:	8f99                	sub	a5,a5,a4
    3220:	04f94c63          	blt	s2,a5,3278 <_printf_common+0xb2>
    3224:	401c                	lw	a5,0(s0)
    3226:	04344683          	lbu	a3,67(s0)
    322a:	0207f793          	andi	a5,a5,32
    322e:	00d036b3          	snez	a3,a3
    3232:	eba5                	bnez	a5,32a2 <_printf_common+0xdc>
    3234:	04340613          	addi	a2,s0,67
    3238:	85d2                	mv	a1,s4
    323a:	854e                	mv	a0,s3
    323c:	9a82                	jalr	s5
    323e:	57fd                	li	a5,-1
    3240:	04f50363          	beq	a0,a5,3286 <_printf_common+0xc0>
    3244:	401c                	lw	a5,0(s0)
    3246:	4611                	li	a2,4
    3248:	4098                	lw	a4,0(s1)
    324a:	8b99                	andi	a5,a5,6
    324c:	4454                	lw	a3,12(s0)
    324e:	4481                	li	s1,0
    3250:	00c79763          	bne	a5,a2,325e <_printf_common+0x98>
    3254:	40e684b3          	sub	s1,a3,a4
    3258:	0004d363          	bgez	s1,325e <_printf_common+0x98>
    325c:	4481                	li	s1,0
    325e:	441c                	lw	a5,8(s0)
    3260:	4818                	lw	a4,16(s0)
    3262:	00f75463          	bge	a4,a5,326a <_printf_common+0xa4>
    3266:	8f99                	sub	a5,a5,a4
    3268:	94be                	add	s1,s1,a5
    326a:	4901                	li	s2,0
    326c:	0469                	addi	s0,s0,26
    326e:	5b7d                	li	s6,-1
    3270:	05249863          	bne	s1,s2,32c0 <_printf_common+0xfa>
    3274:	4501                	li	a0,0
    3276:	a809                	j	3288 <_printf_common+0xc2>
    3278:	4685                	li	a3,1
    327a:	865a                	mv	a2,s6
    327c:	85d2                	mv	a1,s4
    327e:	854e                	mv	a0,s3
    3280:	9a82                	jalr	s5
    3282:	01751e63          	bne	a0,s7,329e <_printf_common+0xd8>
    3286:	557d                	li	a0,-1
    3288:	50b2                	lw	ra,44(sp)
    328a:	5422                	lw	s0,40(sp)
    328c:	5492                	lw	s1,36(sp)
    328e:	5902                	lw	s2,32(sp)
    3290:	49f2                	lw	s3,28(sp)
    3292:	4a62                	lw	s4,24(sp)
    3294:	4ad2                	lw	s5,20(sp)
    3296:	4b42                	lw	s6,16(sp)
    3298:	4bb2                	lw	s7,12(sp)
    329a:	6145                	addi	sp,sp,48
    329c:	8082                	ret
    329e:	0905                	addi	s2,s2,1
    32a0:	bfad                	j	321a <_printf_common+0x54>
    32a2:	00d40733          	add	a4,s0,a3
    32a6:	03000613          	li	a2,48
    32aa:	04c701a3          	sb	a2,67(a4)
    32ae:	04544703          	lbu	a4,69(s0)
    32b2:	00168793          	addi	a5,a3,1
    32b6:	97a2                	add	a5,a5,s0
    32b8:	0689                	addi	a3,a3,2
    32ba:	04e781a3          	sb	a4,67(a5)
    32be:	bf9d                	j	3234 <_printf_common+0x6e>
    32c0:	4685                	li	a3,1
    32c2:	8622                	mv	a2,s0
    32c4:	85d2                	mv	a1,s4
    32c6:	854e                	mv	a0,s3
    32c8:	9a82                	jalr	s5
    32ca:	fb650ee3          	beq	a0,s6,3286 <_printf_common+0xc0>
    32ce:	0905                	addi	s2,s2,1
    32d0:	b745                	j	3270 <_printf_common+0xaa>

000032d2 <_printf_i>:
    32d2:	7179                	addi	sp,sp,-48
    32d4:	d422                	sw	s0,40(sp)
    32d6:	d226                	sw	s1,36(sp)
    32d8:	d04a                	sw	s2,32(sp)
    32da:	ce4e                	sw	s3,28(sp)
    32dc:	d606                	sw	ra,44(sp)
    32de:	cc52                	sw	s4,24(sp)
    32e0:	ca56                	sw	s5,20(sp)
    32e2:	c85a                	sw	s6,16(sp)
    32e4:	89b6                	mv	s3,a3
    32e6:	2d94                	lbu	a3,24(a1)
    32e8:	06900793          	li	a5,105
    32ec:	8932                	mv	s2,a2
    32ee:	84aa                	mv	s1,a0
    32f0:	842e                	mv	s0,a1
    32f2:	04358613          	addi	a2,a1,67
    32f6:	02f68d63          	beq	a3,a5,3330 <_printf_i+0x5e>
    32fa:	06d7e263          	bltu	a5,a3,335e <_printf_i+0x8c>
    32fe:	05800793          	li	a5,88
    3302:	18f68663          	beq	a3,a5,348e <_printf_i+0x1bc>
    3306:	00d7ed63          	bltu	a5,a3,3320 <_printf_i+0x4e>
    330a:	20068e63          	beqz	a3,3526 <_printf_i+0x254>
    330e:	04300793          	li	a5,67
    3312:	0af68e63          	beq	a3,a5,33ce <_printf_i+0xfc>
    3316:	04240a93          	addi	s5,s0,66
    331a:	04d40123          	sb	a3,66(s0)
    331e:	a0c9                	j	33e0 <_printf_i+0x10e>
    3320:	06300793          	li	a5,99
    3324:	0af68563          	beq	a3,a5,33ce <_printf_i+0xfc>
    3328:	06400793          	li	a5,100
    332c:	fef695e3          	bne	a3,a5,3316 <_printf_i+0x44>
    3330:	401c                	lw	a5,0(s0)
    3332:	4308                	lw	a0,0(a4)
    3334:	0807f693          	andi	a3,a5,128
    3338:	00450593          	addi	a1,a0,4
    333c:	c6c5                	beqz	a3,33e4 <_printf_i+0x112>
    333e:	411c                	lw	a5,0(a0)
    3340:	c30c                	sw	a1,0(a4)
    3342:	0007d863          	bgez	a5,3352 <_printf_i+0x80>
    3346:	02d00713          	li	a4,45
    334a:	40f007b3          	neg	a5,a5
    334e:	04e401a3          	sb	a4,67(s0)
    3352:	00001697          	auipc	a3,0x1
    3356:	80668693          	addi	a3,a3,-2042 # 3b58 <__sf_fake_stdout+0x34>
    335a:	4729                	li	a4,10
    335c:	a865                	j	3414 <_printf_i+0x142>
    335e:	07000793          	li	a5,112
    3362:	16f68263          	beq	a3,a5,34c6 <_printf_i+0x1f4>
    3366:	02d7e563          	bltu	a5,a3,3390 <_printf_i+0xbe>
    336a:	06e00793          	li	a5,110
    336e:	18f68963          	beq	a3,a5,3500 <_printf_i+0x22e>
    3372:	06f00793          	li	a5,111
    3376:	faf690e3          	bne	a3,a5,3316 <_printf_i+0x44>
    337a:	400c                	lw	a1,0(s0)
    337c:	431c                	lw	a5,0(a4)
    337e:	0805f813          	andi	a6,a1,128
    3382:	00478513          	addi	a0,a5,4
    3386:	06080763          	beqz	a6,33f4 <_printf_i+0x122>
    338a:	c308                	sw	a0,0(a4)
    338c:	439c                	lw	a5,0(a5)
    338e:	a885                	j	33fe <_printf_i+0x12c>
    3390:	07500793          	li	a5,117
    3394:	fef683e3          	beq	a3,a5,337a <_printf_i+0xa8>
    3398:	07800793          	li	a5,120
    339c:	12f68963          	beq	a3,a5,34ce <_printf_i+0x1fc>
    33a0:	07300793          	li	a5,115
    33a4:	f6f699e3          	bne	a3,a5,3316 <_printf_i+0x44>
    33a8:	431c                	lw	a5,0(a4)
    33aa:	41d0                	lw	a2,4(a1)
    33ac:	4581                	li	a1,0
    33ae:	00478693          	addi	a3,a5,4
    33b2:	c314                	sw	a3,0(a4)
    33b4:	0007aa83          	lw	s5,0(a5)
    33b8:	8556                	mv	a0,s5
    33ba:	268d                	jal	371c <memchr>
    33bc:	c501                	beqz	a0,33c4 <_printf_i+0xf2>
    33be:	41550533          	sub	a0,a0,s5
    33c2:	c048                	sw	a0,4(s0)
    33c4:	405c                	lw	a5,4(s0)
    33c6:	c81c                	sw	a5,16(s0)
    33c8:	040401a3          	sb	zero,67(s0)
    33cc:	a861                	j	3464 <_printf_i+0x192>
    33ce:	431c                	lw	a5,0(a4)
    33d0:	04240a93          	addi	s5,s0,66
    33d4:	00478693          	addi	a3,a5,4
    33d8:	439c                	lw	a5,0(a5)
    33da:	c314                	sw	a3,0(a4)
    33dc:	04f40123          	sb	a5,66(s0)
    33e0:	4785                	li	a5,1
    33e2:	b7d5                	j	33c6 <_printf_i+0xf4>
    33e4:	0407f693          	andi	a3,a5,64
    33e8:	411c                	lw	a5,0(a0)
    33ea:	c30c                	sw	a1,0(a4)
    33ec:	dab9                	beqz	a3,3342 <_printf_i+0x70>
    33ee:	07c2                	slli	a5,a5,0x10
    33f0:	87c1                	srai	a5,a5,0x10
    33f2:	bf81                	j	3342 <_printf_i+0x70>
    33f4:	0405f593          	andi	a1,a1,64
    33f8:	c308                	sw	a0,0(a4)
    33fa:	d9c9                	beqz	a1,338c <_printf_i+0xba>
    33fc:	239e                	lhu	a5,0(a5)
    33fe:	06f00713          	li	a4,111
    3402:	0ee68763          	beq	a3,a4,34f0 <_printf_i+0x21e>
    3406:	00000697          	auipc	a3,0x0
    340a:	75268693          	addi	a3,a3,1874 # 3b58 <__sf_fake_stdout+0x34>
    340e:	4729                	li	a4,10
    3410:	040401a3          	sb	zero,67(s0)
    3414:	404c                	lw	a1,4(s0)
    3416:	c40c                	sw	a1,8(s0)
    3418:	0005c563          	bltz	a1,3422 <_printf_i+0x150>
    341c:	4008                	lw	a0,0(s0)
    341e:	996d                	andi	a0,a0,-5
    3420:	c008                	sw	a0,0(s0)
    3422:	e399                	bnez	a5,3428 <_printf_i+0x156>
    3424:	8ab2                	mv	s5,a2
    3426:	cd89                	beqz	a1,3440 <_printf_i+0x16e>
    3428:	8ab2                	mv	s5,a2
    342a:	02e7f5b3          	remu	a1,a5,a4
    342e:	1afd                	addi	s5,s5,-1
    3430:	95b6                	add	a1,a1,a3
    3432:	218c                	lbu	a1,0(a1)
    3434:	00ba8023          	sb	a1,0(s5)
    3438:	02e7d5b3          	divu	a1,a5,a4
    343c:	0ce7f063          	bgeu	a5,a4,34fc <_printf_i+0x22a>
    3440:	47a1                	li	a5,8
    3442:	00f71e63          	bne	a4,a5,345e <_printf_i+0x18c>
    3446:	401c                	lw	a5,0(s0)
    3448:	8b85                	andi	a5,a5,1
    344a:	cb91                	beqz	a5,345e <_printf_i+0x18c>
    344c:	4058                	lw	a4,4(s0)
    344e:	481c                	lw	a5,16(s0)
    3450:	00e7c763          	blt	a5,a4,345e <_printf_i+0x18c>
    3454:	03000793          	li	a5,48
    3458:	fefa8fa3          	sb	a5,-1(s5)
    345c:	1afd                	addi	s5,s5,-1
    345e:	41560633          	sub	a2,a2,s5
    3462:	c810                	sw	a2,16(s0)
    3464:	874e                	mv	a4,s3
    3466:	86ca                	mv	a3,s2
    3468:	0070                	addi	a2,sp,12
    346a:	85a2                	mv	a1,s0
    346c:	8526                	mv	a0,s1
    346e:	d59ff0ef          	jal	ra,31c6 <_printf_common>
    3472:	5a7d                	li	s4,-1
    3474:	0b451d63          	bne	a0,s4,352e <_printf_i+0x25c>
    3478:	557d                	li	a0,-1
    347a:	50b2                	lw	ra,44(sp)
    347c:	5422                	lw	s0,40(sp)
    347e:	5492                	lw	s1,36(sp)
    3480:	5902                	lw	s2,32(sp)
    3482:	49f2                	lw	s3,28(sp)
    3484:	4a62                	lw	s4,24(sp)
    3486:	4ad2                	lw	s5,20(sp)
    3488:	4b42                	lw	s6,16(sp)
    348a:	6145                	addi	sp,sp,48
    348c:	8082                	ret
    348e:	04d582a3          	sb	a3,69(a1)
    3492:	00000697          	auipc	a3,0x0
    3496:	6c668693          	addi	a3,a3,1734 # 3b58 <__sf_fake_stdout+0x34>
    349a:	400c                	lw	a1,0(s0)
    349c:	4308                	lw	a0,0(a4)
    349e:	0805f813          	andi	a6,a1,128
    34a2:	411c                	lw	a5,0(a0)
    34a4:	0511                	addi	a0,a0,4
    34a6:	02080d63          	beqz	a6,34e0 <_printf_i+0x20e>
    34aa:	c308                	sw	a0,0(a4)
    34ac:	0015f713          	andi	a4,a1,1
    34b0:	c701                	beqz	a4,34b8 <_printf_i+0x1e6>
    34b2:	0205e593          	ori	a1,a1,32
    34b6:	c00c                	sw	a1,0(s0)
    34b8:	4741                	li	a4,16
    34ba:	fbb9                	bnez	a5,3410 <_printf_i+0x13e>
    34bc:	400c                	lw	a1,0(s0)
    34be:	fdf5f593          	andi	a1,a1,-33
    34c2:	c00c                	sw	a1,0(s0)
    34c4:	b7b1                	j	3410 <_printf_i+0x13e>
    34c6:	419c                	lw	a5,0(a1)
    34c8:	0207e793          	ori	a5,a5,32
    34cc:	c19c                	sw	a5,0(a1)
    34ce:	07800793          	li	a5,120
    34d2:	04f402a3          	sb	a5,69(s0)
    34d6:	00000697          	auipc	a3,0x0
    34da:	69668693          	addi	a3,a3,1686 # 3b6c <__sf_fake_stdout+0x48>
    34de:	bf75                	j	349a <_printf_i+0x1c8>
    34e0:	0405f813          	andi	a6,a1,64
    34e4:	c308                	sw	a0,0(a4)
    34e6:	fc0803e3          	beqz	a6,34ac <_printf_i+0x1da>
    34ea:	07c2                	slli	a5,a5,0x10
    34ec:	83c1                	srli	a5,a5,0x10
    34ee:	bf7d                	j	34ac <_printf_i+0x1da>
    34f0:	00000697          	auipc	a3,0x0
    34f4:	66868693          	addi	a3,a3,1640 # 3b58 <__sf_fake_stdout+0x34>
    34f8:	4721                	li	a4,8
    34fa:	bf19                	j	3410 <_printf_i+0x13e>
    34fc:	87ae                	mv	a5,a1
    34fe:	b735                	j	342a <_printf_i+0x158>
    3500:	4194                	lw	a3,0(a1)
    3502:	431c                	lw	a5,0(a4)
    3504:	49cc                	lw	a1,20(a1)
    3506:	0806f813          	andi	a6,a3,128
    350a:	00478513          	addi	a0,a5,4
    350e:	00080663          	beqz	a6,351a <_printf_i+0x248>
    3512:	c308                	sw	a0,0(a4)
    3514:	439c                	lw	a5,0(a5)
    3516:	c38c                	sw	a1,0(a5)
    3518:	a039                	j	3526 <_printf_i+0x254>
    351a:	c308                	sw	a0,0(a4)
    351c:	0406f693          	andi	a3,a3,64
    3520:	439c                	lw	a5,0(a5)
    3522:	daf5                	beqz	a3,3516 <_printf_i+0x244>
    3524:	a38e                	sh	a1,0(a5)
    3526:	00042823          	sw	zero,16(s0)
    352a:	8ab2                	mv	s5,a2
    352c:	bf25                	j	3464 <_printf_i+0x192>
    352e:	4814                	lw	a3,16(s0)
    3530:	8656                	mv	a2,s5
    3532:	85ca                	mv	a1,s2
    3534:	8526                	mv	a0,s1
    3536:	9982                	jalr	s3
    3538:	f54500e3          	beq	a0,s4,3478 <_printf_i+0x1a6>
    353c:	401c                	lw	a5,0(s0)
    353e:	8b89                	andi	a5,a5,2
    3540:	e78d                	bnez	a5,356a <_printf_i+0x298>
    3542:	47b2                	lw	a5,12(sp)
    3544:	4448                	lw	a0,12(s0)
    3546:	f2f55ae3          	bge	a0,a5,347a <_printf_i+0x1a8>
    354a:	853e                	mv	a0,a5
    354c:	b73d                	j	347a <_printf_i+0x1a8>
    354e:	4685                	li	a3,1
    3550:	8656                	mv	a2,s5
    3552:	85ca                	mv	a1,s2
    3554:	8526                	mv	a0,s1
    3556:	9982                	jalr	s3
    3558:	f36500e3          	beq	a0,s6,3478 <_printf_i+0x1a6>
    355c:	0a05                	addi	s4,s4,1
    355e:	445c                	lw	a5,12(s0)
    3560:	4732                	lw	a4,12(sp)
    3562:	8f99                	sub	a5,a5,a4
    3564:	fefa45e3          	blt	s4,a5,354e <_printf_i+0x27c>
    3568:	bfe9                	j	3542 <_printf_i+0x270>
    356a:	4a01                	li	s4,0
    356c:	01940a93          	addi	s5,s0,25
    3570:	5b7d                	li	s6,-1
    3572:	b7f5                	j	355e <_printf_i+0x28c>

00003574 <_sbrk_r>:
    3574:	1141                	addi	sp,sp,-16
    3576:	c422                	sw	s0,8(sp)
    3578:	842a                	mv	s0,a0
    357a:	852e                	mv	a0,a1
    357c:	8801a023          	sw	zero,-1920(gp) # 20000130 <errno>
    3580:	c606                	sw	ra,12(sp)
    3582:	f22fe0ef          	jal	ra,1ca4 <_sbrk>
    3586:	57fd                	li	a5,-1
    3588:	00f51763          	bne	a0,a5,3596 <_sbrk_r+0x22>
    358c:	88018793          	addi	a5,gp,-1920 # 20000130 <errno>
    3590:	439c                	lw	a5,0(a5)
    3592:	c391                	beqz	a5,3596 <_sbrk_r+0x22>
    3594:	c01c                	sw	a5,0(s0)
    3596:	40b2                	lw	ra,12(sp)
    3598:	4422                	lw	s0,8(sp)
    359a:	0141                	addi	sp,sp,16
    359c:	8082                	ret

0000359e <__sread>:
    359e:	1141                	addi	sp,sp,-16
    35a0:	c422                	sw	s0,8(sp)
    35a2:	842e                	mv	s0,a1
    35a4:	00e59583          	lh	a1,14(a1)
    35a8:	c606                	sw	ra,12(sp)
    35aa:	2279                	jal	3738 <_read_r>
    35ac:	00054963          	bltz	a0,35be <__sread+0x20>
    35b0:	487c                	lw	a5,84(s0)
    35b2:	97aa                	add	a5,a5,a0
    35b4:	c87c                	sw	a5,84(s0)
    35b6:	40b2                	lw	ra,12(sp)
    35b8:	4422                	lw	s0,8(sp)
    35ba:	0141                	addi	sp,sp,16
    35bc:	8082                	ret
    35be:	245e                	lhu	a5,12(s0)
    35c0:	777d                	lui	a4,0xfffff
    35c2:	177d                	addi	a4,a4,-1
    35c4:	8ff9                	and	a5,a5,a4
    35c6:	a45e                	sh	a5,12(s0)
    35c8:	b7fd                	j	35b6 <__sread+0x18>

000035ca <__swrite>:
    35ca:	25de                	lhu	a5,12(a1)
    35cc:	1101                	addi	sp,sp,-32
    35ce:	cc22                	sw	s0,24(sp)
    35d0:	ca26                	sw	s1,20(sp)
    35d2:	c84a                	sw	s2,16(sp)
    35d4:	c64e                	sw	s3,12(sp)
    35d6:	ce06                	sw	ra,28(sp)
    35d8:	1007f793          	andi	a5,a5,256
    35dc:	84aa                	mv	s1,a0
    35de:	842e                	mv	s0,a1
    35e0:	8932                	mv	s2,a2
    35e2:	89b6                	mv	s3,a3
    35e4:	c791                	beqz	a5,35f0 <__swrite+0x26>
    35e6:	00e59583          	lh	a1,14(a1)
    35ea:	4689                	li	a3,2
    35ec:	4601                	li	a2,0
    35ee:	2209                	jal	36f0 <_lseek_r>
    35f0:	245e                	lhu	a5,12(s0)
    35f2:	777d                	lui	a4,0xfffff
    35f4:	177d                	addi	a4,a4,-1
    35f6:	8ff9                	and	a5,a5,a4
    35f8:	a45e                	sh	a5,12(s0)
    35fa:	00e41583          	lh	a1,14(s0)
    35fe:	4462                	lw	s0,24(sp)
    3600:	40f2                	lw	ra,28(sp)
    3602:	86ce                	mv	a3,s3
    3604:	864a                	mv	a2,s2
    3606:	49b2                	lw	s3,12(sp)
    3608:	4942                	lw	s2,16(sp)
    360a:	8526                	mv	a0,s1
    360c:	44d2                	lw	s1,20(sp)
    360e:	6105                	addi	sp,sp,32
    3610:	a825                	j	3648 <_write_r>

00003612 <__sseek>:
    3612:	1141                	addi	sp,sp,-16
    3614:	c422                	sw	s0,8(sp)
    3616:	842e                	mv	s0,a1
    3618:	00e59583          	lh	a1,14(a1)
    361c:	c606                	sw	ra,12(sp)
    361e:	28c9                	jal	36f0 <_lseek_r>
    3620:	57fd                	li	a5,-1
    3622:	245a                	lhu	a4,12(s0)
    3624:	00f51a63          	bne	a0,a5,3638 <__sseek+0x26>
    3628:	77fd                	lui	a5,0xfffff
    362a:	17fd                	addi	a5,a5,-1
    362c:	8ff9                	and	a5,a5,a4
    362e:	a45e                	sh	a5,12(s0)
    3630:	40b2                	lw	ra,12(sp)
    3632:	4422                	lw	s0,8(sp)
    3634:	0141                	addi	sp,sp,16
    3636:	8082                	ret
    3638:	6785                	lui	a5,0x1
    363a:	8fd9                	or	a5,a5,a4
    363c:	a45e                	sh	a5,12(s0)
    363e:	c868                	sw	a0,84(s0)
    3640:	bfc5                	j	3630 <__sseek+0x1e>

00003642 <__sclose>:
    3642:	00e59583          	lh	a1,14(a1)
    3646:	a805                	j	3676 <_close_r>

00003648 <_write_r>:
    3648:	1141                	addi	sp,sp,-16
    364a:	c422                	sw	s0,8(sp)
    364c:	842a                	mv	s0,a0
    364e:	852e                	mv	a0,a1
    3650:	85b2                	mv	a1,a2
    3652:	8636                	mv	a2,a3
    3654:	8801a023          	sw	zero,-1920(gp) # 20000130 <errno>
    3658:	c606                	sw	ra,12(sp)
    365a:	e0cfe0ef          	jal	ra,1c66 <_write>
    365e:	57fd                	li	a5,-1
    3660:	00f51763          	bne	a0,a5,366e <_write_r+0x26>
    3664:	88018793          	addi	a5,gp,-1920 # 20000130 <errno>
    3668:	439c                	lw	a5,0(a5)
    366a:	c391                	beqz	a5,366e <_write_r+0x26>
    366c:	c01c                	sw	a5,0(s0)
    366e:	40b2                	lw	ra,12(sp)
    3670:	4422                	lw	s0,8(sp)
    3672:	0141                	addi	sp,sp,16
    3674:	8082                	ret

00003676 <_close_r>:
    3676:	1141                	addi	sp,sp,-16
    3678:	c422                	sw	s0,8(sp)
    367a:	842a                	mv	s0,a0
    367c:	852e                	mv	a0,a1
    367e:	8801a023          	sw	zero,-1920(gp) # 20000130 <errno>
    3682:	c606                	sw	ra,12(sp)
    3684:	20c5                	jal	3764 <_close>
    3686:	57fd                	li	a5,-1
    3688:	00f51763          	bne	a0,a5,3696 <_close_r+0x20>
    368c:	88018793          	addi	a5,gp,-1920 # 20000130 <errno>
    3690:	439c                	lw	a5,0(a5)
    3692:	c391                	beqz	a5,3696 <_close_r+0x20>
    3694:	c01c                	sw	a5,0(s0)
    3696:	40b2                	lw	ra,12(sp)
    3698:	4422                	lw	s0,8(sp)
    369a:	0141                	addi	sp,sp,16
    369c:	8082                	ret

0000369e <_fstat_r>:
    369e:	1141                	addi	sp,sp,-16
    36a0:	c422                	sw	s0,8(sp)
    36a2:	842a                	mv	s0,a0
    36a4:	852e                	mv	a0,a1
    36a6:	85b2                	mv	a1,a2
    36a8:	8801a023          	sw	zero,-1920(gp) # 20000130 <errno>
    36ac:	c606                	sw	ra,12(sp)
    36ae:	20c9                	jal	3770 <_fstat>
    36b0:	57fd                	li	a5,-1
    36b2:	00f51763          	bne	a0,a5,36c0 <_fstat_r+0x22>
    36b6:	88018793          	addi	a5,gp,-1920 # 20000130 <errno>
    36ba:	439c                	lw	a5,0(a5)
    36bc:	c391                	beqz	a5,36c0 <_fstat_r+0x22>
    36be:	c01c                	sw	a5,0(s0)
    36c0:	40b2                	lw	ra,12(sp)
    36c2:	4422                	lw	s0,8(sp)
    36c4:	0141                	addi	sp,sp,16
    36c6:	8082                	ret

000036c8 <_isatty_r>:
    36c8:	1141                	addi	sp,sp,-16
    36ca:	c422                	sw	s0,8(sp)
    36cc:	842a                	mv	s0,a0
    36ce:	852e                	mv	a0,a1
    36d0:	8801a023          	sw	zero,-1920(gp) # 20000130 <errno>
    36d4:	c606                	sw	ra,12(sp)
    36d6:	205d                	jal	377c <_isatty>
    36d8:	57fd                	li	a5,-1
    36da:	00f51763          	bne	a0,a5,36e8 <_isatty_r+0x20>
    36de:	88018793          	addi	a5,gp,-1920 # 20000130 <errno>
    36e2:	439c                	lw	a5,0(a5)
    36e4:	c391                	beqz	a5,36e8 <_isatty_r+0x20>
    36e6:	c01c                	sw	a5,0(s0)
    36e8:	40b2                	lw	ra,12(sp)
    36ea:	4422                	lw	s0,8(sp)
    36ec:	0141                	addi	sp,sp,16
    36ee:	8082                	ret

000036f0 <_lseek_r>:
    36f0:	1141                	addi	sp,sp,-16
    36f2:	c422                	sw	s0,8(sp)
    36f4:	842a                	mv	s0,a0
    36f6:	852e                	mv	a0,a1
    36f8:	85b2                	mv	a1,a2
    36fa:	8636                	mv	a2,a3
    36fc:	8801a023          	sw	zero,-1920(gp) # 20000130 <errno>
    3700:	c606                	sw	ra,12(sp)
    3702:	2059                	jal	3788 <_lseek>
    3704:	57fd                	li	a5,-1
    3706:	00f51763          	bne	a0,a5,3714 <_lseek_r+0x24>
    370a:	88018793          	addi	a5,gp,-1920 # 20000130 <errno>
    370e:	439c                	lw	a5,0(a5)
    3710:	c391                	beqz	a5,3714 <_lseek_r+0x24>
    3712:	c01c                	sw	a5,0(s0)
    3714:	40b2                	lw	ra,12(sp)
    3716:	4422                	lw	s0,8(sp)
    3718:	0141                	addi	sp,sp,16
    371a:	8082                	ret

0000371c <memchr>:
    371c:	0ff5f593          	andi	a1,a1,255
    3720:	962a                	add	a2,a2,a0
    3722:	00c51463          	bne	a0,a2,372a <memchr+0xe>
    3726:	4501                	li	a0,0
    3728:	8082                	ret
    372a:	211c                	lbu	a5,0(a0)
    372c:	feb78ee3          	beq	a5,a1,3728 <memchr+0xc>
    3730:	0505                	addi	a0,a0,1
    3732:	bfc5                	j	3722 <memchr+0x6>

00003734 <__malloc_lock>:
    3734:	8082                	ret

00003736 <__malloc_unlock>:
    3736:	8082                	ret

00003738 <_read_r>:
    3738:	1141                	addi	sp,sp,-16
    373a:	c422                	sw	s0,8(sp)
    373c:	842a                	mv	s0,a0
    373e:	852e                	mv	a0,a1
    3740:	85b2                	mv	a1,a2
    3742:	8636                	mv	a2,a3
    3744:	8801a023          	sw	zero,-1920(gp) # 20000130 <errno>
    3748:	c606                	sw	ra,12(sp)
    374a:	20a9                	jal	3794 <_read>
    374c:	57fd                	li	a5,-1
    374e:	00f51763          	bne	a0,a5,375c <_read_r+0x24>
    3752:	88018793          	addi	a5,gp,-1920 # 20000130 <errno>
    3756:	439c                	lw	a5,0(a5)
    3758:	c391                	beqz	a5,375c <_read_r+0x24>
    375a:	c01c                	sw	a5,0(s0)
    375c:	40b2                	lw	ra,12(sp)
    375e:	4422                	lw	s0,8(sp)
    3760:	0141                	addi	sp,sp,16
    3762:	8082                	ret

00003764 <_close>:
    3764:	05800793          	li	a5,88
    3768:	88f1a023          	sw	a5,-1920(gp) # 20000130 <errno>
    376c:	557d                	li	a0,-1
    376e:	8082                	ret

00003770 <_fstat>:
    3770:	05800793          	li	a5,88
    3774:	88f1a023          	sw	a5,-1920(gp) # 20000130 <errno>
    3778:	557d                	li	a0,-1
    377a:	8082                	ret

0000377c <_isatty>:
    377c:	05800793          	li	a5,88
    3780:	88f1a023          	sw	a5,-1920(gp) # 20000130 <errno>
    3784:	4501                	li	a0,0
    3786:	8082                	ret

00003788 <_lseek>:
    3788:	05800793          	li	a5,88
    378c:	88f1a023          	sw	a5,-1920(gp) # 20000130 <errno>
    3790:	557d                	li	a0,-1
    3792:	8082                	ret

00003794 <_read>:
    3794:	05800793          	li	a5,88
    3798:	88f1a023          	sw	a5,-1920(gp) # 20000130 <errno>
    379c:	557d                	li	a0,-1
    379e:	8082                	ret
    37a0:	c2ce                	sw	s3,68(sp)
    37a2:	c8b6                	sw	a3,80(sp)
    37a4:	a8b1                	j	3800 <_read+0x6c>
    37a6:	afbe                	sh	a5,26(a5)
    37a8:	a5b4                	sb	a3,10(a1)
    37aa:	aca3a2b7          	lui	t0,0xaca3a
    37ae:	b1b5                	j	341a <_printf_i+0x148>
    37b0:	c2ceb0c7          	fmsub.d	ft1,ft9,fa2,fs8,rup
    37b4:	c8b6                	sw	a3,80(sp)
    37b6:	203a                	lhu	a4,2(s0)
    37b8:	2e25                	jal	3af0 <__sf_fake_stderr+0xc>
    37ba:	6632                	flw	fa2,12(sp)
    37bc:	e3a1                	bnez	a5,37fc <_read+0x68>
    37be:	00000a43          	fmadd.s	fs4,ft0,ft0,ft0,rne
    37c2:	0000                	unimp
    37c4:	0000                	unimp
    37c6:	4220                	lw	s0,64(a2)
    37c8:	bccaf5b3          	0xbccaf5b3
    37cc:	d0d6afbb          	0xd0d6afbb
    37d0:	2e2e                	lhu	a1,26(a2)
    37d2:	002e                	c.slli	zero,0xb
    37d4:	5344                	lw	s1,36(a4)
    37d6:	3831                	jal	2ff2 <_vfiprintf_r+0xb4>
    37d8:	3242                	lhu	s0,36(a2)
    37da:	b330                	sb	a2,3(a4)
    37dc:	caf5                	beqz	a3,38d0 <_read+0x13c>
    37de:	bbbc                	sb	a5,19(a5)
    37e0:	b0a7caaf          	0xb0a7caaf
    37e4:	a3dc                	sb	a5,4(a5)
    37e6:	c7ac                	sw	a1,72(a5)
    37e8:	b2ecbceb          	0xb2ecbceb
    37ec:	c1e9                	beqz	a1,38ae <_read+0x11a>
    37ee:	bdac                	sb	a1,27(a1)
    37f0:	000000d3          	fadd.s	ft1,ft0,ft0,rne
    37f4:	b3cdb5cf          	fnmadd.d	fa1,fs11,ft8,fs6,rup
    37f8:	bccaf5b3          	0xbccaf5b3
    37fc:	eacdafbb          	0xeacdafbb
    3800:	aca3c9b3          	0xaca3c9b3
    3804:	ddc5e5b3          	0xddc5e5b3
    3808:	c6d6d8bf b3cdb5cf 	0xb3cdb5cfc6d6d8bf
    3810:	d1d2                	sw	s4,224(sp)
    3812:	f4c6                	fsw	fa7,104(sp)
    3814:	afb6                	sh	a3,26(a5)
    3816:	0000                	unimp
    3818:	0e76                	slli	t3,t3,0x1d
    381a:	0000                	unimp
    381c:	0e9c                	addi	a5,sp,848
    381e:	0000                	unimp
    3820:	0ec2                	slli	t4,t4,0x10
    3822:	0000                	unimp
    3824:	0e9c                	addi	a5,sp,848
    3826:	0000                	unimp
    3828:	0e9c                	addi	a5,sp,848
    382a:	0000                	unimp
    382c:	0f28                	addi	a0,sp,920
    382e:	0000                	unimp
    3830:	0f38                	addi	a4,sp,920
    3832:	0000                	unimp
    3834:	0f5c                	addi	a5,sp,916
    3836:	0000                	unimp
    3838:	bccaaabf aecbd3bc 	0xaecbd3bcbccaaabf
    3840:	aecbaca3          	sw	a2,-1287(s7)
    3844:	c3b1                	beqz	a5,3888 <_read+0xf4>
    3846:	c631                	beqz	a2,3892 <_read+0xfe>
    3848:	b6f4                	sb	a3,15(a3)
    384a:	aabf00af          	0xaabf00af
    384e:	bcca                	sh	a0,60(s1)
    3850:	d3bc                	sw	a5,96(a5)
    3852:	c8c8                	sw	a0,20(s1)
    3854:	f5b3aca3          	sw	s11,-167(t2)
    3858:	bcca                	sh	a0,60(s1)
    385a:	c2ce                	sw	s3,68(sp)
    385c:	c8b6                	sw	a3,80(sp)
    385e:	203a                	lhu	a4,2(s0)
    3860:	2e25                	jal	3b98 <_data_lma+0x18>
    3862:	6632                	flw	fa2,12(sp)
    3864:	e3a1                	bnez	a5,38a4 <_read+0x110>
    3866:	00000a43          	fmadd.s	fs4,ft0,ft0,ft0,rne
    386a:	0000                	unimp
    386c:	bccaaabf e8b0c1bd 	0xe8b0c1bdbccaaabf
    3874:	0000                	unimp
    3876:	0000                	unimp
    3878:	c1bd                	beqz	a1,38de <_read+0x14a>
    387a:	e8b0                	fsw	fa2,80(s1)
    387c:	d1d2                	sw	s4,224(sp)
    387e:	a3cd                	j	3e60 <_data_lma+0x2e0>
    3880:	b9d6                	sh	a3,52(a1)
    3882:	0000                	unimp
    3884:	ddc5e5b3          	0xddc5e5b3
    3888:	f7c1                	bnez	a5,3810 <_read+0x7c>
    388a:	aabfccb3          	0xaabfccb3
    388e:	bcca                	sh	a0,60(s1)
    3890:	c8b5aca3          	sw	a1,-871(a1)
    3894:	fdb4                	fsw	fa3,120(a1)
    3896:	b4b0                	sb	a2,11(s1)
    3898:	fcbc                	fsw	fa5,120(s1)
    389a:	c831                	beqz	s0,38ee <_read+0x15a>
    389c:	bccfc8b7          	lui	a7,0xbccfc
    38a0:	00aecbd3          	fadd.s	fs7,ft9,fa0,rmm
    38a4:	0000                	unimp
    38a6:	4000                	lw	s0,0(s0)
    38a8:	d3bc                	sw	a5,96(a5)
    38aa:	eacdaecb          	fnmsub.d	ft9,fs11,fa2,ft9,rdn
    38ae:	aca3c9b3          	0xaca3c9b3
    38b2:	b1cac3d3          	0xb1cac3d3
    38b6:	203a                	lhu	a4,2(s0)
    38b8:	6c25                	lui	s8,0x9
    38ba:	2075                	jal	3966 <_read+0x1d2>
    38bc:	736d                	lui	t1,0xffffb
    38be:	c8b5aca3          	sw	a1,-871(a1)
    38c2:	fdb4                	fsw	fa3,120(a1)
    38c4:	b4b0                	sb	a2,11(s1)
    38c6:	fcbc                	fsw	fa5,120(s1)
    38c8:	c832                	sw	a2,16(sp)
    38ca:	bccfc8b7          	lui	a7,0xbccfc
    38ce:	0ac8c8d3          	fsub.d	fa7,fa7,fa2,rmm
    38d2:	0000                	unimp
    38d4:	d3bc                	sw	a5,96(a5)
    38d6:	acb3aecb          	0xacb3aecb
    38da:	b1ca                	sh	a0,36(a1)
    38dc:	b5cfaca3          	sw	t3,-1191(t6)
    38e0:	b3cd                	j	36c2 <_fstat_r+0x24>
    38e2:	b4b8                	sb	a4,11(s1)
    38e4:	bbce                	sh	a1,52(a5)
    38e6:	0000                	unimp
    38e8:	d3bc                	sw	a5,96(a5)
    38ea:	c8c8                	sw	a0,20(s1)
    38ec:	eacd                	bnez	a3,399e <_read+0x20a>
    38ee:	aca3c9b3          	0xaca3c9b3
    38f2:	c2ce                	sw	s3,68(sp)
    38f4:	c8b6                	sw	a3,80(sp)
    38f6:	cfc9                	beqz	a5,3990 <_read+0x1fc>
    38f8:	fdc9                	bnez	a1,3892 <_read+0xfe>
    38fa:	203a                	lhu	a4,2(s0)
    38fc:	2e25                	jal	3c34 <_data_lma+0xb4>
    38fe:	6632                	flw	fa2,12(sp)
    3900:	e3a1                	bnez	a5,3940 <_read+0x1ac>
    3902:	b5aca343          	0xb5aca343
    3906:	b4c8                	sb	a0,13(s1)
    3908:	b0fd                	j	31f6 <_printf_common+0x30>
    390a:	bcb4                	sb	a3,27(s1)
    390c:	33fc                	lbu	a5,7(a5)
    390e:	b7c8                	sb	a0,13(a5)
    3910:	cfc8                	sw	a0,28(a5)
    3912:	c1bd                	beqz	a1,3978 <_read+0x1e4>
    3914:	e8b0                	fsw	fa2,80(s1)
    3916:	000a                	c.slli	zero,0x2
    3918:	d3bc                	sw	a5,96(a5)
    391a:	c8c8                	sw	a0,20(s1)
    391c:	b1caacb3          	0xb1caacb3
    3920:	b5cfaca3          	sw	t3,-1191(t6)
    3924:	b3cd                	j	3706 <_lseek_r+0x16>
    3926:	b4b8                	sb	a4,11(s1)
    3928:	bbce                	sh	a1,52(a5)
    392a:	0000                	unimp
    392c:	bccaaabf c3d6b2be 	0xc3d6b2bebccaaabf
    3934:	ebc33033          	0xebc33033
    3938:	0000                	unimp
    393a:	0000                	unimp
    393c:	b2be                	sh	a5,34(a3)
    393e:	c3d6                	sw	s5,196(sp)
    3940:	eacd                	bnez	a3,39f2 <__clz_tab+0xe>
    3942:	aca3c9b3          	0xaca3c9b3
    3946:	bccaaabf cbc2fdb9 	0xcbc2fdb9bccaaabf
    394e:	aecbaca3          	sw	a2,-1287(s7)
    3952:	c3b1                	beqz	a5,3996 <_read+0x202>
    3954:	c632                	sw	a2,12(sp)
    3956:	b6f4                	sb	a3,15(a3)
    3958:	000000af          	0xaf
    395c:	fdb9                	bnez	a1,38ba <_read+0x126>
    395e:	cbc2                	sw	a6,212(sp)
    3960:	eacd                	bnez	a3,3a12 <__clz_tab+0x2e>
    3962:	aca3c9b3          	0xaca3c9b3
    3966:	ddc5e5b3          	0xddc5e5b3
    396a:	f7c1                	bnez	a5,38f2 <_read+0x15e>
    396c:	e1bdccb3          	0xe1bdccb3
    3970:	f8ca                	fsw	fs2,112(sp)
    3972:	0000                	unimp
    3974:	5344                	lw	s1,36(a4)
    3976:	3831                	jal	3192 <_vfiprintf_r+0x254>
    3978:	3242                	lhu	s0,36(a2)
    397a:	b330                	sb	a2,3(a4)
    397c:	caf5                	beqz	a3,3a70 <__clz_tab+0x8c>
    397e:	bbbc                	sb	a5,19(a5)
    3980:	b0a7caaf          	0xb0a7caaf
    3984:	a3dc                	sb	a5,4(a5)
    3986:	c3ac                	sw	a1,64(a5)
    3988:	bcd0d3bb          	0xbcd0d3bb
    398c:	b2ec                	sb	a1,7(a3)
    398e:	b5e2                	sh	s0,46(a1)
    3990:	c9bd                	beqz	a1,3a06 <__clz_tab+0x22>
    3992:	b1e8                	sb	a0,7(a1)
    3994:	00b8                	addi	a4,sp,72
    3996:	0000                	unimp
    3998:	0000                	unimp
    399a:	c2c8                	sw	a0,4(a3)
    399c:	0000                	unimp
    399e:	c47a                	sw	t5,8(sp)
    39a0:	0000                	unimp
    39a2:	3d80                	lbu	s0,25(a1)
    39a4:	e5b8                	fsw	fa4,72(a1)
    39a6:	ffff                	0xffff
    39a8:	e50a                	fsw	ft2,136(sp)
    39aa:	ffff                	0xffff
    39ac:	e50a                	fsw	ft2,136(sp)
    39ae:	ffff                	0xffff
    39b0:	e508                	fsw	fa0,8(a0)
    39b2:	ffff                	0xffff
    39b4:	e50e                	fsw	ft3,136(sp)
    39b6:	ffff                	0xffff
    39b8:	e50e                	fsw	ft3,136(sp)
    39ba:	ffff                	0xffff
    39bc:	e4de                	fsw	fs7,72(sp)
    39be:	ffff                	0xffff
    39c0:	e508                	fsw	fa0,8(a0)
    39c2:	ffff                	0xffff
    39c4:	e50e                	fsw	ft3,136(sp)
    39c6:	ffff                	0xffff
    39c8:	e4de                	fsw	fs7,72(sp)
    39ca:	ffff                	0xffff
    39cc:	e50e                	fsw	ft3,136(sp)
    39ce:	ffff                	0xffff
    39d0:	e508                	fsw	fa0,8(a0)
    39d2:	ffff                	0xffff
    39d4:	e5a6                	fsw	fs1,200(sp)
    39d6:	ffff                	0xffff
    39d8:	e5a6                	fsw	fs1,200(sp)
    39da:	ffff                	0xffff
    39dc:	e5a6                	fsw	fs1,200(sp)
    39de:	ffff                	0xffff
    39e0:	e4de                	fsw	fs7,72(sp)
    39e2:	ffff                	0xffff

000039e4 <__clz_tab>:
    39e4:	0100 0202 0303 0303 0404 0404 0404 0404     ................
    39f4:	0505 0505 0505 0505 0505 0505 0505 0505     ................
    3a04:	0606 0606 0606 0606 0606 0606 0606 0606     ................
    3a14:	0606 0606 0606 0606 0606 0606 0606 0606     ................
    3a24:	0707 0707 0707 0707 0707 0707 0707 0707     ................
    3a34:	0707 0707 0707 0707 0707 0707 0707 0707     ................
    3a44:	0707 0707 0707 0707 0707 0707 0707 0707     ................
    3a54:	0707 0707 0707 0707 0707 0707 0707 0707     ................
    3a64:	0808 0808 0808 0808 0808 0808 0808 0808     ................
    3a74:	0808 0808 0808 0808 0808 0808 0808 0808     ................
    3a84:	0808 0808 0808 0808 0808 0808 0808 0808     ................
    3a94:	0808 0808 0808 0808 0808 0808 0808 0808     ................
    3aa4:	0808 0808 0808 0808 0808 0808 0808 0808     ................
    3ab4:	0808 0808 0808 0808 0808 0808 0808 0808     ................
    3ac4:	0808 0808 0808 0808 0808 0808 0808 0808     ................
    3ad4:	0808 0808 0808 0808 0808 0808 0808 0808     ................

00003ae4 <__sf_fake_stderr>:
	...

00003b04 <__sf_fake_stdin>:
	...

00003b24 <__sf_fake_stdout>:
	...
    3b44:	2d23 2b30 0020 0000 6c68 004c 6665 4567     #-0+ ...hlL.efgE
    3b54:	4746 0000 3130 3332 3534 3736 3938 4241     FG..0123456789AB
    3b64:	4443 4645 0000 0000 3130 3332 3534 3736     CDEF....01234567
    3b74:	3938 6261 6463 6665 0000 0000               89abcdef....
