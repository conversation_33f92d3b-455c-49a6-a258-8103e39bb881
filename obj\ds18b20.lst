
ds18b20.elf:     file format elf32-littleriscv
ds18b20.elf
architecture: riscv:rv32, flags 0x00000112:
EXEC_P, HAS_SYMS, D_PAGED
start address 0x00000000

Program Header:
    LOAD off    0x00001000 vaddr 0x00000000 paddr 0x00000000 align 2**12
         filesz 0x0000135c memsz 0x0000135c flags r-x
    LOAD off    0x00003000 vaddr 0x20000000 paddr 0x0000135c align 2**12
         filesz 0x00000050 memsz 0x00000084 flags rw-
    LOAD off    0x00003800 vaddr 0x20007800 paddr 0x20007800 align 2**12
         filesz 0x00000000 memsz 0x00000800 flags rw-

Sections:
Idx Name          Size      VMA       LMA       File off  Algn
  0 .init         00000004  00000000  00000000  00001000  2**1
                  CONTENTS, ALLOC, LOAD, READONLY, CODE
  1 .vector       000001bc  00000004  00000004  00001004  2**1
                  CONTENTS, ALLOC, LOAD, READONLY, CODE
  2 .text         0000119c  000001c0  000001c0  000011c0  2**1
                  CONTENTS, ALLOC, LOAD, READONLY, CODE
  3 .fini         00000000  0000135c  0000135c  00003050  2**0
                  CONTENTS, ALLOC, LOAD, CODE
  4 .dalign       00000000  20000000  20000000  00003050  2**0
                  CONTENTS
  5 .dlalign      00000000  0000135c  0000135c  00003050  2**0
                  CONTENTS
  6 .data         00000050  20000000  0000135c  00003000  2**2
                  CONTENTS, ALLOC, LOAD, DATA
  7 .bss          00000034  20000050  000013ac  00003050  2**2
                  ALLOC
  8 .stack        00000800  20007800  20007800  00003800  2**0
                  ALLOC
  9 .debug_info   0001106a  00000000  00000000  00003050  2**0
                  CONTENTS, READONLY, DEBUGGING
 10 .debug_abbrev 000029f5  00000000  00000000  000140ba  2**0
                  CONTENTS, READONLY, DEBUGGING
 11 .debug_aranges 000009a0  00000000  00000000  00016ab0  2**3
                  CONTENTS, READONLY, DEBUGGING
 12 .debug_ranges 00000a00  00000000  00000000  00017450  2**3
                  CONTENTS, READONLY, DEBUGGING
 13 .debug_line   0000c17c  00000000  00000000  00017e50  2**0
                  CONTENTS, READONLY, DEBUGGING
 14 .debug_str    000030cb  00000000  00000000  00023fcc  2**0
                  CONTENTS, READONLY, DEBUGGING
 15 .comment      00000033  00000000  00000000  00027097  2**0
                  CONTENTS, READONLY
 16 .debug_frame  000016e0  00000000  00000000  000270cc  2**2
                  CONTENTS, READONLY, DEBUGGING
 17 .debug_loc    000054bc  00000000  00000000  000287ac  2**0
                  CONTENTS, READONLY, DEBUGGING
 18 .stabstr      00000117  00000000  00000000  0002dc68  2**0
                  CONTENTS, READONLY, DEBUGGING
SYMBOL TABLE:
00000000 l    d  .init	00000000 .init
00000004 l    d  .vector	00000000 .vector
000001c0 l    d  .text	00000000 .text
0000135c l    d  .fini	00000000 .fini
20000000 l    d  .dalign	00000000 .dalign
0000135c l    d  .dlalign	00000000 .dlalign
20000000 l    d  .data	00000000 .data
20000050 l    d  .bss	00000000 .bss
20007800 l    d  .stack	00000000 .stack
00000000 l    d  .debug_info	00000000 .debug_info
00000000 l    d  .debug_abbrev	00000000 .debug_abbrev
00000000 l    d  .debug_aranges	00000000 .debug_aranges
00000000 l    d  .debug_ranges	00000000 .debug_ranges
00000000 l    d  .debug_line	00000000 .debug_line
00000000 l    d  .debug_str	00000000 .debug_str
00000000 l    d  .comment	00000000 .comment
00000000 l    d  .debug_frame	00000000 .debug_frame
00000000 l    d  .debug_loc	00000000 .debug_loc
00000000 l    d  .stabstr	00000000 .stabstr
00000000 l    df *ABS*	00000000 ./Startup/startup_ch32v30x_D8C.o
00000004 l       .vector	00000000 _vector_base
00000000 l    df *ABS*	00000000 ch32v30x_it.c
00000000 l    df *ABS*	00000000 main.c
00000000 l    df *ABS*	00000000 system_ch32v30x.c
00000000 l    df *ABS*	00000000 ch32v30x_gpio.c
00000000 l    df *ABS*	00000000 ch32v30x_misc.c
00000000 l    df *ABS*	00000000 ch32v30x_rcc.c
20000034 l     O .data	00000010 APBAHBPrescTable
2000004c l     O .data	00000004 ADCPrescTable
00000000 l    df *ABS*	00000000 ch32v30x_spi.c
00000000 l    df *ABS*	00000000 ch32v30x_tim.c
00000000 l    df *ABS*	00000000 ch32v30x_usart.c
00000000 l    df *ABS*	00000000 key.c
00000000 l    df *ABS*	00000000 lcd.c
00000000 l    df *ABS*	00000000 water_pump.c
00000000 l    df *ABS*	00000000 debug.c
20000060 l     O .bss	00000002 p_ms
00000000 l    df *ABS*	00000000 close.c
000005b8  w      .text	00000000 EXTI2_IRQHandler
000005b8  w      .text	00000000 TIM8_TRG_COM_IRQHandler
000005b8  w      .text	00000000 TIM8_CC_IRQHandler
000005b8  w      .text	00000000 UART8_IRQHandler
20000848 g       .data	00000000 __global_pointer$
000001c8 g     F .text	00000028 .hidden __riscv_save_8
000005b8  w      .text	00000000 TIM1_CC_IRQHandler
00000974 g     F .text	00000004 SPI_I2S_SendData
00000222 g     F .text	00000010 HardFault_Handler
20000054 g     O .bss	00000001 p
00000b22 g     F .text	00000082 key_init
00000234 g     F .text	00000032 key_proc
20000000 g     O .data	00000024 scheduler_task_t
00000214 g     F .text	0000000c .hidden __riscv_restore_3
000005b8  w      .text	00000000 TIM6_IRQHandler
000005b8  w      .text	00000000 SysTick_Handler
0000071c g     F .text	0000004e NVIC_Init
000005b8  w      .text	00000000 PVD_IRQHandler
000005b8  w      .text	00000000 SDIO_IRQHandler
000005b8  w      .text	00000000 TIM9_BRK_IRQHandler
00000200 g     F .text	00000020 .hidden __riscv_restore_10
00000978 g     F .text	00000004 SPI_I2S_ReceiveData
000005b8  w      .text	00000000 DMA2_Channel8_IRQHandler
00000220 g     F .text	00000002 NMI_Handler
000005b8  w      .text	00000000 CAN2_RX1_IRQHandler
000005b8  w      .text	00000000 EXTI3_IRQHandler
000001c8 g     F .text	00000028 .hidden __riscv_save_11
00000e3c g     F .text	0000005a spi_readwrite
000005b8  w      .text	00000000 USBHS_IRQHandler
000005b8  w      .text	00000000 DMA2_Channel9_IRQHandler
000005b8  w      .text	00000000 TIM10_CC_IRQHandler
20000050 g       .bss	00000000 _sbss
00000800 g       *ABS*	00000000 __stack_size
00000efe g     F .text	00000030 LCD_WR_REG
00001300 g     F .text	0000005a USART_Printf_Init
000005b8  w      .text	00000000 USBFS_IRQHandler
00000214 g     F .text	0000000c .hidden __riscv_restore_2
000011f0 g     F .text	00000082 WaterPump_Init
000005b8  w      .text	00000000 EXTI0_IRQHandler
000005b8  w      .text	00000000 I2C2_EV_IRQHandler
000005b8  w      .text	00000000 TIM10_TRG_COM_IRQHandler
00000a30 g     F .text	00000018 TIM_Cmd
20000048 g     O .data	00000004 SystemCoreClock
00000004 g       .init	00000000 _einit
00000a72 g     F .text	0000000c TIM_ClearITPendingBit
000008e0 g     F .text	0000001e RCC_APB2PeriphClockCmd
000001c0 g     F .text	00000030 .hidden __riscv_save_12
000005b8  w      .text	00000000 CAN2_SCE_IRQHandler
000005b8  w      .text	00000000 ADC1_2_IRQHandler
00000644 g     F .text	000000c0 GPIO_Init
000005b8  w      .text	00000000 Break_Point_Handler
00000200 g     F .text	00000020 .hidden __riscv_restore_11
2000005c g     O .bss	00000004 NVIC_Priority_Group
000005b8  w      .text	00000000 SPI1_IRQHandler
00000b0c g     F .text	00000016 USART_Cmd
000005b8  w      .text	00000000 TAMPER_IRQHandler
000001f0 g     F .text	0000000c .hidden __riscv_save_1
00000214 g     F .text	0000000c .hidden __riscv_restore_0
000001d6 g     F .text	0000001a .hidden __riscv_save_7
000005b8  w      .text	00000000 CAN2_RX0_IRQHandler
000005b8  w      .text	00000000 TIM8_UP_IRQHandler
000008fe g     F .text	0000001e RCC_APB1PeriphClockCmd
000005b8  w      .text	00000000 Ecall_M_Mode_Handler
20007800 g       .stack	00000000 _heap_end
0000020a g     F .text	00000016 .hidden __riscv_restore_5
0000097c g     F .text	0000000a SPI_I2S_GetFlagStatus
000005b8  w      .text	00000000 DMA2_Channel2_IRQHandler
000005b8  w      .text	00000000 DMA1_Channel4_IRQHandler
000005b8  w      .text	00000000 TIM9_UP_IRQHandler
0000020a g     F .text	00000016 .hidden __riscv_restore_6
000005b8  w      .text	00000000 USART3_IRQHandler
20000053 g     O .bss	00000001 key_val
000005b8  w      .text	00000000 RTC_IRQHandler
20000084 g       .bss	00000000 _ebss
000005b8  w      .text	00000000 DMA1_Channel7_IRQHandler
000005b8  w      .text	00000000 CAN1_RX1_IRQHandler
000005b8  w      .text	00000000 DVP_IRQHandler
000005b8  w      .text	00000000 UART5_IRQHandler
00000ba4 g     F .text	000001c0 key_read
20000051 g     O .bss	00000001 key_old
00000ec6 g     F .text	00000038 LCD_WR_DATA
00000d64 g     F .text	000000d4 SPI_LCD_Init
0000070e g     F .text	00000004 GPIO_SetBits
000005b8  w      .text	00000000 TIM4_IRQHandler
000001c8 g     F .text	00000028 .hidden __riscv_save_9
00001272 g     F .text	00000058 WaterPump_Control
000005b8  w      .text	00000000 DMA2_Channel1_IRQHandler
000001d6 g     F .text	0000001a .hidden __riscv_save_4
000005b8  w      .text	00000000 I2C1_EV_IRQHandler
00000a5a g     F .text	00000018 TIM_GetITStatus
0000076a g     F .text	00000176 RCC_GetClocksFreq
000005b8  w      .text	00000000 DMA1_Channel6_IRQHandler
000005b8  w      .text	00000000 UART4_IRQHandler
000005b8  w      .text	00000000 DMA2_Channel4_IRQHandler
00000a7e g     F .text	0000008e USART_Init
000002be g     F .text	0000002c TIM3_IRQHandler
000005b8  w      .text	00000000 RCC_IRQHandler
000001f0 g     F .text	0000000c .hidden __riscv_save_3
000005b8  w      .text	00000000 TIM1_TRG_COM_IRQHandler
000005b8  w      .text	00000000 DMA1_Channel1_IRQHandler
00000000 g       .init	00000000 _start
000005b8  w      .text	00000000 DMA2_Channel7_IRQHandler
20000024 g     O .data	00000010 AHBPrescTable
000002ea g     F .text	0000003c scheduler_run
000005b8  w      .text	00000000 EXTI15_10_IRQHandler
00000a48 g     F .text	00000012 TIM_ITConfig
00000712 g     F .text	00000004 GPIO_ResetBits
000005b8  w      .text	00000000 TIM7_IRQHandler
000005b8  w      .text	00000000 CAN2_TX_IRQHandler
20000000 g       .dalign	00000000 _data_vma
000005b8  w      .text	00000000 TIM5_IRQHandler
20000055 g     O .bss	00000001 task_num
000005b8  w      .text	00000000 EXTI9_5_IRQHandler
000001c8 g     F .text	00000028 .hidden __riscv_save_10
000011c6 g     F .text	00000010 delay_us
000005b8  w      .text	00000000 ETH_WKUP_IRQHandler
0000020a g     F .text	00000016 .hidden __riscv_restore_4
00000200 g     F .text	00000020 .hidden __riscv_restore_8
000001d6 g     F .text	0000001a .hidden __riscv_save_6
000005b8  w      .text	00000000 SPI2_IRQHandler
00000e96 g     F .text	00000030 LCD_WR_DATA8
00000f82 g     F .text	00000206 LCD_Init
00000200 g     F .text	00000020 .hidden __riscv_restore_9
0000020a g     F .text	00000016 .hidden __riscv_restore_7
00000326 g     F .text	0000006a main
000011d6 g     F .text	0000001a delay_ms
000005b8  w      .text	00000000 TIM10_BRK_IRQHandler
000005b8  w      .text	00000000 TIM9_CC_IRQHandler
000005b8  w      .text	00000000 DMA2_Channel5_IRQHandler
20000064 g     O .bss	00000020 g_pump_ctrl
000005b8  w      .text	00000000 DMA1_Channel5_IRQHandler
000005b8  w      .text	00000000 EXTI4_IRQHandler
000012ca g     F .text	00000036 Delay_Ms
000005b8  w      .text	00000000 USB_LP_CAN1_RX0_IRQHandler
0000095a g     F .text	0000001a SPI_Cmd
00000266 g     F .text	00000002 lock_proc
00000390 g     F .text	000000fa SystemInit
000005b8  w      .text	00000000 RNG_IRQHandler
00001188 g     F .text	0000003e LCD_Fill
000005b8  w      .text	00000000 USB_HP_CAN1_TX_IRQHandler
00000000 g       .init	00000000 _sinit
000005b8  w      .text	00000000 DMA1_Channel3_IRQHandler
00000f2e g     F .text	00000054 LCD_Address_Set
00000268 g     F .text	00000056 Tim3_Init
000005b8  w      .text	00000000 ETH_IRQHandler
000005b8  w      .text	00000000 TIM1_UP_IRQHandler
00000232 g     F .text	00000002 lcd_proc
000005b8  w      .text	00000000 WWDG_IRQHandler
000005b8  w      .text	00000000 USBHSWakeup_IRQHandler
000005b8  w      .text	00000000 DMA2_Channel11_IRQHandler
000005b8  w      .text	00000000 Ecall_U_Mode_Handler
000005b8  w      .text	00000000 DMA2_Channel6_IRQHandler
000005b8  w      .text	00000000 TIM2_IRQHandler
20008000 g       .stack	00000000 _eusrstack
000001f0 g     F .text	0000000c .hidden __riscv_save_2
000005b8  w      .text	00000000 SW_Handler
000005b8  w      .text	00000000 TIM1_BRK_IRQHandler
000005b8  w      .text	00000000 DMA2_Channel10_IRQHandler
000005b8  w      .text	00000000 EXTI1_IRQHandler
20000050 g     O .bss	00000001 key_down
000001d6 g     F .text	0000001a .hidden __riscv_save_5
20000052 g     O .bss	00000001 key_up
20000050 g       .data	00000000 _edata
20000084 g       .bss	00000000 _end
00000986 g     F .text	000000aa TIM_TimeBaseInit
000005b8  w      .text	00000000 RTCAlarm_IRQHandler
0000135c g       .dlalign	00000000 _data_lma
000005b8  w      .text	00000000 TIM10_UP_IRQHandler
000005b8  w      .text	00000000 TIM9_TRG_COM_IRQHandler
000005b8  w      .text	00000000 UART7_IRQHandler
000005b8  w      .text	00000000 USART2_IRQHandler
000005b8  w      .text	00000000 UART6_IRQHandler
0000048a g     F .text	0000012e SystemCoreClockUpdate
000005b8  w      .text	00000000 I2C2_ER_IRQHandler
000005b8  w      .text	00000000 DMA1_Channel2_IRQHandler
000001fc g     F .text	00000024 .hidden __riscv_restore_12
000005b8  w      .text	00000000 TIM8_BRK_IRQHandler
000005ba  w      .text	00000000 handle_reset
000005b8  w      .text	00000000 CAN1_SCE_IRQHandler
000005b8  w      .text	00000000 FLASH_IRQHandler
000001f0 g     F .text	0000000c .hidden __riscv_save_0
000005b8  w      .text	00000000 USART1_IRQHandler
00000e38 g     F .text	00000004 SPI3_IRQHandler
000005b8  w      .text	00000000 I2C1_ER_IRQHandler
0000091c g     F .text	0000003e SPI_Init
00000716 g     F .text	00000006 NVIC_PriorityGroupConfig
00000214 g     F .text	0000000c .hidden __riscv_restore_1
00000704 g     F .text	0000000a GPIO_ReadInputDataBit
20000058 g     O .bss	00000004 uwtick
000005b8  w      .text	00000000 USBWakeUp_IRQHandler
000005b8  w      .text	00000000 DMA2_Channel3_IRQHandler



Disassembly of section .init:

00000000 <_sinit>:
   0:	5ba0006f          	j	5ba <handle_reset>

Disassembly of section .vector:

00000004 <_vector_base>:
	...
   c:	0220                	addi	s0,sp,264
   e:	0000                	unimp
  10:	0222                	slli	tp,tp,0x8
  12:	0000                	unimp
  14:	0000                	unimp
  16:	0000                	unimp
  18:	05b8                	addi	a4,sp,712
	...
  22:	0000                	unimp
  24:	05b8                	addi	a4,sp,712
  26:	0000                	unimp
  28:	05b8                	addi	a4,sp,712
	...
  32:	0000                	unimp
  34:	05b8                	addi	a4,sp,712
  36:	0000                	unimp
  38:	0000                	unimp
  3a:	0000                	unimp
  3c:	05b8                	addi	a4,sp,712
  3e:	0000                	unimp
  40:	0000                	unimp
  42:	0000                	unimp
  44:	05b8                	addi	a4,sp,712
  46:	0000                	unimp
  48:	05b8                	addi	a4,sp,712
  4a:	0000                	unimp
  4c:	05b8                	addi	a4,sp,712
  4e:	0000                	unimp
  50:	05b8                	addi	a4,sp,712
  52:	0000                	unimp
  54:	05b8                	addi	a4,sp,712
  56:	0000                	unimp
  58:	05b8                	addi	a4,sp,712
  5a:	0000                	unimp
  5c:	05b8                	addi	a4,sp,712
  5e:	0000                	unimp
  60:	05b8                	addi	a4,sp,712
  62:	0000                	unimp
  64:	05b8                	addi	a4,sp,712
  66:	0000                	unimp
  68:	05b8                	addi	a4,sp,712
  6a:	0000                	unimp
  6c:	05b8                	addi	a4,sp,712
  6e:	0000                	unimp
  70:	05b8                	addi	a4,sp,712
  72:	0000                	unimp
  74:	05b8                	addi	a4,sp,712
  76:	0000                	unimp
  78:	05b8                	addi	a4,sp,712
  7a:	0000                	unimp
  7c:	05b8                	addi	a4,sp,712
  7e:	0000                	unimp
  80:	05b8                	addi	a4,sp,712
  82:	0000                	unimp
  84:	05b8                	addi	a4,sp,712
  86:	0000                	unimp
  88:	05b8                	addi	a4,sp,712
  8a:	0000                	unimp
  8c:	05b8                	addi	a4,sp,712
  8e:	0000                	unimp
  90:	05b8                	addi	a4,sp,712
  92:	0000                	unimp
  94:	05b8                	addi	a4,sp,712
  96:	0000                	unimp
  98:	05b8                	addi	a4,sp,712
  9a:	0000                	unimp
  9c:	05b8                	addi	a4,sp,712
  9e:	0000                	unimp
  a0:	05b8                	addi	a4,sp,712
  a2:	0000                	unimp
  a4:	05b8                	addi	a4,sp,712
  a6:	0000                	unimp
  a8:	05b8                	addi	a4,sp,712
  aa:	0000                	unimp
  ac:	05b8                	addi	a4,sp,712
  ae:	0000                	unimp
  b0:	05b8                	addi	a4,sp,712
  b2:	0000                	unimp
  b4:	05b8                	addi	a4,sp,712
  b6:	0000                	unimp
  b8:	02be                	slli	t0,t0,0xf
  ba:	0000                	unimp
  bc:	05b8                	addi	a4,sp,712
  be:	0000                	unimp
  c0:	05b8                	addi	a4,sp,712
  c2:	0000                	unimp
  c4:	05b8                	addi	a4,sp,712
  c6:	0000                	unimp
  c8:	05b8                	addi	a4,sp,712
  ca:	0000                	unimp
  cc:	05b8                	addi	a4,sp,712
  ce:	0000                	unimp
  d0:	05b8                	addi	a4,sp,712
  d2:	0000                	unimp
  d4:	05b8                	addi	a4,sp,712
  d6:	0000                	unimp
  d8:	05b8                	addi	a4,sp,712
  da:	0000                	unimp
  dc:	05b8                	addi	a4,sp,712
  de:	0000                	unimp
  e0:	05b8                	addi	a4,sp,712
  e2:	0000                	unimp
  e4:	05b8                	addi	a4,sp,712
  e6:	0000                	unimp
  e8:	05b8                	addi	a4,sp,712
  ea:	0000                	unimp
  ec:	05b8                	addi	a4,sp,712
  ee:	0000                	unimp
  f0:	05b8                	addi	a4,sp,712
  f2:	0000                	unimp
  f4:	05b8                	addi	a4,sp,712
  f6:	0000                	unimp
  f8:	05b8                	addi	a4,sp,712
  fa:	0000                	unimp
  fc:	05b8                	addi	a4,sp,712
  fe:	0000                	unimp
 100:	05b8                	addi	a4,sp,712
 102:	0000                	unimp
 104:	0000                	unimp
 106:	0000                	unimp
 108:	05b8                	addi	a4,sp,712
 10a:	0000                	unimp
 10c:	05b8                	addi	a4,sp,712
 10e:	0000                	unimp
 110:	0e38                	addi	a4,sp,792
 112:	0000                	unimp
 114:	05b8                	addi	a4,sp,712
 116:	0000                	unimp
 118:	05b8                	addi	a4,sp,712
 11a:	0000                	unimp
 11c:	05b8                	addi	a4,sp,712
 11e:	0000                	unimp
 120:	05b8                	addi	a4,sp,712
 122:	0000                	unimp
 124:	05b8                	addi	a4,sp,712
 126:	0000                	unimp
 128:	05b8                	addi	a4,sp,712
 12a:	0000                	unimp
 12c:	05b8                	addi	a4,sp,712
 12e:	0000                	unimp
 130:	05b8                	addi	a4,sp,712
 132:	0000                	unimp
 134:	05b8                	addi	a4,sp,712
 136:	0000                	unimp
 138:	05b8                	addi	a4,sp,712
 13a:	0000                	unimp
 13c:	05b8                	addi	a4,sp,712
 13e:	0000                	unimp
 140:	05b8                	addi	a4,sp,712
 142:	0000                	unimp
 144:	05b8                	addi	a4,sp,712
 146:	0000                	unimp
 148:	05b8                	addi	a4,sp,712
 14a:	0000                	unimp
 14c:	05b8                	addi	a4,sp,712
 14e:	0000                	unimp
 150:	05b8                	addi	a4,sp,712
 152:	0000                	unimp
 154:	05b8                	addi	a4,sp,712
 156:	0000                	unimp
 158:	05b8                	addi	a4,sp,712
 15a:	0000                	unimp
 15c:	05b8                	addi	a4,sp,712
 15e:	0000                	unimp
 160:	05b8                	addi	a4,sp,712
 162:	0000                	unimp
 164:	05b8                	addi	a4,sp,712
 166:	0000                	unimp
 168:	05b8                	addi	a4,sp,712
 16a:	0000                	unimp
 16c:	05b8                	addi	a4,sp,712
 16e:	0000                	unimp
 170:	05b8                	addi	a4,sp,712
 172:	0000                	unimp
 174:	05b8                	addi	a4,sp,712
 176:	0000                	unimp
 178:	05b8                	addi	a4,sp,712
 17a:	0000                	unimp
 17c:	05b8                	addi	a4,sp,712
 17e:	0000                	unimp
 180:	05b8                	addi	a4,sp,712
 182:	0000                	unimp
 184:	05b8                	addi	a4,sp,712
 186:	0000                	unimp
 188:	05b8                	addi	a4,sp,712
 18a:	0000                	unimp
 18c:	05b8                	addi	a4,sp,712
 18e:	0000                	unimp
 190:	05b8                	addi	a4,sp,712
 192:	0000                	unimp
 194:	05b8                	addi	a4,sp,712
 196:	0000                	unimp
 198:	05b8                	addi	a4,sp,712
 19a:	0000                	unimp
 19c:	05b8                	addi	a4,sp,712
 19e:	0000                	unimp
 1a0:	05b8                	addi	a4,sp,712
	...

Disassembly of section .text:

000001c0 <__riscv_save_12>:
     1c0:	7139                	addi	sp,sp,-64
     1c2:	4301                	li	t1,0
     1c4:	c66e                	sw	s11,12(sp)
     1c6:	a019                	j	1cc <__riscv_save_10+0x4>

000001c8 <__riscv_save_10>:
     1c8:	7139                	addi	sp,sp,-64
     1ca:	5341                	li	t1,-16
     1cc:	c86a                	sw	s10,16(sp)
     1ce:	ca66                	sw	s9,20(sp)
     1d0:	cc62                	sw	s8,24(sp)
     1d2:	ce5e                	sw	s7,28(sp)
     1d4:	a019                	j	1da <__riscv_save_4+0x4>

000001d6 <__riscv_save_4>:
     1d6:	7139                	addi	sp,sp,-64
     1d8:	5301                	li	t1,-32
     1da:	d05a                	sw	s6,32(sp)
     1dc:	d256                	sw	s5,36(sp)
     1de:	d452                	sw	s4,40(sp)
     1e0:	d64e                	sw	s3,44(sp)
     1e2:	d84a                	sw	s2,48(sp)
     1e4:	da26                	sw	s1,52(sp)
     1e6:	dc22                	sw	s0,56(sp)
     1e8:	de06                	sw	ra,60(sp)
     1ea:	40610133          	sub	sp,sp,t1
     1ee:	8282                	jr	t0

000001f0 <__riscv_save_0>:
     1f0:	1141                	addi	sp,sp,-16
     1f2:	c04a                	sw	s2,0(sp)
     1f4:	c226                	sw	s1,4(sp)
     1f6:	c422                	sw	s0,8(sp)
     1f8:	c606                	sw	ra,12(sp)
     1fa:	8282                	jr	t0

000001fc <__riscv_restore_12>:
     1fc:	4db2                	lw	s11,12(sp)
     1fe:	0141                	addi	sp,sp,16

00000200 <__riscv_restore_10>:
     200:	4d02                	lw	s10,0(sp)
     202:	4c92                	lw	s9,4(sp)
     204:	4c22                	lw	s8,8(sp)
     206:	4bb2                	lw	s7,12(sp)
     208:	0141                	addi	sp,sp,16

0000020a <__riscv_restore_4>:
     20a:	4b02                	lw	s6,0(sp)
     20c:	4a92                	lw	s5,4(sp)
     20e:	4a22                	lw	s4,8(sp)
     210:	49b2                	lw	s3,12(sp)
     212:	0141                	addi	sp,sp,16

00000214 <__riscv_restore_0>:
     214:	4902                	lw	s2,0(sp)
     216:	4492                	lw	s1,4(sp)
     218:	4422                	lw	s0,8(sp)
     21a:	40b2                	lw	ra,12(sp)
     21c:	0141                	addi	sp,sp,16
     21e:	8082                	ret

00000220 <NMI_Handler>:
     220:	a001                	j	220 <NMI_Handler>

00000222 <HardFault_Handler>:
     222:	beef07b7          	lui	a5,0xbeef0
     226:	e000e737          	lui	a4,0xe000e
     22a:	08078793          	addi	a5,a5,128 # beef0080 <_eusrstack+0x9eee8080>
     22e:	c73c                	sw	a5,72(a4)
     230:	a001                	j	230 <HardFault_Handler+0xe>

00000232 <lcd_proc>:
     232:	8082                	ret

00000234 <key_proc>:
     234:	fbdff2ef          	jal	t0,1f0 <__riscv_save_0>
     238:	16d000ef          	jal	ra,ba4 <key_read>
     23c:	80918713          	addi	a4,gp,-2039 # 20000051 <key_old>
     240:	80a185a3          	sb	a0,-2037(gp) # 20000053 <key_val>
     244:	231c                	lbu	a5,0(a4)
     246:	a308                	sb	a0,0(a4)
     248:	00f546b3          	xor	a3,a0,a5
     24c:	fff7c793          	not	a5,a5
     250:	80d18623          	sb	a3,-2036(gp) # 20000054 <p>
     254:	8fe9                	and	a5,a5,a0
     256:	80f18423          	sb	a5,-2040(gp) # 20000050 <_edata>
     25a:	fff54793          	not	a5,a0
     25e:	8ff5                	and	a5,a5,a3
     260:	80f18523          	sb	a5,-2038(gp) # 20000052 <key_up>
     264:	bf45                	j	214 <__riscv_restore_0>

00000266 <lock_proc>:
     266:	8082                	ret

00000268 <Tim3_Init>:
     268:	f89ff2ef          	jal	t0,1f0 <__riscv_save_0>
     26c:	1101                	addi	sp,sp,-32
     26e:	84aa                	mv	s1,a0
     270:	842e                	mv	s0,a1
     272:	4509                	li	a0,2
     274:	4585                	li	a1,1
     276:	2561                	jal	8fe <RCC_APB1PeriphClockCmd>
     278:	82e0                	sh	s0,20(sp)
     27a:	40000437          	lui	s0,0x40000
     27e:	084c                	addi	a1,sp,20
     280:	40040513          	addi	a0,s0,1024 # 40000400 <_eusrstack+0x1fff8400>
     284:	84e4                	sh	s1,24(sp)
     286:	00011d23          	sh	zero,26(sp)
     28a:	00011b23          	sh	zero,22(sp)
     28e:	2de5                	jal	986 <TIM_TimeBaseInit>
     290:	4605                	li	a2,1
     292:	04100593          	li	a1,65
     296:	40040513          	addi	a0,s0,1024
     29a:	7ae000ef          	jal	ra,a48 <TIM_ITConfig>
     29e:	12d00793          	li	a5,301
     2a2:	867c                	sh	a5,12(sp)
     2a4:	478d                	li	a5,3
     2a6:	875c                	sb	a5,14(sp)
     2a8:	0068                	addi	a0,sp,12
     2aa:	4785                	li	a5,1
     2ac:	c83e                	sw	a5,16(sp)
     2ae:	21bd                	jal	71c <NVIC_Init>
     2b0:	4585                	li	a1,1
     2b2:	40040513          	addi	a0,s0,1024
     2b6:	77a000ef          	jal	ra,a30 <TIM_Cmd>
     2ba:	6105                	addi	sp,sp,32
     2bc:	bfa1                	j	214 <__riscv_restore_0>

000002be <TIM3_IRQHandler>:
     2be:	40000537          	lui	a0,0x40000
     2c2:	4585                	li	a1,1
     2c4:	40050513          	addi	a0,a0,1024 # 40000400 <_eusrstack+0x1fff8400>
     2c8:	792000ef          	jal	ra,a5a <TIM_GetITStatus>
     2cc:	c511                	beqz	a0,2d8 <TIM3_IRQHandler+0x1a>
     2ce:	81018793          	addi	a5,gp,-2032 # 20000058 <uwtick>
     2d2:	4398                	lw	a4,0(a5)
     2d4:	0705                	addi	a4,a4,1
     2d6:	c398                	sw	a4,0(a5)
     2d8:	40000537          	lui	a0,0x40000
     2dc:	4585                	li	a1,1
     2de:	40050513          	addi	a0,a0,1024 # 40000400 <_eusrstack+0x1fff8400>
     2e2:	790000ef          	jal	ra,a72 <TIM_ClearITPendingBit>
     2e6:	30200073          	mret

000002ea <scheduler_run>:
     2ea:	eedff2ef          	jal	t0,1d6 <__riscv_save_4>
     2ee:	200004b7          	lui	s1,0x20000
     2f2:	4401                	li	s0,0
     2f4:	00048493          	mv	s1,s1
     2f8:	4a31                	li	s4,12
     2fa:	80d1c783          	lbu	a5,-2035(gp) # 20000055 <task_num>
     2fe:	00f46363          	bltu	s0,a5,304 <scheduler_run+0x1a>
     302:	b721                	j	20a <__riscv_restore_4>
     304:	034407b3          	mul	a5,s0,s4
     308:	8101a683          	lw	a3,-2032(gp) # 20000058 <uwtick>
     30c:	97a6                	add	a5,a5,s1
     30e:	43d8                	lw	a4,4(a5)
     310:	4790                	lw	a2,8(a5)
     312:	9732                	add	a4,a4,a2
     314:	00e6e563          	bltu	a3,a4,31e <scheduler_run+0x34>
     318:	c794                	sw	a3,8(a5)
     31a:	439c                	lw	a5,0(a5)
     31c:	9782                	jalr	a5
     31e:	0405                	addi	s0,s0,1
     320:	0ff47413          	andi	s0,s0,255
     324:	bfd9                	j	2fa <scheduler_run+0x10>

00000326 <main>:
     326:	ecbff2ef          	jal	t0,1f0 <__riscv_save_0>
     32a:	4509                	li	a0,2
     32c:	26ed                	jal	716 <NVIC_PriorityGroupConfig>
     32e:	2ab1                	jal	48a <SystemCoreClockUpdate>
     330:	6571                	lui	a0,0x1c
     332:	20050513          	addi	a0,a0,512 # 1c200 <_data_lma+0x1aea4>
     336:	7cb000ef          	jal	ra,1300 <USART_Printf_Init>
     33a:	6b7000ef          	jal	ra,11f0 <WaterPump_Init>
     33e:	4585                	li	a1,1
     340:	4501                	li	a0,0
     342:	731000ef          	jal	ra,1272 <WaterPump_Control>
     346:	6505                	lui	a0,0x1
     348:	bb850513          	addi	a0,a0,-1096 # bb8 <key_read+0x14>
     34c:	68b000ef          	jal	ra,11d6 <delay_ms>
     350:	4581                	li	a1,0
     352:	4501                	li	a0,0
     354:	71f000ef          	jal	ra,1272 <WaterPump_Control>
     358:	7d000513          	li	a0,2000
     35c:	67b000ef          	jal	ra,11d6 <delay_ms>
     360:	423000ef          	jal	ra,f82 <LCD_Init>
     364:	6741                	lui	a4,0x10
     366:	177d                	addi	a4,a4,-1
     368:	07f00693          	li	a3,127
     36c:	07f00613          	li	a2,127
     370:	4581                	li	a1,0
     372:	4501                	li	a0,0
     374:	615000ef          	jal	ra,1188 <LCD_Fill>
     378:	7aa000ef          	jal	ra,b22 <key_init>
     37c:	05f00593          	li	a1,95
     380:	3e800513          	li	a0,1000
     384:	35d5                	jal	268 <Tim3_Init>
     386:	470d                	li	a4,3
     388:	80e186a3          	sb	a4,-2035(gp) # 20000055 <task_num>
     38c:	3fb9                	jal	2ea <scheduler_run>
     38e:	bffd                	j	38c <main+0x66>

00000390 <SystemInit>:
     390:	400217b7          	lui	a5,0x40021
     394:	4398                	lw	a4,0(a5)
     396:	f0ff06b7          	lui	a3,0xf0ff0
     39a:	1141                	addi	sp,sp,-16
     39c:	00176713          	ori	a4,a4,1
     3a0:	c398                	sw	a4,0(a5)
     3a2:	43d8                	lw	a4,4(a5)
     3a4:	00020637          	lui	a2,0x20
     3a8:	8f75                	and	a4,a4,a3
     3aa:	c3d8                	sw	a4,4(a5)
     3ac:	4398                	lw	a4,0(a5)
     3ae:	fef706b7          	lui	a3,0xfef70
     3b2:	16fd                	addi	a3,a3,-1
     3b4:	8f75                	and	a4,a4,a3
     3b6:	c398                	sw	a4,0(a5)
     3b8:	4398                	lw	a4,0(a5)
     3ba:	fffc06b7          	lui	a3,0xfffc0
     3be:	16fd                	addi	a3,a3,-1
     3c0:	8f75                	and	a4,a4,a3
     3c2:	c398                	sw	a4,0(a5)
     3c4:	43d8                	lw	a4,4(a5)
     3c6:	ff0106b7          	lui	a3,0xff010
     3ca:	16fd                	addi	a3,a3,-1
     3cc:	8f75                	and	a4,a4,a3
     3ce:	c3d8                	sw	a4,4(a5)
     3d0:	4398                	lw	a4,0(a5)
     3d2:	ec0006b7          	lui	a3,0xec000
     3d6:	16fd                	addi	a3,a3,-1
     3d8:	8f75                	and	a4,a4,a3
     3da:	c398                	sw	a4,0(a5)
     3dc:	00ff0737          	lui	a4,0xff0
     3e0:	c798                	sw	a4,8(a5)
     3e2:	0207a623          	sw	zero,44(a5) # 4002102c <_eusrstack+0x2001902c>
     3e6:	c402                	sw	zero,8(sp)
     3e8:	c602                	sw	zero,12(sp)
     3ea:	4398                	lw	a4,0(a5)
     3ec:	66c1                	lui	a3,0x10
     3ee:	8f55                	or	a4,a4,a3
     3f0:	c398                	sw	a4,0(a5)
     3f2:	400216b7          	lui	a3,0x40021
     3f6:	6705                	lui	a4,0x1
     3f8:	429c                	lw	a5,0(a3)
     3fa:	8ff1                	and	a5,a5,a2
     3fc:	c63e                	sw	a5,12(sp)
     3fe:	47a2                	lw	a5,8(sp)
     400:	0785                	addi	a5,a5,1
     402:	c43e                	sw	a5,8(sp)
     404:	47b2                	lw	a5,12(sp)
     406:	e781                	bnez	a5,40e <SystemInit+0x7e>
     408:	47a2                	lw	a5,8(sp)
     40a:	fee797e3          	bne	a5,a4,3f8 <SystemInit+0x68>
     40e:	400217b7          	lui	a5,0x40021
     412:	439c                	lw	a5,0(a5)
     414:	00e79713          	slli	a4,a5,0xe
     418:	06075763          	bgez	a4,486 <SystemInit+0xf6>
     41c:	4785                	li	a5,1
     41e:	c63e                	sw	a5,12(sp)
     420:	4732                	lw	a4,12(sp)
     422:	4785                	li	a5,1
     424:	04f71f63          	bne	a4,a5,482 <SystemInit+0xf2>
     428:	400217b7          	lui	a5,0x40021
     42c:	43d8                	lw	a4,4(a5)
     42e:	ffc106b7          	lui	a3,0xffc10
     432:	16fd                	addi	a3,a3,-1
     434:	c3d8                	sw	a4,4(a5)
     436:	43d8                	lw	a4,4(a5)
     438:	c3d8                	sw	a4,4(a5)
     43a:	43d8                	lw	a4,4(a5)
     43c:	40076713          	ori	a4,a4,1024
     440:	c3d8                	sw	a4,4(a5)
     442:	43d8                	lw	a4,4(a5)
     444:	8f75                	and	a4,a4,a3
     446:	c3d8                	sw	a4,4(a5)
     448:	43d8                	lw	a4,4(a5)
     44a:	002906b7          	lui	a3,0x290
     44e:	8f55                	or	a4,a4,a3
     450:	c3d8                	sw	a4,4(a5)
     452:	4398                	lw	a4,0(a5)
     454:	010006b7          	lui	a3,0x1000
     458:	8f55                	or	a4,a4,a3
     45a:	c398                	sw	a4,0(a5)
     45c:	4398                	lw	a4,0(a5)
     45e:	00671693          	slli	a3,a4,0x6
     462:	fe06dde3          	bgez	a3,45c <SystemInit+0xcc>
     466:	43d8                	lw	a4,4(a5)
     468:	400216b7          	lui	a3,0x40021
     46c:	9b71                	andi	a4,a4,-4
     46e:	c3d8                	sw	a4,4(a5)
     470:	43d8                	lw	a4,4(a5)
     472:	00276713          	ori	a4,a4,2
     476:	c3d8                	sw	a4,4(a5)
     478:	4721                	li	a4,8
     47a:	42dc                	lw	a5,4(a3)
     47c:	8bb1                	andi	a5,a5,12
     47e:	fee79ee3          	bne	a5,a4,47a <SystemInit+0xea>
     482:	0141                	addi	sp,sp,16
     484:	8082                	ret
     486:	c602                	sw	zero,12(sp)
     488:	bf61                	j	420 <SystemInit+0x90>

0000048a <SystemCoreClockUpdate>:
     48a:	400216b7          	lui	a3,0x40021
     48e:	42d8                	lw	a4,4(a3)
     490:	200007b7          	lui	a5,0x20000
     494:	4611                	li	a2,4
     496:	8b31                	andi	a4,a4,12
     498:	04878793          	addi	a5,a5,72 # 20000048 <SystemCoreClock>
     49c:	00c70563          	beq	a4,a2,4a6 <SystemCoreClockUpdate+0x1c>
     4a0:	4621                	li	a2,8
     4a2:	02c70863          	beq	a4,a2,4d2 <SystemCoreClockUpdate+0x48>
     4a6:	007a1737          	lui	a4,0x7a1
     4aa:	20070713          	addi	a4,a4,512 # 7a1200 <_data_lma+0x79fea4>
     4ae:	c398                	sw	a4,0(a5)
     4b0:	40021737          	lui	a4,0x40021
     4b4:	4358                	lw	a4,4(a4)
     4b6:	8311                	srli	a4,a4,0x4
     4b8:	00f77693          	andi	a3,a4,15
     4bc:	20000737          	lui	a4,0x20000
     4c0:	02470713          	addi	a4,a4,36 # 20000024 <AHBPrescTable>
     4c4:	9736                	add	a4,a4,a3
     4c6:	2314                	lbu	a3,0(a4)
     4c8:	4398                	lw	a4,0(a5)
     4ca:	00d75733          	srl	a4,a4,a3
     4ce:	c398                	sw	a4,0(a5)
     4d0:	8082                	ret
     4d2:	42d8                	lw	a4,4(a3)
     4d4:	42d4                	lw	a3,4(a3)
     4d6:	6641                	lui	a2,0x10
     4d8:	8349                	srli	a4,a4,0x12
     4da:	8b3d                	andi	a4,a4,15
     4dc:	8ef1                	and	a3,a3,a2
     4de:	00270613          	addi	a2,a4,2
     4e2:	cf15                	beqz	a4,51e <SystemCoreClockUpdate+0x94>
     4e4:	473d                	li	a4,15
     4e6:	02e60f63          	beq	a2,a4,524 <SystemCoreClockUpdate+0x9a>
     4ea:	4741                	li	a4,16
     4ec:	02e60f63          	beq	a2,a4,52a <SystemCoreClockUpdate+0xa0>
     4f0:	4745                	li	a4,17
     4f2:	4581                	li	a1,0
     4f4:	00e61363          	bne	a2,a4,4fa <SystemCoreClockUpdate+0x70>
     4f8:	4641                	li	a2,16
     4fa:	e2a1                	bnez	a3,53a <SystemCoreClockUpdate+0xb0>
     4fc:	40024737          	lui	a4,0x40024
     500:	80072703          	lw	a4,-2048(a4) # 40023800 <_eusrstack+0x2001b800>
     504:	8b41                	andi	a4,a4,16
     506:	c70d                	beqz	a4,530 <SystemCoreClockUpdate+0xa6>
     508:	007a1737          	lui	a4,0x7a1
     50c:	20070713          	addi	a4,a4,512 # 7a1200 <_data_lma+0x79fea4>
     510:	02c70633          	mul	a2,a4,a2
     514:	c390                	sw	a2,0(a5)
     516:	ddc9                	beqz	a1,4b0 <SystemCoreClockUpdate+0x26>
     518:	4398                	lw	a4,0(a5)
     51a:	8305                	srli	a4,a4,0x1
     51c:	bf49                	j	4ae <SystemCoreClockUpdate+0x24>
     51e:	4581                	li	a1,0
     520:	4649                	li	a2,18
     522:	bfe1                	j	4fa <SystemCoreClockUpdate+0x70>
     524:	4585                	li	a1,1
     526:	4635                	li	a2,13
     528:	bfc9                	j	4fa <SystemCoreClockUpdate+0x70>
     52a:	4581                	li	a1,0
     52c:	463d                	li	a2,15
     52e:	b7f1                	j	4fa <SystemCoreClockUpdate+0x70>
     530:	003d1737          	lui	a4,0x3d1
     534:	90070713          	addi	a4,a4,-1792 # 3d0900 <_data_lma+0x3cf5a4>
     538:	bfe1                	j	510 <SystemCoreClockUpdate+0x86>
     53a:	40021537          	lui	a0,0x40021
     53e:	5558                	lw	a4,44(a0)
     540:	00f71693          	slli	a3,a4,0xf
     544:	5558                	lw	a4,44(a0)
     546:	0406df63          	bgez	a3,5a4 <SystemCoreClockUpdate+0x11a>
     54a:	8311                	srli	a4,a4,0x4
     54c:	8b3d                	andi	a4,a4,15
     54e:	00170693          	addi	a3,a4,1
     552:	007a1737          	lui	a4,0x7a1
     556:	20070713          	addi	a4,a4,512 # 7a1200 <_data_lma+0x79fea4>
     55a:	02d75733          	divu	a4,a4,a3
     55e:	c398                	sw	a4,0(a5)
     560:	5554                	lw	a3,44(a0)
     562:	82a1                	srli	a3,a3,0x8
     564:	8abd                	andi	a3,a3,15
     566:	e28d                	bnez	a3,588 <SystemCoreClockUpdate+0xfe>
     568:	4695                	li	a3,5
     56a:	02d70733          	mul	a4,a4,a3
     56e:	8305                	srli	a4,a4,0x1
     570:	c398                	sw	a4,0(a5)
     572:	40021737          	lui	a4,0x40021
     576:	5758                	lw	a4,44(a4)
     578:	4394                	lw	a3,0(a5)
     57a:	8b3d                	andi	a4,a4,15
     57c:	0705                	addi	a4,a4,1
     57e:	02e6d733          	divu	a4,a3,a4
     582:	c398                	sw	a4,0(a5)
     584:	4398                	lw	a4,0(a5)
     586:	b769                	j	510 <SystemCoreClockUpdate+0x86>
     588:	4505                	li	a0,1
     58a:	00a69463          	bne	a3,a0,592 <SystemCoreClockUpdate+0x108>
     58e:	46e5                	li	a3,25
     590:	bfe9                	j	56a <SystemCoreClockUpdate+0xe0>
     592:	453d                	li	a0,15
     594:	00a69663          	bne	a3,a0,5a0 <SystemCoreClockUpdate+0x116>
     598:	46d1                	li	a3,20
     59a:	02e68733          	mul	a4,a3,a4
     59e:	bfc9                	j	570 <SystemCoreClockUpdate+0xe6>
     5a0:	0689                	addi	a3,a3,2
     5a2:	bfe5                	j	59a <SystemCoreClockUpdate+0x110>
     5a4:	8b3d                	andi	a4,a4,15
     5a6:	00170693          	addi	a3,a4,1 # 40021001 <_eusrstack+0x20019001>
     5aa:	007a1737          	lui	a4,0x7a1
     5ae:	20070713          	addi	a4,a4,512 # 7a1200 <_data_lma+0x79fea4>
     5b2:	02d75733          	divu	a4,a4,a3
     5b6:	b7f1                	j	582 <SystemCoreClockUpdate+0xf8>

000005b8 <ADC1_2_IRQHandler>:
     5b8:	a001                	j	5b8 <ADC1_2_IRQHandler>

000005ba <handle_reset>:
     5ba:	20000197          	auipc	gp,0x20000
     5be:	28e18193          	addi	gp,gp,654 # 20000848 <__global_pointer$>
     5c2:	20008117          	auipc	sp,0x20008
     5c6:	a3e10113          	addi	sp,sp,-1474 # 20008000 <_eusrstack>
     5ca:	00001517          	auipc	a0,0x1
     5ce:	d9250513          	addi	a0,a0,-622 # 135c <_data_lma>
     5d2:	20000597          	auipc	a1,0x20000
     5d6:	a2e58593          	addi	a1,a1,-1490 # 20000000 <_data_vma>
     5da:	80818613          	addi	a2,gp,-2040 # 20000050 <_edata>
     5de:	00c5fa63          	bgeu	a1,a2,5f2 <handle_reset+0x38>
     5e2:	00052283          	lw	t0,0(a0)
     5e6:	0055a023          	sw	t0,0(a1)
     5ea:	0511                	addi	a0,a0,4
     5ec:	0591                	addi	a1,a1,4
     5ee:	fec5eae3          	bltu	a1,a2,5e2 <handle_reset+0x28>
     5f2:	80818513          	addi	a0,gp,-2040 # 20000050 <_edata>
     5f6:	83c18593          	addi	a1,gp,-1988 # 20000084 <_ebss>
     5fa:	00b57763          	bgeu	a0,a1,608 <handle_reset+0x4e>
     5fe:	00052023          	sw	zero,0(a0)
     602:	0511                	addi	a0,a0,4
     604:	feb56de3          	bltu	a0,a1,5fe <handle_reset+0x44>
     608:	42fd                	li	t0,31
     60a:	bc029073          	csrw	0xbc0,t0
     60e:	42ad                	li	t0,11
     610:	80429073          	csrw	0x804,t0
     614:	000062b7          	lui	t0,0x6
     618:	08828293          	addi	t0,t0,136 # 6088 <_data_lma+0x4d2c>
     61c:	30029073          	csrw	mstatus,t0
     620:	00000297          	auipc	t0,0x0
     624:	9e428293          	addi	t0,t0,-1564 # 4 <_einit>
     628:	0032e293          	ori	t0,t0,3
     62c:	30529073          	csrw	mtvec,t0
     630:	d61ff0ef          	jal	ra,390 <SystemInit>
     634:	00000297          	auipc	t0,0x0
     638:	cf228293          	addi	t0,t0,-782 # 326 <main>
     63c:	34129073          	csrw	mepc,t0
     640:	30200073          	mret

00000644 <GPIO_Init>:
     644:	459c                	lw	a5,8(a1)
     646:	0107f713          	andi	a4,a5,16
     64a:	00f7f813          	andi	a6,a5,15
     64e:	c701                	beqz	a4,656 <GPIO_Init+0x12>
     650:	41d8                	lw	a4,4(a1)
     652:	00e86833          	or	a6,a6,a4
     656:	218e                	lhu	a1,0(a1)
     658:	0ff5f713          	andi	a4,a1,255
     65c:	c339                	beqz	a4,6a2 <GPIO_Init+0x5e>
     65e:	4118                	lw	a4,0(a0)
     660:	4681                	li	a3,0
     662:	4e85                	li	t4,1
     664:	4f3d                	li	t5,15
     666:	02800f93          	li	t6,40
     66a:	04800293          	li	t0,72
     66e:	4e21                	li	t3,8
     670:	00de9633          	sll	a2,t4,a3
     674:	00c5f8b3          	and	a7,a1,a2
     678:	03161163          	bne	a2,a7,69a <GPIO_Init+0x56>
     67c:	00269893          	slli	a7,a3,0x2
     680:	011f1333          	sll	t1,t5,a7
     684:	fff34313          	not	t1,t1
     688:	00e37733          	and	a4,t1,a4
     68c:	011818b3          	sll	a7,a6,a7
     690:	00e8e733          	or	a4,a7,a4
     694:	05f79f63          	bne	a5,t6,6f2 <GPIO_Init+0xae>
     698:	c950                	sw	a2,20(a0)
     69a:	0685                	addi	a3,a3,1
     69c:	fdc69ae3          	bne	a3,t3,670 <GPIO_Init+0x2c>
     6a0:	c118                	sw	a4,0(a0)
     6a2:	0ff00713          	li	a4,255
     6a6:	04b77563          	bgeu	a4,a1,6f0 <GPIO_Init+0xac>
     6aa:	4154                	lw	a3,4(a0)
     6ac:	4621                	li	a2,8
     6ae:	4e85                	li	t4,1
     6b0:	4f3d                	li	t5,15
     6b2:	02800f93          	li	t6,40
     6b6:	04800293          	li	t0,72
     6ba:	4e41                	li	t3,16
     6bc:	00ce98b3          	sll	a7,t4,a2
     6c0:	0115f733          	and	a4,a1,a7
     6c4:	02e89263          	bne	a7,a4,6e8 <GPIO_Init+0xa4>
     6c8:	00261713          	slli	a4,a2,0x2
     6cc:	1701                	addi	a4,a4,-32
     6ce:	00ef1333          	sll	t1,t5,a4
     6d2:	fff34313          	not	t1,t1
     6d6:	00d376b3          	and	a3,t1,a3
     6da:	00e81733          	sll	a4,a6,a4
     6de:	8ed9                	or	a3,a3,a4
     6e0:	01f79d63          	bne	a5,t6,6fa <GPIO_Init+0xb6>
     6e4:	01152a23          	sw	a7,20(a0)
     6e8:	0605                	addi	a2,a2,1
     6ea:	fdc619e3          	bne	a2,t3,6bc <GPIO_Init+0x78>
     6ee:	c154                	sw	a3,4(a0)
     6f0:	8082                	ret
     6f2:	fa5794e3          	bne	a5,t0,69a <GPIO_Init+0x56>
     6f6:	c910                	sw	a2,16(a0)
     6f8:	b74d                	j	69a <GPIO_Init+0x56>
     6fa:	fe5797e3          	bne	a5,t0,6e8 <GPIO_Init+0xa4>
     6fe:	01152823          	sw	a7,16(a0)
     702:	b7dd                	j	6e8 <GPIO_Init+0xa4>

00000704 <GPIO_ReadInputDataBit>:
     704:	4508                	lw	a0,8(a0)
     706:	8d6d                	and	a0,a0,a1
     708:	00a03533          	snez	a0,a0
     70c:	8082                	ret

0000070e <GPIO_SetBits>:
     70e:	c90c                	sw	a1,16(a0)
     710:	8082                	ret

00000712 <GPIO_ResetBits>:
     712:	c94c                	sw	a1,20(a0)
     714:	8082                	ret

00000716 <NVIC_PriorityGroupConfig>:
     716:	80a1aa23          	sw	a0,-2028(gp) # 2000005c <NVIC_Priority_Group>
     71a:	8082                	ret

0000071c <NVIC_Init>:
     71c:	8141a703          	lw	a4,-2028(gp) # 2000005c <NVIC_Priority_Group>
     720:	4789                	li	a5,2
     722:	2110                	lbu	a2,0(a0)
     724:	02f71163          	bne	a4,a5,746 <NVIC_Init+0x2a>
     728:	3114                	lbu	a3,1(a0)
     72a:	478d                	li	a5,3
     72c:	00d7ed63          	bltu	a5,a3,746 <NVIC_Init+0x2a>
     730:	213c                	lbu	a5,2(a0)
     732:	069a                	slli	a3,a3,0x6
     734:	e000e737          	lui	a4,0xe000e
     738:	0796                	slli	a5,a5,0x5
     73a:	8fd5                	or	a5,a5,a3
     73c:	0ff7f793          	andi	a5,a5,255
     740:	9732                	add	a4,a4,a2
     742:	40f70023          	sb	a5,1024(a4) # e000e400 <_eusrstack+0xc0006400>
     746:	4154                	lw	a3,4(a0)
     748:	4705                	li	a4,1
     74a:	00565793          	srli	a5,a2,0x5
     74e:	00c71733          	sll	a4,a4,a2
     752:	ca89                	beqz	a3,764 <NVIC_Init+0x48>
     754:	04078793          	addi	a5,a5,64
     758:	078a                	slli	a5,a5,0x2
     75a:	e000e6b7          	lui	a3,0xe000e
     75e:	97b6                	add	a5,a5,a3
     760:	c398                	sw	a4,0(a5)
     762:	8082                	ret
     764:	06078793          	addi	a5,a5,96
     768:	bfc5                	j	758 <NVIC_Init+0x3c>

0000076a <RCC_GetClocksFreq>:
     76a:	40021737          	lui	a4,0x40021
     76e:	435c                	lw	a5,4(a4)
     770:	4691                	li	a3,4
     772:	8bb1                	andi	a5,a5,12
     774:	00d78563          	beq	a5,a3,77e <RCC_GetClocksFreq+0x14>
     778:	46a1                	li	a3,8
     77a:	06d78263          	beq	a5,a3,7de <RCC_GetClocksFreq+0x74>
     77e:	007a17b7          	lui	a5,0x7a1
     782:	20078793          	addi	a5,a5,512 # 7a1200 <_data_lma+0x79fea4>
     786:	c11c                	sw	a5,0(a0)
     788:	40021637          	lui	a2,0x40021
     78c:	425c                	lw	a5,4(a2)
     78e:	20000737          	lui	a4,0x20000
     792:	03470713          	addi	a4,a4,52 # 20000034 <APBAHBPrescTable>
     796:	8391                	srli	a5,a5,0x4
     798:	8bbd                	andi	a5,a5,15
     79a:	97ba                	add	a5,a5,a4
     79c:	2394                	lbu	a3,0(a5)
     79e:	411c                	lw	a5,0(a0)
     7a0:	00d7d7b3          	srl	a5,a5,a3
     7a4:	c15c                	sw	a5,4(a0)
     7a6:	4254                	lw	a3,4(a2)
     7a8:	82a1                	srli	a3,a3,0x8
     7aa:	8a9d                	andi	a3,a3,7
     7ac:	96ba                	add	a3,a3,a4
     7ae:	2294                	lbu	a3,0(a3)
     7b0:	00d7d6b3          	srl	a3,a5,a3
     7b4:	c514                	sw	a3,8(a0)
     7b6:	4254                	lw	a3,4(a2)
     7b8:	82ad                	srli	a3,a3,0xb
     7ba:	8a9d                	andi	a3,a3,7
     7bc:	9736                	add	a4,a4,a3
     7be:	2318                	lbu	a4,0(a4)
     7c0:	00e7d7b3          	srl	a5,a5,a4
     7c4:	c55c                	sw	a5,12(a0)
     7c6:	4258                	lw	a4,4(a2)
     7c8:	8339                	srli	a4,a4,0xe
     7ca:	00377693          	andi	a3,a4,3
     7ce:	80418713          	addi	a4,gp,-2044 # 2000004c <ADCPrescTable>
     7d2:	9736                	add	a4,a4,a3
     7d4:	2318                	lbu	a4,0(a4)
     7d6:	02e7d7b3          	divu	a5,a5,a4
     7da:	c91c                	sw	a5,16(a0)
     7dc:	8082                	ret
     7de:	435c                	lw	a5,4(a4)
     7e0:	4358                	lw	a4,4(a4)
     7e2:	66c1                	lui	a3,0x10
     7e4:	83c9                	srli	a5,a5,0x12
     7e6:	8f75                	and	a4,a4,a3
     7e8:	1ffff6b7          	lui	a3,0x1ffff
     7ec:	70c6a683          	lw	a3,1804(a3) # 1ffff70c <_data_lma+0x1fffe3b0>
     7f0:	8bbd                	andi	a5,a5,15
     7f2:	0789                	addi	a5,a5,2
     7f4:	01169613          	slli	a2,a3,0x11
     7f8:	00064863          	bltz	a2,808 <__stack_size+0x8>
     7fc:	46c5                	li	a3,17
     7fe:	4601                	li	a2,0
     800:	02d79263          	bne	a5,a3,824 <__stack_size+0x24>
     804:	47c9                	li	a5,18
     806:	a839                	j	824 <__stack_size+0x24>
     808:	4689                	li	a3,2
     80a:	02d78f63          	beq	a5,a3,848 <__stack_size+0x48>
     80e:	46bd                	li	a3,15
     810:	02d78e63          	beq	a5,a3,84c <__stack_size+0x4c>
     814:	46c1                	li	a3,16
     816:	02d78e63          	beq	a5,a3,852 <__stack_size+0x52>
     81a:	46c5                	li	a3,17
     81c:	4601                	li	a2,0
     81e:	00d79363          	bne	a5,a3,824 <__stack_size+0x24>
     822:	47c1                	li	a5,16
     824:	ef1d                	bnez	a4,862 <__stack_size+0x62>
     826:	40024737          	lui	a4,0x40024
     82a:	80072703          	lw	a4,-2048(a4) # 40023800 <_eusrstack+0x2001b800>
     82e:	8b41                	andi	a4,a4,16
     830:	c705                	beqz	a4,858 <__stack_size+0x58>
     832:	007a1737          	lui	a4,0x7a1
     836:	20070713          	addi	a4,a4,512 # 7a1200 <_data_lma+0x79fea4>
     83a:	02f707b3          	mul	a5,a4,a5
     83e:	c11c                	sw	a5,0(a0)
     840:	d621                	beqz	a2,788 <RCC_GetClocksFreq+0x1e>
     842:	411c                	lw	a5,0(a0)
     844:	8385                	srli	a5,a5,0x1
     846:	b781                	j	786 <RCC_GetClocksFreq+0x1c>
     848:	4601                	li	a2,0
     84a:	bf6d                	j	804 <__stack_size+0x4>
     84c:	4605                	li	a2,1
     84e:	47b5                	li	a5,13
     850:	bfd1                	j	824 <__stack_size+0x24>
     852:	4601                	li	a2,0
     854:	47bd                	li	a5,15
     856:	b7f9                	j	824 <__stack_size+0x24>
     858:	003d1737          	lui	a4,0x3d1
     85c:	90070713          	addi	a4,a4,-1792 # 3d0900 <_data_lma+0x3cf5a4>
     860:	bfe9                	j	83a <__stack_size+0x3a>
     862:	400215b7          	lui	a1,0x40021
     866:	55d8                	lw	a4,44(a1)
     868:	00f71693          	slli	a3,a4,0xf
     86c:	55d8                	lw	a4,44(a1)
     86e:	0406df63          	bgez	a3,8cc <__stack_size+0xcc>
     872:	8311                	srli	a4,a4,0x4
     874:	8b3d                	andi	a4,a4,15
     876:	00170693          	addi	a3,a4,1
     87a:	007a1737          	lui	a4,0x7a1
     87e:	20070713          	addi	a4,a4,512 # 7a1200 <_data_lma+0x79fea4>
     882:	02d75733          	divu	a4,a4,a3
     886:	c118                	sw	a4,0(a0)
     888:	55d4                	lw	a3,44(a1)
     88a:	82a1                	srli	a3,a3,0x8
     88c:	8abd                	andi	a3,a3,15
     88e:	e28d                	bnez	a3,8b0 <__stack_size+0xb0>
     890:	4695                	li	a3,5
     892:	02d70733          	mul	a4,a4,a3
     896:	8305                	srli	a4,a4,0x1
     898:	c118                	sw	a4,0(a0)
     89a:	40021737          	lui	a4,0x40021
     89e:	5758                	lw	a4,44(a4)
     8a0:	4114                	lw	a3,0(a0)
     8a2:	8b3d                	andi	a4,a4,15
     8a4:	0705                	addi	a4,a4,1
     8a6:	02e6d733          	divu	a4,a3,a4
     8aa:	c118                	sw	a4,0(a0)
     8ac:	4118                	lw	a4,0(a0)
     8ae:	b771                	j	83a <__stack_size+0x3a>
     8b0:	4585                	li	a1,1
     8b2:	00b69463          	bne	a3,a1,8ba <__stack_size+0xba>
     8b6:	46e5                	li	a3,25
     8b8:	bfe9                	j	892 <__stack_size+0x92>
     8ba:	45bd                	li	a1,15
     8bc:	00b69663          	bne	a3,a1,8c8 <__stack_size+0xc8>
     8c0:	46d1                	li	a3,20
     8c2:	02e68733          	mul	a4,a3,a4
     8c6:	bfc9                	j	898 <__stack_size+0x98>
     8c8:	0689                	addi	a3,a3,2
     8ca:	bfe5                	j	8c2 <__stack_size+0xc2>
     8cc:	8b3d                	andi	a4,a4,15
     8ce:	00170693          	addi	a3,a4,1 # 40021001 <_eusrstack+0x20019001>
     8d2:	007a1737          	lui	a4,0x7a1
     8d6:	20070713          	addi	a4,a4,512 # 7a1200 <_data_lma+0x79fea4>
     8da:	02d75733          	divu	a4,a4,a3
     8de:	b7f1                	j	8aa <__stack_size+0xaa>

000008e0 <RCC_APB2PeriphClockCmd>:
     8e0:	c599                	beqz	a1,8ee <RCC_APB2PeriphClockCmd+0xe>
     8e2:	40021737          	lui	a4,0x40021
     8e6:	4f1c                	lw	a5,24(a4)
     8e8:	8d5d                	or	a0,a0,a5
     8ea:	cf08                	sw	a0,24(a4)
     8ec:	8082                	ret
     8ee:	400217b7          	lui	a5,0x40021
     8f2:	4f98                	lw	a4,24(a5)
     8f4:	fff54513          	not	a0,a0
     8f8:	8d79                	and	a0,a0,a4
     8fa:	cf88                	sw	a0,24(a5)
     8fc:	8082                	ret

000008fe <RCC_APB1PeriphClockCmd>:
     8fe:	c599                	beqz	a1,90c <RCC_APB1PeriphClockCmd+0xe>
     900:	40021737          	lui	a4,0x40021
     904:	4f5c                	lw	a5,28(a4)
     906:	8d5d                	or	a0,a0,a5
     908:	cf48                	sw	a0,28(a4)
     90a:	8082                	ret
     90c:	400217b7          	lui	a5,0x40021
     910:	4fd8                	lw	a4,28(a5)
     912:	fff54513          	not	a0,a0
     916:	8d79                	and	a0,a0,a4
     918:	cfc8                	sw	a0,28(a5)
     91a:	8082                	ret

0000091c <SPI_Init>:
     91c:	211a                	lhu	a4,0(a0)
     91e:	678d                	lui	a5,0x3
     920:	04078793          	addi	a5,a5,64 # 3040 <_data_lma+0x1ce4>
     924:	21b6                	lhu	a3,2(a1)
     926:	8f7d                	and	a4,a4,a5
     928:	219e                	lhu	a5,0(a1)
     92a:	8fd5                	or	a5,a5,a3
     92c:	21d6                	lhu	a3,4(a1)
     92e:	8fd5                	or	a5,a5,a3
     930:	21f6                	lhu	a3,6(a1)
     932:	8fd5                	or	a5,a5,a3
     934:	2596                	lhu	a3,8(a1)
     936:	8fd5                	or	a5,a5,a3
     938:	25b6                	lhu	a3,10(a1)
     93a:	8fd5                	or	a5,a5,a3
     93c:	25d6                	lhu	a3,12(a1)
     93e:	8fd5                	or	a5,a5,a3
     940:	25f6                	lhu	a3,14(a1)
     942:	8fd5                	or	a5,a5,a3
     944:	8fd9                	or	a5,a5,a4
     946:	a11e                	sh	a5,0(a0)
     948:	2d5a                	lhu	a4,28(a0)
     94a:	77fd                	lui	a5,0xfffff
     94c:	7ff78793          	addi	a5,a5,2047 # fffff7ff <_eusrstack+0xdfff77ff>
     950:	8ff9                	and	a5,a5,a4
     952:	ad5e                	sh	a5,28(a0)
     954:	299e                	lhu	a5,16(a1)
     956:	a91e                	sh	a5,16(a0)
     958:	8082                	ret

0000095a <SPI_Cmd>:
     95a:	211e                	lhu	a5,0(a0)
     95c:	c589                	beqz	a1,966 <SPI_Cmd+0xc>
     95e:	0407e793          	ori	a5,a5,64
     962:	a11e                	sh	a5,0(a0)
     964:	8082                	ret
     966:	07c2                	slli	a5,a5,0x10
     968:	83c1                	srli	a5,a5,0x10
     96a:	fbf7f793          	andi	a5,a5,-65
     96e:	07c2                	slli	a5,a5,0x10
     970:	83c1                	srli	a5,a5,0x10
     972:	bfc5                	j	962 <SPI_Cmd+0x8>

00000974 <SPI_I2S_SendData>:
     974:	a54e                	sh	a1,12(a0)
     976:	8082                	ret

00000978 <SPI_I2S_ReceiveData>:
     978:	254a                	lhu	a0,12(a0)
     97a:	8082                	ret

0000097c <SPI_I2S_GetFlagStatus>:
     97c:	250a                	lhu	a0,8(a0)
     97e:	8d6d                	and	a0,a0,a1
     980:	00a03533          	snez	a0,a0
     984:	8082                	ret

00000986 <TIM_TimeBaseInit>:
     986:	211e                	lhu	a5,0(a0)
     988:	40013737          	lui	a4,0x40013
     98c:	c0070693          	addi	a3,a4,-1024 # 40012c00 <_eusrstack+0x2000ac00>
     990:	07c2                	slli	a5,a5,0x10
     992:	83c1                	srli	a5,a5,0x10
     994:	04d50063          	beq	a0,a3,9d4 <TIM_TimeBaseInit+0x4e>
     998:	400006b7          	lui	a3,0x40000
     99c:	02d50c63          	beq	a0,a3,9d4 <TIM_TimeBaseInit+0x4e>
     9a0:	40068693          	addi	a3,a3,1024 # 40000400 <_eusrstack+0x1fff8400>
     9a4:	02d50863          	beq	a0,a3,9d4 <TIM_TimeBaseInit+0x4e>
     9a8:	400016b7          	lui	a3,0x40001
     9ac:	80068613          	addi	a2,a3,-2048 # 40000800 <_eusrstack+0x1fff8800>
     9b0:	02c50263          	beq	a0,a2,9d4 <TIM_TimeBaseInit+0x4e>
     9b4:	c0068693          	addi	a3,a3,-1024
     9b8:	00d50e63          	beq	a0,a3,9d4 <TIM_TimeBaseInit+0x4e>
     9bc:	40070713          	addi	a4,a4,1024
     9c0:	00e50a63          	beq	a0,a4,9d4 <TIM_TimeBaseInit+0x4e>
     9c4:	40015737          	lui	a4,0x40015
     9c8:	c0070693          	addi	a3,a4,-1024 # 40014c00 <_eusrstack+0x2000cc00>
     9cc:	00d50463          	beq	a0,a3,9d4 <TIM_TimeBaseInit+0x4e>
     9d0:	00e51663          	bne	a0,a4,9dc <TIM_TimeBaseInit+0x56>
     9d4:	21ba                	lhu	a4,2(a1)
     9d6:	f8f7f793          	andi	a5,a5,-113
     9da:	8fd9                	or	a5,a5,a4
     9dc:	40001737          	lui	a4,0x40001
     9e0:	00e50c63          	beq	a0,a4,9f8 <TIM_TimeBaseInit+0x72>
     9e4:	40070713          	addi	a4,a4,1024 # 40001400 <_eusrstack+0x1fff9400>
     9e8:	00e50863          	beq	a0,a4,9f8 <TIM_TimeBaseInit+0x72>
     9ec:	cff7f793          	andi	a5,a5,-769
     9f0:	21fa                	lhu	a4,6(a1)
     9f2:	07c2                	slli	a5,a5,0x10
     9f4:	83c1                	srli	a5,a5,0x10
     9f6:	8fd9                	or	a5,a5,a4
     9f8:	a11e                	sh	a5,0(a0)
     9fa:	21de                	lhu	a5,4(a1)
     9fc:	b55e                	sh	a5,44(a0)
     9fe:	219e                	lhu	a5,0(a1)
     a00:	b51e                	sh	a5,40(a0)
     a02:	400137b7          	lui	a5,0x40013
     a06:	c0078713          	addi	a4,a5,-1024 # 40012c00 <_eusrstack+0x2000ac00>
     a0a:	00e50e63          	beq	a0,a4,a26 <TIM_TimeBaseInit+0xa0>
     a0e:	40078793          	addi	a5,a5,1024
     a12:	00f50a63          	beq	a0,a5,a26 <TIM_TimeBaseInit+0xa0>
     a16:	400157b7          	lui	a5,0x40015
     a1a:	c0078713          	addi	a4,a5,-1024 # 40014c00 <_eusrstack+0x2000cc00>
     a1e:	00e50463          	beq	a0,a4,a26 <TIM_TimeBaseInit+0xa0>
     a22:	00f51463          	bne	a0,a5,a2a <TIM_TimeBaseInit+0xa4>
     a26:	259c                	lbu	a5,8(a1)
     a28:	b91e                	sh	a5,48(a0)
     a2a:	4785                	li	a5,1
     a2c:	a95e                	sh	a5,20(a0)
     a2e:	8082                	ret

00000a30 <TIM_Cmd>:
     a30:	211e                	lhu	a5,0(a0)
     a32:	c589                	beqz	a1,a3c <TIM_Cmd+0xc>
     a34:	0017e793          	ori	a5,a5,1
     a38:	a11e                	sh	a5,0(a0)
     a3a:	8082                	ret
     a3c:	07c2                	slli	a5,a5,0x10
     a3e:	83c1                	srli	a5,a5,0x10
     a40:	9bf9                	andi	a5,a5,-2
     a42:	07c2                	slli	a5,a5,0x10
     a44:	83c1                	srli	a5,a5,0x10
     a46:	bfcd                	j	a38 <TIM_Cmd+0x8>

00000a48 <TIM_ITConfig>:
     a48:	255e                	lhu	a5,12(a0)
     a4a:	c601                	beqz	a2,a52 <TIM_ITConfig+0xa>
     a4c:	8ddd                	or	a1,a1,a5
     a4e:	a54e                	sh	a1,12(a0)
     a50:	8082                	ret
     a52:	fff5c593          	not	a1,a1
     a56:	8dfd                	and	a1,a1,a5
     a58:	bfdd                	j	a4e <TIM_ITConfig+0x6>

00000a5a <TIM_GetITStatus>:
     a5a:	291e                	lhu	a5,16(a0)
     a5c:	254a                	lhu	a0,12(a0)
     a5e:	8fed                	and	a5,a5,a1
     a60:	0542                	slli	a0,a0,0x10
     a62:	8141                	srli	a0,a0,0x10
     a64:	c789                	beqz	a5,a6e <TIM_GetITStatus+0x14>
     a66:	8d6d                	and	a0,a0,a1
     a68:	00a03533          	snez	a0,a0
     a6c:	8082                	ret
     a6e:	4501                	li	a0,0
     a70:	8082                	ret

00000a72 <TIM_ClearITPendingBit>:
     a72:	fff5c593          	not	a1,a1
     a76:	05c2                	slli	a1,a1,0x10
     a78:	81c1                	srli	a1,a1,0x10
     a7a:	a90e                	sh	a1,16(a0)
     a7c:	8082                	ret

00000a7e <USART_Init>:
     a7e:	f72ff2ef          	jal	t0,1f0 <__riscv_save_0>
     a82:	2916                	lhu	a3,16(a0)
     a84:	77f5                	lui	a5,0xffffd
     a86:	17fd                	addi	a5,a5,-1
     a88:	8ff5                	and	a5,a5,a3
     a8a:	21f6                	lhu	a3,6(a1)
     a8c:	25da                	lhu	a4,12(a1)
     a8e:	7179                	addi	sp,sp,-48
     a90:	8fd5                	or	a5,a5,a3
     a92:	a91e                	sh	a5,16(a0)
     a94:	2556                	lhu	a3,12(a0)
     a96:	77fd                	lui	a5,0xfffff
     a98:	9f378793          	addi	a5,a5,-1549 # ffffe9f3 <_eusrstack+0xdfff69f3>
     a9c:	8ff5                	and	a5,a5,a3
     a9e:	21d6                	lhu	a3,4(a1)
     aa0:	842a                	mv	s0,a0
     aa2:	c62e                	sw	a1,12(sp)
     aa4:	8fd5                	or	a5,a5,a3
     aa6:	2596                	lhu	a3,8(a1)
     aa8:	8fd5                	or	a5,a5,a3
     aaa:	25b6                	lhu	a3,10(a1)
     aac:	8fd5                	or	a5,a5,a3
     aae:	a55e                	sh	a5,12(a0)
     ab0:	295e                	lhu	a5,20(a0)
     ab2:	07c2                	slli	a5,a5,0x10
     ab4:	83c1                	srli	a5,a5,0x10
     ab6:	cff7f793          	andi	a5,a5,-769
     aba:	8fd9                	or	a5,a5,a4
     abc:	a95e                	sh	a5,20(a0)
     abe:	0868                	addi	a0,sp,28
     ac0:	316d                	jal	76a <RCC_GetClocksFreq>
     ac2:	400147b7          	lui	a5,0x40014
     ac6:	80078793          	addi	a5,a5,-2048 # 40013800 <_eusrstack+0x2000b800>
     aca:	45b2                	lw	a1,12(sp)
     acc:	02f41e63          	bne	s0,a5,b08 <USART_Init+0x8a>
     ad0:	57a2                	lw	a5,40(sp)
     ad2:	4765                	li	a4,25
     ad4:	02e787b3          	mul	a5,a5,a4
     ad8:	4198                	lw	a4,0(a1)
     ada:	06400693          	li	a3,100
     ade:	070a                	slli	a4,a4,0x2
     ae0:	02e7d7b3          	divu	a5,a5,a4
     ae4:	02d7d733          	divu	a4,a5,a3
     ae8:	02d7f7b3          	remu	a5,a5,a3
     aec:	0712                	slli	a4,a4,0x4
     aee:	0792                	slli	a5,a5,0x4
     af0:	03278793          	addi	a5,a5,50
     af4:	02d7d7b3          	divu	a5,a5,a3
     af8:	8bbd                	andi	a5,a5,15
     afa:	8fd9                	or	a5,a5,a4
     afc:	07c2                	slli	a5,a5,0x10
     afe:	83c1                	srli	a5,a5,0x10
     b00:	a41e                	sh	a5,8(s0)
     b02:	6145                	addi	sp,sp,48
     b04:	f10ff06f          	j	214 <__riscv_restore_0>
     b08:	5792                	lw	a5,36(sp)
     b0a:	b7e1                	j	ad2 <USART_Init+0x54>

00000b0c <USART_Cmd>:
     b0c:	c591                	beqz	a1,b18 <USART_Cmd+0xc>
     b0e:	255e                	lhu	a5,12(a0)
     b10:	6709                	lui	a4,0x2
     b12:	8fd9                	or	a5,a5,a4
     b14:	a55e                	sh	a5,12(a0)
     b16:	8082                	ret
     b18:	255a                	lhu	a4,12(a0)
     b1a:	77f9                	lui	a5,0xffffe
     b1c:	17fd                	addi	a5,a5,-1
     b1e:	8ff9                	and	a5,a5,a4
     b20:	bfd5                	j	b14 <USART_Cmd+0x8>

00000b22 <key_init>:
     b22:	eceff2ef          	jal	t0,1f0 <__riscv_save_0>
     b26:	1141                	addi	sp,sp,-16
     b28:	4585                	li	a1,1
     b2a:	02000513          	li	a0,32
     b2e:	3b4d                	jal	8e0 <RCC_APB2PeriphClockCmd>
     b30:	6485                	lui	s1,0x1
     b32:	40011537          	lui	a0,0x40011
     b36:	440d                	li	s0,3
     b38:	a0048793          	addi	a5,s1,-1536 # a00 <TIM_TimeBaseInit+0x7a>
     b3c:	4941                	li	s2,16
     b3e:	004c                	addi	a1,sp,4
     b40:	40050513          	addi	a0,a0,1024 # 40011400 <_eusrstack+0x20009400>
     b44:	827c                	sh	a5,4(sp)
     b46:	c64a                	sw	s2,12(sp)
     b48:	c422                	sw	s0,8(sp)
     b4a:	3ced                	jal	644 <GPIO_Init>
     b4c:	4585                	li	a1,1
     b4e:	04000513          	li	a0,64
     b52:	3379                	jal	8e0 <RCC_APB2PeriphClockCmd>
     b54:	c64a                	sw	s2,12(sp)
     b56:	40012937          	lui	s2,0x40012
     b5a:	77e9                	lui	a5,0xffffa
     b5c:	004c                	addi	a1,sp,4
     b5e:	80090513          	addi	a0,s2,-2048 # 40011800 <_eusrstack+0x20009800>
     b62:	827c                	sh	a5,4(sp)
     b64:	c422                	sw	s0,8(sp)
     b66:	3cf9                	jal	644 <GPIO_Init>
     b68:	4585                	li	a1,1
     b6a:	04000513          	li	a0,64
     b6e:	3b8d                	jal	8e0 <RCC_APB2PeriphClockCmd>
     b70:	a8048493          	addi	s1,s1,-1408
     b74:	8264                	sh	s1,4(sp)
     b76:	004c                	addi	a1,sp,4
     b78:	04800493          	li	s1,72
     b7c:	80090513          	addi	a0,s2,-2048
     b80:	c626                	sw	s1,12(sp)
     b82:	c422                	sw	s0,8(sp)
     b84:	34c1                	jal	644 <GPIO_Init>
     b86:	4585                	li	a1,1
     b88:	4541                	li	a0,16
     b8a:	3b99                	jal	8e0 <RCC_APB2PeriphClockCmd>
     b8c:	02000793          	li	a5,32
     b90:	004c                	addi	a1,sp,4
     b92:	40011537          	lui	a0,0x40011
     b96:	827c                	sh	a5,4(sp)
     b98:	c626                	sw	s1,12(sp)
     b9a:	c422                	sw	s0,8(sp)
     b9c:	3465                	jal	644 <GPIO_Init>
     b9e:	0141                	addi	sp,sp,16
     ba0:	e74ff06f          	j	214 <__riscv_restore_0>

00000ba4 <key_read>:
     ba4:	e4cff2ef          	jal	t0,1f0 <__riscv_save_0>
     ba8:	6405                	lui	s0,0x1
     baa:	400114b7          	lui	s1,0x40011
     bae:	80040593          	addi	a1,s0,-2048 # 800 <__stack_size>
     bb2:	40048513          	addi	a0,s1,1024 # 40011400 <_eusrstack+0x20009400>
     bb6:	3eb1                	jal	712 <GPIO_ResetBits>
     bb8:	40048513          	addi	a0,s1,1024
     bbc:	20000593          	li	a1,512
     bc0:	400124b7          	lui	s1,0x40012
     bc4:	36a9                	jal	70e <GPIO_SetBits>
     bc6:	65a1                	lui	a1,0x8
     bc8:	80048513          	addi	a0,s1,-2048 # 40011800 <_eusrstack+0x20009800>
     bcc:	3689                	jal	70e <GPIO_SetBits>
     bce:	6589                	lui	a1,0x2
     bd0:	80048513          	addi	a0,s1,-2048
     bd4:	3e2d                	jal	70e <GPIO_SetBits>
     bd6:	80040593          	addi	a1,s0,-2048
     bda:	80048513          	addi	a0,s1,-2048
     bde:	361d                	jal	704 <GPIO_ReadInputDataBit>
     be0:	00153413          	seqz	s0,a0
     be4:	20000593          	li	a1,512
     be8:	80048513          	addi	a0,s1,-2048
     bec:	3e21                	jal	704 <GPIO_ReadInputDataBit>
     bee:	040a                	slli	s0,s0,0x2
     bf0:	e111                	bnez	a0,bf4 <key_read+0x50>
     bf2:	440d                	li	s0,3
     bf4:	40012537          	lui	a0,0x40012
     bf8:	08000593          	li	a1,128
     bfc:	80050513          	addi	a0,a0,-2048 # 40011800 <_eusrstack+0x20009800>
     c00:	3611                	jal	704 <GPIO_ReadInputDataBit>
     c02:	e111                	bnez	a0,c06 <key_read+0x62>
     c04:	4409                	li	s0,2
     c06:	02000593          	li	a1,32
     c0a:	40011537          	lui	a0,0x40011
     c0e:	3cdd                	jal	704 <GPIO_ReadInputDataBit>
     c10:	e111                	bnez	a0,c14 <key_read+0x70>
     c12:	4405                	li	s0,1
     c14:	400114b7          	lui	s1,0x40011
     c18:	6905                	lui	s2,0x1
     c1a:	40048513          	addi	a0,s1,1024 # 40011400 <_eusrstack+0x20009400>
     c1e:	80090593          	addi	a1,s2,-2048 # 800 <__stack_size>
     c22:	34f5                	jal	70e <GPIO_SetBits>
     c24:	40048513          	addi	a0,s1,1024
     c28:	20000593          	li	a1,512
     c2c:	34dd                	jal	712 <GPIO_ResetBits>
     c2e:	400124b7          	lui	s1,0x40012
     c32:	65a1                	lui	a1,0x8
     c34:	80048513          	addi	a0,s1,-2048 # 40011800 <_eusrstack+0x20009800>
     c38:	3cd9                	jal	70e <GPIO_SetBits>
     c3a:	6589                	lui	a1,0x2
     c3c:	80048513          	addi	a0,s1,-2048
     c40:	34f9                	jal	70e <GPIO_SetBits>
     c42:	80090593          	addi	a1,s2,-2048
     c46:	80048513          	addi	a0,s1,-2048
     c4a:	3c6d                	jal	704 <GPIO_ReadInputDataBit>
     c4c:	e111                	bnez	a0,c50 <key_read+0xac>
     c4e:	4421                	li	s0,8
     c50:	40012537          	lui	a0,0x40012
     c54:	20000593          	li	a1,512
     c58:	80050513          	addi	a0,a0,-2048 # 40011800 <_eusrstack+0x20009800>
     c5c:	3465                	jal	704 <GPIO_ReadInputDataBit>
     c5e:	e111                	bnez	a0,c62 <key_read+0xbe>
     c60:	441d                	li	s0,7
     c62:	40012537          	lui	a0,0x40012
     c66:	08000593          	li	a1,128
     c6a:	80050513          	addi	a0,a0,-2048 # 40011800 <_eusrstack+0x20009800>
     c6e:	3c59                	jal	704 <GPIO_ReadInputDataBit>
     c70:	e111                	bnez	a0,c74 <key_read+0xd0>
     c72:	4419                	li	s0,6
     c74:	02000593          	li	a1,32
     c78:	40011537          	lui	a0,0x40011
     c7c:	3461                	jal	704 <GPIO_ReadInputDataBit>
     c7e:	e111                	bnez	a0,c82 <key_read+0xde>
     c80:	4415                	li	s0,5
     c82:	400114b7          	lui	s1,0x40011
     c86:	6905                	lui	s2,0x1
     c88:	40048513          	addi	a0,s1,1024 # 40011400 <_eusrstack+0x20009400>
     c8c:	80090593          	addi	a1,s2,-2048 # 800 <__stack_size>
     c90:	3cbd                	jal	70e <GPIO_SetBits>
     c92:	40048513          	addi	a0,s1,1024
     c96:	20000593          	li	a1,512
     c9a:	3c95                	jal	70e <GPIO_SetBits>
     c9c:	400124b7          	lui	s1,0x40012
     ca0:	65a1                	lui	a1,0x8
     ca2:	80048513          	addi	a0,s1,-2048 # 40011800 <_eusrstack+0x20009800>
     ca6:	34b5                	jal	712 <GPIO_ResetBits>
     ca8:	6589                	lui	a1,0x2
     caa:	80048513          	addi	a0,s1,-2048
     cae:	3485                	jal	70e <GPIO_SetBits>
     cb0:	80090593          	addi	a1,s2,-2048
     cb4:	80048513          	addi	a0,s1,-2048
     cb8:	34b1                	jal	704 <GPIO_ReadInputDataBit>
     cba:	e111                	bnez	a0,cbe <key_read+0x11a>
     cbc:	4431                	li	s0,12
     cbe:	40012537          	lui	a0,0x40012
     cc2:	20000593          	li	a1,512
     cc6:	80050513          	addi	a0,a0,-2048 # 40011800 <_eusrstack+0x20009800>
     cca:	3c2d                	jal	704 <GPIO_ReadInputDataBit>
     ccc:	e111                	bnez	a0,cd0 <key_read+0x12c>
     cce:	442d                	li	s0,11
     cd0:	40012537          	lui	a0,0x40012
     cd4:	08000593          	li	a1,128
     cd8:	80050513          	addi	a0,a0,-2048 # 40011800 <_eusrstack+0x20009800>
     cdc:	3425                	jal	704 <GPIO_ReadInputDataBit>
     cde:	e111                	bnez	a0,ce2 <key_read+0x13e>
     ce0:	4429                	li	s0,10
     ce2:	02000593          	li	a1,32
     ce6:	40011537          	lui	a0,0x40011
     cea:	3c29                	jal	704 <GPIO_ReadInputDataBit>
     cec:	e111                	bnez	a0,cf0 <key_read+0x14c>
     cee:	4425                	li	s0,9
     cf0:	400114b7          	lui	s1,0x40011
     cf4:	6905                	lui	s2,0x1
     cf6:	40048513          	addi	a0,s1,1024 # 40011400 <_eusrstack+0x20009400>
     cfa:	80090593          	addi	a1,s2,-2048 # 800 <__stack_size>
     cfe:	3c01                	jal	70e <GPIO_SetBits>
     d00:	40048513          	addi	a0,s1,1024
     d04:	20000593          	li	a1,512
     d08:	3419                	jal	70e <GPIO_SetBits>
     d0a:	400124b7          	lui	s1,0x40012
     d0e:	65a1                	lui	a1,0x8
     d10:	80048513          	addi	a0,s1,-2048 # 40011800 <_eusrstack+0x20009800>
     d14:	3aed                	jal	70e <GPIO_SetBits>
     d16:	6589                	lui	a1,0x2
     d18:	80048513          	addi	a0,s1,-2048
     d1c:	3add                	jal	712 <GPIO_ResetBits>
     d1e:	80090593          	addi	a1,s2,-2048
     d22:	80048513          	addi	a0,s1,-2048
     d26:	3af9                	jal	704 <GPIO_ReadInputDataBit>
     d28:	e111                	bnez	a0,d2c <key_read+0x188>
     d2a:	4441                	li	s0,16
     d2c:	40012537          	lui	a0,0x40012
     d30:	20000593          	li	a1,512
     d34:	80050513          	addi	a0,a0,-2048 # 40011800 <_eusrstack+0x20009800>
     d38:	32f1                	jal	704 <GPIO_ReadInputDataBit>
     d3a:	e111                	bnez	a0,d3e <key_read+0x19a>
     d3c:	443d                	li	s0,15
     d3e:	40012537          	lui	a0,0x40012
     d42:	08000593          	li	a1,128
     d46:	80050513          	addi	a0,a0,-2048 # 40011800 <_eusrstack+0x20009800>
     d4a:	3a6d                	jal	704 <GPIO_ReadInputDataBit>
     d4c:	e111                	bnez	a0,d50 <key_read+0x1ac>
     d4e:	4439                	li	s0,14
     d50:	02000593          	li	a1,32
     d54:	40011537          	lui	a0,0x40011
     d58:	3275                	jal	704 <GPIO_ReadInputDataBit>
     d5a:	e111                	bnez	a0,d5e <key_read+0x1ba>
     d5c:	4435                	li	s0,13
     d5e:	8522                	mv	a0,s0
     d60:	cb4ff06f          	j	214 <__riscv_restore_0>

00000d64 <SPI_LCD_Init>:
     d64:	c72ff2ef          	jal	t0,1d6 <__riscv_save_4>
     d68:	1101                	addi	sp,sp,-32
     d6a:	4585                	li	a1,1
     d6c:	02c00513          	li	a0,44
     d70:	3e85                	jal	8e0 <RCC_APB2PeriphClockCmd>
     d72:	4585                	li	a1,1
     d74:	6521                	lui	a0,0x8
     d76:	40011437          	lui	s0,0x40011
     d7a:	3651                	jal	8fe <RCC_APB1PeriphClockCmd>
     d7c:	03800793          	li	a5,56
     d80:	4941                	li	s2,16
     d82:	448d                	li	s1,3
     d84:	858a                	mv	a1,sp
     d86:	40040513          	addi	a0,s0,1024 # 40011400 <_eusrstack+0x20009400>
     d8a:	807c                	sh	a5,0(sp)
     d8c:	c44a                	sw	s2,8(sp)
     d8e:	c226                	sw	s1,4(sp)
     d90:	8b5ff0ef          	jal	ra,644 <GPIO_Init>
     d94:	40040513          	addi	a0,s0,1024
     d98:	45a1                	li	a1,8
     d9a:	975ff0ef          	jal	ra,70e <GPIO_SetBits>
     d9e:	40040513          	addi	a0,s0,1024
     da2:	45c1                	li	a1,16
     da4:	96bff0ef          	jal	ra,70e <GPIO_SetBits>
     da8:	40040513          	addi	a0,s0,1024
     dac:	02000593          	li	a1,32
     db0:	95fff0ef          	jal	ra,70e <GPIO_SetBits>
     db4:	77e1                	lui	a5,0xffff8
     db6:	858a                	mv	a1,sp
     db8:	80040513          	addi	a0,s0,-2048
     dbc:	807c                	sh	a5,0(sp)
     dbe:	c44a                	sw	s2,8(sp)
     dc0:	c226                	sw	s1,4(sp)
     dc2:	883ff0ef          	jal	ra,644 <GPIO_Init>
     dc6:	80040513          	addi	a0,s0,-2048
     dca:	65a1                	lui	a1,0x8
     dcc:	943ff0ef          	jal	ra,70e <GPIO_SetBits>
     dd0:	47a1                	li	a5,8
     dd2:	49e1                	li	s3,24
     dd4:	858a                	mv	a1,sp
     dd6:	c0040513          	addi	a0,s0,-1024
     dda:	807c                	sh	a5,0(sp)
     ddc:	c44e                	sw	s3,8(sp)
     dde:	c226                	sw	s1,4(sp)
     de0:	865ff0ef          	jal	ra,644 <GPIO_Init>
     de4:	858a                	mv	a1,sp
     de6:	c0040513          	addi	a0,s0,-1024
     dea:	01211023          	sh	s2,0(sp)
     dee:	c44a                	sw	s2,8(sp)
     df0:	855ff0ef          	jal	ra,644 <GPIO_Init>
     df4:	02000793          	li	a5,32
     df8:	858a                	mv	a1,sp
     dfa:	c0040513          	addi	a0,s0,-1024
     dfe:	807c                	sh	a5,0(sp)
     e00:	c44e                	sw	s3,8(sp)
     e02:	c226                	sw	s1,4(sp)
     e04:	841ff0ef          	jal	ra,644 <GPIO_Init>
     e08:	010407b7          	lui	a5,0x1040
     e0c:	c63e                	sw	a5,12(sp)
     e0e:	020007b7          	lui	a5,0x2000
     e12:	40004437          	lui	s0,0x40004
     e16:	ca3e                	sw	a5,20(sp)
     e18:	47a1                	li	a5,8
     e1a:	cc3e                	sw	a5,24(sp)
     e1c:	006c                	addi	a1,sp,12
     e1e:	479d                	li	a5,7
     e20:	c0040513          	addi	a0,s0,-1024 # 40003c00 <_eusrstack+0x1fffbc00>
     e24:	86fc                	sh	a5,28(sp)
     e26:	c802                	sw	zero,16(sp)
     e28:	3cd5                	jal	91c <SPI_Init>
     e2a:	4585                	li	a1,1
     e2c:	c0040513          	addi	a0,s0,-1024
     e30:	362d                	jal	95a <SPI_Cmd>
     e32:	6105                	addi	sp,sp,32
     e34:	bd6ff06f          	j	20a <__riscv_restore_4>

00000e38 <SPI3_IRQHandler>:
     e38:	30200073          	mret

00000e3c <spi_readwrite>:
     e3c:	b9aff2ef          	jal	t0,1d6 <__riscv_save_4>
     e40:	400044b7          	lui	s1,0x40004
     e44:	892a                	mv	s2,a0
     e46:	0c900413          	li	s0,201
     e4a:	c0048993          	addi	s3,s1,-1024 # 40003c00 <_eusrstack+0x1fffbc00>
     e4e:	4589                	li	a1,2
     e50:	854e                	mv	a0,s3
     e52:	362d                	jal	97c <SPI_I2S_GetFlagStatus>
     e54:	c50d                	beqz	a0,e7e <spi_readwrite+0x42>
     e56:	85ca                	mv	a1,s2
     e58:	c0048513          	addi	a0,s1,-1024
     e5c:	400044b7          	lui	s1,0x40004
     e60:	3e11                	jal	974 <SPI_I2S_SendData>
     e62:	0c900413          	li	s0,201
     e66:	c0048913          	addi	s2,s1,-1024 # 40003c00 <_eusrstack+0x1fffbc00>
     e6a:	4585                	li	a1,1
     e6c:	854a                	mv	a0,s2
     e6e:	3639                	jal	97c <SPI_I2S_GetFlagStatus>
     e70:	cd11                	beqz	a0,e8c <spi_readwrite+0x50>
     e72:	c0048513          	addi	a0,s1,-1024
     e76:	3609                	jal	978 <SPI_I2S_ReceiveData>
     e78:	0ff57513          	andi	a0,a0,255
     e7c:	a031                	j	e88 <spi_readwrite+0x4c>
     e7e:	147d                	addi	s0,s0,-1
     e80:	0ff47413          	andi	s0,s0,255
     e84:	f469                	bnez	s0,e4e <spi_readwrite+0x12>
     e86:	4501                	li	a0,0
     e88:	b82ff06f          	j	20a <__riscv_restore_4>
     e8c:	147d                	addi	s0,s0,-1
     e8e:	0ff47413          	andi	s0,s0,255
     e92:	fc61                	bnez	s0,e6a <spi_readwrite+0x2e>
     e94:	bfcd                	j	e86 <spi_readwrite+0x4a>

00000e96 <LCD_WR_DATA8>:
     e96:	b5aff2ef          	jal	t0,1f0 <__riscv_save_0>
     e9a:	40011437          	lui	s0,0x40011
     e9e:	84aa                	mv	s1,a0
     ea0:	45c1                	li	a1,16
     ea2:	40040513          	addi	a0,s0,1024 # 40011400 <_eusrstack+0x20009400>
     ea6:	86dff0ef          	jal	ra,712 <GPIO_ResetBits>
     eaa:	45a1                	li	a1,8
     eac:	40040513          	addi	a0,s0,1024
     eb0:	85fff0ef          	jal	ra,70e <GPIO_SetBits>
     eb4:	8526                	mv	a0,s1
     eb6:	3759                	jal	e3c <spi_readwrite>
     eb8:	45c1                	li	a1,16
     eba:	40040513          	addi	a0,s0,1024
     ebe:	851ff0ef          	jal	ra,70e <GPIO_SetBits>
     ec2:	b52ff06f          	j	214 <__riscv_restore_0>

00000ec6 <LCD_WR_DATA>:
     ec6:	b2aff2ef          	jal	t0,1f0 <__riscv_save_0>
     eca:	40011437          	lui	s0,0x40011
     ece:	84aa                	mv	s1,a0
     ed0:	45c1                	li	a1,16
     ed2:	40040513          	addi	a0,s0,1024 # 40011400 <_eusrstack+0x20009400>
     ed6:	83dff0ef          	jal	ra,712 <GPIO_ResetBits>
     eda:	45a1                	li	a1,8
     edc:	40040513          	addi	a0,s0,1024
     ee0:	82fff0ef          	jal	ra,70e <GPIO_SetBits>
     ee4:	0084d513          	srli	a0,s1,0x8
     ee8:	3f91                	jal	e3c <spi_readwrite>
     eea:	0ff4f513          	andi	a0,s1,255
     eee:	37b9                	jal	e3c <spi_readwrite>
     ef0:	45c1                	li	a1,16
     ef2:	40040513          	addi	a0,s0,1024
     ef6:	819ff0ef          	jal	ra,70e <GPIO_SetBits>
     efa:	b1aff06f          	j	214 <__riscv_restore_0>

00000efe <LCD_WR_REG>:
     efe:	af2ff2ef          	jal	t0,1f0 <__riscv_save_0>
     f02:	40011437          	lui	s0,0x40011
     f06:	84aa                	mv	s1,a0
     f08:	45c1                	li	a1,16
     f0a:	40040513          	addi	a0,s0,1024 # 40011400 <_eusrstack+0x20009400>
     f0e:	805ff0ef          	jal	ra,712 <GPIO_ResetBits>
     f12:	45a1                	li	a1,8
     f14:	40040513          	addi	a0,s0,1024
     f18:	ffaff0ef          	jal	ra,712 <GPIO_ResetBits>
     f1c:	8526                	mv	a0,s1
     f1e:	3f39                	jal	e3c <spi_readwrite>
     f20:	45c1                	li	a1,16
     f22:	40040513          	addi	a0,s0,1024
     f26:	fe8ff0ef          	jal	ra,70e <GPIO_SetBits>
     f2a:	aeaff06f          	j	214 <__riscv_restore_0>

00000f2e <LCD_Address_Set>:
     f2e:	ac2ff2ef          	jal	t0,1f0 <__riscv_save_0>
     f32:	1141                	addi	sp,sp,-16
     f34:	842a                	mv	s0,a0
     f36:	02a00513          	li	a0,42
     f3a:	c236                	sw	a3,4(sp)
     f3c:	c62e                	sw	a1,12(sp)
     f3e:	c432                	sw	a2,8(sp)
     f40:	3f7d                	jal	efe <LCD_WR_REG>
     f42:	00240513          	addi	a0,s0,2
     f46:	0542                	slli	a0,a0,0x10
     f48:	8141                	srli	a0,a0,0x10
     f4a:	3fb5                	jal	ec6 <LCD_WR_DATA>
     f4c:	4622                	lw	a2,8(sp)
     f4e:	0609                	addi	a2,a2,2
     f50:	01061513          	slli	a0,a2,0x10
     f54:	8141                	srli	a0,a0,0x10
     f56:	3f85                	jal	ec6 <LCD_WR_DATA>
     f58:	02b00513          	li	a0,43
     f5c:	374d                	jal	efe <LCD_WR_REG>
     f5e:	45b2                	lw	a1,12(sp)
     f60:	058d                	addi	a1,a1,3
     f62:	01059513          	slli	a0,a1,0x10
     f66:	8141                	srli	a0,a0,0x10
     f68:	3fb9                	jal	ec6 <LCD_WR_DATA>
     f6a:	4692                	lw	a3,4(sp)
     f6c:	068d                	addi	a3,a3,3
     f6e:	01069513          	slli	a0,a3,0x10
     f72:	8141                	srli	a0,a0,0x10
     f74:	3f89                	jal	ec6 <LCD_WR_DATA>
     f76:	02c00513          	li	a0,44
     f7a:	3751                	jal	efe <LCD_WR_REG>
     f7c:	0141                	addi	sp,sp,16
     f7e:	a96ff06f          	j	214 <__riscv_restore_0>

00000f82 <LCD_Init>:
     f82:	a6eff2ef          	jal	t0,1f0 <__riscv_save_0>
     f86:	3bf9                	jal	d64 <SPI_LCD_Init>
     f88:	40011437          	lui	s0,0x40011
     f8c:	45c1                	li	a1,16
     f8e:	c0040513          	addi	a0,s0,-1024 # 40010c00 <_eusrstack+0x20008c00>
     f92:	f80ff0ef          	jal	ra,712 <GPIO_ResetBits>
     f96:	06400513          	li	a0,100
     f9a:	2e05                	jal	12ca <Delay_Ms>
     f9c:	45c1                	li	a1,16
     f9e:	c0040513          	addi	a0,s0,-1024
     fa2:	f6cff0ef          	jal	ra,70e <GPIO_SetBits>
     fa6:	06400513          	li	a0,100
     faa:	2605                	jal	12ca <Delay_Ms>
     fac:	02000593          	li	a1,32
     fb0:	40040513          	addi	a0,s0,1024
     fb4:	f5aff0ef          	jal	ra,70e <GPIO_SetBits>
     fb8:	06400513          	li	a0,100
     fbc:	2639                	jal	12ca <Delay_Ms>
     fbe:	4545                	li	a0,17
     fc0:	3f3d                	jal	efe <LCD_WR_REG>
     fc2:	07800513          	li	a0,120
     fc6:	2611                	jal	12ca <Delay_Ms>
     fc8:	0b100513          	li	a0,177
     fcc:	3f0d                	jal	efe <LCD_WR_REG>
     fce:	4515                	li	a0,5
     fd0:	35d9                	jal	e96 <LCD_WR_DATA8>
     fd2:	03c00513          	li	a0,60
     fd6:	35c1                	jal	e96 <LCD_WR_DATA8>
     fd8:	03c00513          	li	a0,60
     fdc:	3d6d                	jal	e96 <LCD_WR_DATA8>
     fde:	0b200513          	li	a0,178
     fe2:	3f31                	jal	efe <LCD_WR_REG>
     fe4:	4515                	li	a0,5
     fe6:	3d45                	jal	e96 <LCD_WR_DATA8>
     fe8:	03c00513          	li	a0,60
     fec:	356d                	jal	e96 <LCD_WR_DATA8>
     fee:	03c00513          	li	a0,60
     ff2:	3555                	jal	e96 <LCD_WR_DATA8>
     ff4:	0b300513          	li	a0,179
     ff8:	3719                	jal	efe <LCD_WR_REG>
     ffa:	4515                	li	a0,5
     ffc:	3d69                	jal	e96 <LCD_WR_DATA8>
     ffe:	03c00513          	li	a0,60
    1002:	3d51                	jal	e96 <LCD_WR_DATA8>
    1004:	03c00513          	li	a0,60
    1008:	3579                	jal	e96 <LCD_WR_DATA8>
    100a:	4515                	li	a0,5
    100c:	3569                	jal	e96 <LCD_WR_DATA8>
    100e:	03c00513          	li	a0,60
    1012:	3551                	jal	e96 <LCD_WR_DATA8>
    1014:	03c00513          	li	a0,60
    1018:	3dbd                	jal	e96 <LCD_WR_DATA8>
    101a:	0b400513          	li	a0,180
    101e:	35c5                	jal	efe <LCD_WR_REG>
    1020:	450d                	li	a0,3
    1022:	3d95                	jal	e96 <LCD_WR_DATA8>
    1024:	03a00513          	li	a0,58
    1028:	3dd9                	jal	efe <LCD_WR_REG>
    102a:	4515                	li	a0,5
    102c:	35ad                	jal	e96 <LCD_WR_DATA8>
    102e:	0c000513          	li	a0,192
    1032:	35f1                	jal	efe <LCD_WR_REG>
    1034:	0a200513          	li	a0,162
    1038:	3db9                	jal	e96 <LCD_WR_DATA8>
    103a:	4509                	li	a0,2
    103c:	3da9                	jal	e96 <LCD_WR_DATA8>
    103e:	08400513          	li	a0,132
    1042:	3d91                	jal	e96 <LCD_WR_DATA8>
    1044:	0c100513          	li	a0,193
    1048:	3d5d                	jal	efe <LCD_WR_REG>
    104a:	0c500513          	li	a0,197
    104e:	35a1                	jal	e96 <LCD_WR_DATA8>
    1050:	0c200513          	li	a0,194
    1054:	356d                	jal	efe <LCD_WR_REG>
    1056:	4535                	li	a0,13
    1058:	3d3d                	jal	e96 <LCD_WR_DATA8>
    105a:	4501                	li	a0,0
    105c:	3d2d                	jal	e96 <LCD_WR_DATA8>
    105e:	0c300513          	li	a0,195
    1062:	3d71                	jal	efe <LCD_WR_REG>
    1064:	08d00513          	li	a0,141
    1068:	353d                	jal	e96 <LCD_WR_DATA8>
    106a:	02a00513          	li	a0,42
    106e:	3525                	jal	e96 <LCD_WR_DATA8>
    1070:	0c400513          	li	a0,196
    1074:	3569                	jal	efe <LCD_WR_REG>
    1076:	08d00513          	li	a0,141
    107a:	3d31                	jal	e96 <LCD_WR_DATA8>
    107c:	0ee00513          	li	a0,238
    1080:	3d19                	jal	e96 <LCD_WR_DATA8>
    1082:	0c500513          	li	a0,197
    1086:	3da5                	jal	efe <LCD_WR_REG>
    1088:	4529                	li	a0,10
    108a:	3531                	jal	e96 <LCD_WR_DATA8>
    108c:	03600513          	li	a0,54
    1090:	35bd                	jal	efe <LCD_WR_REG>
    1092:	0c800513          	li	a0,200
    1096:	3501                	jal	e96 <LCD_WR_DATA8>
    1098:	0e000513          	li	a0,224
    109c:	358d                	jal	efe <LCD_WR_REG>
    109e:	4549                	li	a0,18
    10a0:	3bdd                	jal	e96 <LCD_WR_DATA8>
    10a2:	4571                	li	a0,28
    10a4:	3bcd                	jal	e96 <LCD_WR_DATA8>
    10a6:	4541                	li	a0,16
    10a8:	33fd                	jal	e96 <LCD_WR_DATA8>
    10aa:	4561                	li	a0,24
    10ac:	33ed                	jal	e96 <LCD_WR_DATA8>
    10ae:	03300513          	li	a0,51
    10b2:	33d5                	jal	e96 <LCD_WR_DATA8>
    10b4:	02c00513          	li	a0,44
    10b8:	3bf9                	jal	e96 <LCD_WR_DATA8>
    10ba:	02500513          	li	a0,37
    10be:	3be1                	jal	e96 <LCD_WR_DATA8>
    10c0:	02800513          	li	a0,40
    10c4:	3bc9                	jal	e96 <LCD_WR_DATA8>
    10c6:	02800513          	li	a0,40
    10ca:	33f1                	jal	e96 <LCD_WR_DATA8>
    10cc:	02700513          	li	a0,39
    10d0:	33d9                	jal	e96 <LCD_WR_DATA8>
    10d2:	02f00513          	li	a0,47
    10d6:	33c1                	jal	e96 <LCD_WR_DATA8>
    10d8:	03c00513          	li	a0,60
    10dc:	3b6d                	jal	e96 <LCD_WR_DATA8>
    10de:	4501                	li	a0,0
    10e0:	3b5d                	jal	e96 <LCD_WR_DATA8>
    10e2:	450d                	li	a0,3
    10e4:	3b4d                	jal	e96 <LCD_WR_DATA8>
    10e6:	450d                	li	a0,3
    10e8:	337d                	jal	e96 <LCD_WR_DATA8>
    10ea:	4541                	li	a0,16
    10ec:	336d                	jal	e96 <LCD_WR_DATA8>
    10ee:	0e100513          	li	a0,225
    10f2:	3531                	jal	efe <LCD_WR_REG>
    10f4:	4549                	li	a0,18
    10f6:	3345                	jal	e96 <LCD_WR_DATA8>
    10f8:	4571                	li	a0,28
    10fa:	3b71                	jal	e96 <LCD_WR_DATA8>
    10fc:	4541                	li	a0,16
    10fe:	3b61                	jal	e96 <LCD_WR_DATA8>
    1100:	4561                	li	a0,24
    1102:	3b51                	jal	e96 <LCD_WR_DATA8>
    1104:	02d00513          	li	a0,45
    1108:	3379                	jal	e96 <LCD_WR_DATA8>
    110a:	02800513          	li	a0,40
    110e:	3361                	jal	e96 <LCD_WR_DATA8>
    1110:	02300513          	li	a0,35
    1114:	3349                	jal	e96 <LCD_WR_DATA8>
    1116:	02800513          	li	a0,40
    111a:	3bb5                	jal	e96 <LCD_WR_DATA8>
    111c:	02800513          	li	a0,40
    1120:	3b9d                	jal	e96 <LCD_WR_DATA8>
    1122:	02600513          	li	a0,38
    1126:	3b85                	jal	e96 <LCD_WR_DATA8>
    1128:	02f00513          	li	a0,47
    112c:	33ad                	jal	e96 <LCD_WR_DATA8>
    112e:	03b00513          	li	a0,59
    1132:	3395                	jal	e96 <LCD_WR_DATA8>
    1134:	4501                	li	a0,0
    1136:	3385                	jal	e96 <LCD_WR_DATA8>
    1138:	450d                	li	a0,3
    113a:	3bb1                	jal	e96 <LCD_WR_DATA8>
    113c:	450d                	li	a0,3
    113e:	3ba1                	jal	e96 <LCD_WR_DATA8>
    1140:	4541                	li	a0,16
    1142:	3b91                	jal	e96 <LCD_WR_DATA8>
    1144:	02000513          	li	a0,32
    1148:	3b5d                	jal	efe <LCD_WR_REG>
    114a:	454d                	li	a0,19
    114c:	3b4d                	jal	efe <LCD_WR_REG>
    114e:	02a00513          	li	a0,42
    1152:	3375                	jal	efe <LCD_WR_REG>
    1154:	4501                	li	a0,0
    1156:	3381                	jal	e96 <LCD_WR_DATA8>
    1158:	4501                	li	a0,0
    115a:	3b35                	jal	e96 <LCD_WR_DATA8>
    115c:	4501                	li	a0,0
    115e:	3b25                	jal	e96 <LCD_WR_DATA8>
    1160:	07f00513          	li	a0,127
    1164:	3b0d                	jal	e96 <LCD_WR_DATA8>
    1166:	02b00513          	li	a0,43
    116a:	3b51                	jal	efe <LCD_WR_REG>
    116c:	4501                	li	a0,0
    116e:	3325                	jal	e96 <LCD_WR_DATA8>
    1170:	4501                	li	a0,0
    1172:	3315                	jal	e96 <LCD_WR_DATA8>
    1174:	4501                	li	a0,0
    1176:	3305                	jal	e96 <LCD_WR_DATA8>
    1178:	07f00513          	li	a0,127
    117c:	3b29                	jal	e96 <LCD_WR_DATA8>
    117e:	02900513          	li	a0,41
    1182:	3bb5                	jal	efe <LCD_WR_REG>
    1184:	890ff06f          	j	214 <__riscv_restore_0>

00001188 <LCD_Fill>:
    1188:	84eff2ef          	jal	t0,1d6 <__riscv_save_4>
    118c:	89b2                	mv	s3,a2
    118e:	8936                	mv	s2,a3
    1190:	167d                	addi	a2,a2,-1
    1192:	16fd                	addi	a3,a3,-1
    1194:	06c2                	slli	a3,a3,0x10
    1196:	0642                	slli	a2,a2,0x10
    1198:	82c1                	srli	a3,a3,0x10
    119a:	8241                	srli	a2,a2,0x10
    119c:	8a2a                	mv	s4,a0
    119e:	842e                	mv	s0,a1
    11a0:	8aba                	mv	s5,a4
    11a2:	3371                	jal	f2e <LCD_Address_Set>
    11a4:	01246f63          	bltu	s0,s2,11c2 <LCD_Fill+0x3a>
    11a8:	862ff06f          	j	20a <__riscv_restore_4>
    11ac:	0485                	addi	s1,s1,1
    11ae:	8556                	mv	a0,s5
    11b0:	04c2                	slli	s1,s1,0x10
    11b2:	3b11                	jal	ec6 <LCD_WR_DATA>
    11b4:	80c1                	srli	s1,s1,0x10
    11b6:	ff34ebe3          	bltu	s1,s3,11ac <LCD_Fill+0x24>
    11ba:	0405                	addi	s0,s0,1
    11bc:	0442                	slli	s0,s0,0x10
    11be:	8041                	srli	s0,s0,0x10
    11c0:	b7d5                	j	11a4 <LCD_Fill+0x1c>
    11c2:	84d2                	mv	s1,s4
    11c4:	bfcd                	j	11b6 <LCD_Fill+0x2e>

000011c6 <delay_us>:
    11c6:	050e                	slli	a0,a0,0x3
    11c8:	4781                	li	a5,0
    11ca:	00f51363          	bne	a0,a5,11d0 <delay_us+0xa>
    11ce:	8082                	ret
    11d0:	0001                	nop
    11d2:	0785                	addi	a5,a5,1
    11d4:	bfdd                	j	11ca <delay_us+0x4>

000011d6 <delay_ms>:
    11d6:	81aff2ef          	jal	t0,1f0 <__riscv_save_0>
    11da:	84aa                	mv	s1,a0
    11dc:	4401                	li	s0,0
    11de:	00941463          	bne	s0,s1,11e6 <delay_ms+0x10>
    11e2:	832ff06f          	j	214 <__riscv_restore_0>
    11e6:	3e800513          	li	a0,1000
    11ea:	3ff1                	jal	11c6 <delay_us>
    11ec:	0405                	addi	s0,s0,1
    11ee:	bfc5                	j	11de <delay_ms+0x8>

000011f0 <WaterPump_Init>:
    11f0:	800ff2ef          	jal	t0,1f0 <__riscv_save_0>
    11f4:	1141                	addi	sp,sp,-16
    11f6:	4585                	li	a1,1
    11f8:	4541                	li	a0,16
    11fa:	ee6ff0ef          	jal	ra,8e0 <RCC_APB2PeriphClockCmd>
    11fe:	6789                	lui	a5,0x2
    1200:	440d                	li	s0,3
    1202:	44c1                	li	s1,16
    1204:	004c                	addi	a1,sp,4
    1206:	40011537          	lui	a0,0x40011
    120a:	827c                	sh	a5,4(sp)
    120c:	c626                	sw	s1,12(sp)
    120e:	c422                	sw	s0,8(sp)
    1210:	c34ff0ef          	jal	ra,644 <GPIO_Init>
    1214:	6589                	lui	a1,0x2
    1216:	40011537          	lui	a0,0x40011
    121a:	cf8ff0ef          	jal	ra,712 <GPIO_ResetBits>
    121e:	4585                	li	a1,1
    1220:	4541                	li	a0,16
    1222:	ebeff0ef          	jal	ra,8e0 <RCC_APB2PeriphClockCmd>
    1226:	6785                	lui	a5,0x1
    1228:	80078793          	addi	a5,a5,-2048 # 800 <__stack_size>
    122c:	004c                	addi	a1,sp,4
    122e:	40011537          	lui	a0,0x40011
    1232:	827c                	sh	a5,4(sp)
    1234:	c626                	sw	s1,12(sp)
    1236:	c422                	sw	s0,8(sp)
    1238:	c0cff0ef          	jal	ra,644 <GPIO_Init>
    123c:	6585                	lui	a1,0x1
    123e:	80058593          	addi	a1,a1,-2048 # 800 <__stack_size>
    1242:	40011537          	lui	a0,0x40011
    1246:	cccff0ef          	jal	ra,712 <GPIO_ResetBits>
    124a:	81c18793          	addi	a5,gp,-2020 # 20000064 <g_pump_ctrl>
    124e:	4705                	li	a4,1
    1250:	0007a023          	sw	zero,0(a5)
    1254:	a3da                	sh	a4,4(a5)
    1256:	0007a423          	sw	zero,8(a5)
    125a:	0007a623          	sw	zero,12(a5)
    125e:	0007a823          	sw	zero,16(a5)
    1262:	abda                	sh	a4,20(a5)
    1264:	0007ac23          	sw	zero,24(a5)
    1268:	0007ae23          	sw	zero,28(a5)
    126c:	0141                	addi	sp,sp,16
    126e:	fa7fe06f          	j	214 <__riscv_restore_0>

00001272 <WaterPump_Control>:
    1272:	4705                	li	a4,1
    1274:	04a76a63          	bltu	a4,a0,12c8 <WaterPump_Control+0x56>
    1278:	f79fe2ef          	jal	t0,1f0 <__riscv_save_0>
    127c:	81c18793          	addi	a5,gp,-2020 # 20000064 <g_pump_ctrl>
    1280:	00451493          	slli	s1,a0,0x4
    1284:	97a6                	add	a5,a5,s1
    1286:	23dc                	lbu	a5,4(a5)
    1288:	81c18413          	addi	s0,gp,-2020 # 20000064 <g_pump_ctrl>
    128c:	cf99                	beqz	a5,12aa <WaterPump_Control+0x38>
    128e:	02e59063          	bne	a1,a4,12ae <WaterPump_Control+0x3c>
    1292:	6589                	lui	a1,0x2
    1294:	c501                	beqz	a0,129c <WaterPump_Control+0x2a>
    1296:	6585                	lui	a1,0x1
    1298:	80058593          	addi	a1,a1,-2048 # 800 <__stack_size>
    129c:	40011537          	lui	a0,0x40011
    12a0:	c6eff0ef          	jal	ra,70e <GPIO_SetBits>
    12a4:	9426                	add	s0,s0,s1
    12a6:	4785                	li	a5,1
    12a8:	c01c                	sw	a5,0(s0)
    12aa:	f6bfe06f          	j	214 <__riscv_restore_0>
    12ae:	6589                	lui	a1,0x2
    12b0:	c501                	beqz	a0,12b8 <WaterPump_Control+0x46>
    12b2:	6585                	lui	a1,0x1
    12b4:	80058593          	addi	a1,a1,-2048 # 800 <__stack_size>
    12b8:	40011537          	lui	a0,0x40011
    12bc:	9426                	add	s0,s0,s1
    12be:	c54ff0ef          	jal	ra,712 <GPIO_ResetBits>
    12c2:	00042023          	sw	zero,0(s0)
    12c6:	b7d5                	j	12aa <WaterPump_Control+0x38>
    12c8:	8082                	ret

000012ca <Delay_Ms>:
    12ca:	e000f7b7          	lui	a5,0xe000f
    12ce:	43d8                	lw	a4,4(a5)
    12d0:	4681                	li	a3,0
    12d2:	9b79                	andi	a4,a4,-2
    12d4:	c3d8                	sw	a4,4(a5)
    12d6:	8181d703          	lhu	a4,-2024(gp) # 20000060 <p_ms>
    12da:	02a70633          	mul	a2,a4,a0
    12de:	cb90                	sw	a2,16(a5)
    12e0:	cbd4                	sw	a3,20(a5)
    12e2:	4398                	lw	a4,0(a5)
    12e4:	01076713          	ori	a4,a4,16
    12e8:	c398                	sw	a4,0(a5)
    12ea:	4398                	lw	a4,0(a5)
    12ec:	02176713          	ori	a4,a4,33
    12f0:	c398                	sw	a4,0(a5)
    12f2:	43d8                	lw	a4,4(a5)
    12f4:	8b05                	andi	a4,a4,1
    12f6:	df75                	beqz	a4,12f2 <Delay_Ms+0x28>
    12f8:	4398                	lw	a4,0(a5)
    12fa:	9b79                	andi	a4,a4,-2
    12fc:	c398                	sw	a4,0(a5)
    12fe:	8082                	ret

00001300 <USART_Printf_Init>:
    1300:	ef1fe2ef          	jal	t0,1f0 <__riscv_save_0>
    1304:	842a                	mv	s0,a0
    1306:	6511                	lui	a0,0x4
    1308:	1101                	addi	sp,sp,-32
    130a:	4585                	li	a1,1
    130c:	0511                	addi	a0,a0,4
    130e:	dd2ff0ef          	jal	ra,8e0 <RCC_APB2PeriphClockCmd>
    1312:	20000793          	li	a5,512
    1316:	827c                	sh	a5,4(sp)
    1318:	40011537          	lui	a0,0x40011
    131c:	478d                	li	a5,3
    131e:	c43e                	sw	a5,8(sp)
    1320:	004c                	addi	a1,sp,4
    1322:	47e1                	li	a5,24
    1324:	80050513          	addi	a0,a0,-2048 # 40010800 <_eusrstack+0x20008800>
    1328:	c63e                	sw	a5,12(sp)
    132a:	b1aff0ef          	jal	ra,644 <GPIO_Init>
    132e:	c822                	sw	s0,16(sp)
    1330:	40014437          	lui	s0,0x40014
    1334:	000807b7          	lui	a5,0x80
    1338:	080c                	addi	a1,sp,16
    133a:	80040513          	addi	a0,s0,-2048 # 40013800 <_eusrstack+0x2000b800>
    133e:	cc3e                	sw	a5,24(sp)
    1340:	ca02                	sw	zero,20(sp)
    1342:	00011e23          	sh	zero,28(sp)
    1346:	f38ff0ef          	jal	ra,a7e <USART_Init>
    134a:	4585                	li	a1,1
    134c:	80040513          	addi	a0,s0,-2048
    1350:	fbcff0ef          	jal	ra,b0c <USART_Cmd>
    1354:	6105                	addi	sp,sp,32
    1356:	ebffe06f          	j	214 <__riscv_restore_0>
	...
