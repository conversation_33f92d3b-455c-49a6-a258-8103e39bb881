/*头文件包含区*/
#include "debug.h"            //主要系统头文件
#include "lcd.h"              //屏幕头文件
#include "pic.h"              //图片头文件
#include "timer.h"            //定时器头文件（舵机）
#include "key.h"              //矩阵按键头文件
#include "string.h"           //字符串处理相关函数头文件
#include "stdio.h"

/*新增的头文件*/
#include "ds18b20.h"          //温度传感器驱动头文件
#include "heater.h"           //加热片驱动器驱动头文件
#include "water_pump.h"       //水泵驱动器驱动头文件
#include "water_level.h"      //检水位传感器驱动头文件
#include "brewing_control.h"  //冲泡控制模块头文件


// USART_SendData(USART5, 01);//简单的思路，实际不建议，太危险了，如01-舵机到1度，255--舵机到255度,但是可以这样也可以这样写，这样不会影响其他实际简单的操作流程
//---通过数据包的方式，一次发送多个数据，比如说一个什么什么数据包，要放到一个数组里面，这样这样这样一个模块，这样就不会取错



/*函数声明区*/
/*之前的函数*/
void TIM3_IRQHandler(void) __attribute__((interrupt("WCH-Interrupt-fast")));//定时器3中断函数

/*新增的函数*/
void TempAlarmHandler(float temp);//温度报警回调（这里用户自定义，比如点亮LED或者其他）


/*全局变量区*/
unsigned long int uwtick;               //系统计时变量，每一毫秒+1，这个定时器来源于第一个定时器TIM3中断毫秒
u8 key_val,key_down,key_up,key_old;     //按键相关变量
u8 p;                                   //p变量(既按键上变化，也可以下变化)

/*新增的变量*/
//ds18b20
DS18B20_HandleTypeDef ds18b20;          //温度传感器句柄
float threshold = 40.0f; // 设定报警阈值为40度

u8 water_lvel_flag;//水位检测标志位0-未到指定水位位置，1-到达指定水位位置


/*按键处理函数*/
void key_proc()//每隔10ms读取一次，这是为了防止10ms的机械抖动
{
    //检测按键变化
    key_val=key_read();
    p=key_val^key_old;
    key_down=key_val&(key_val^key_old);
    key_up=~key_val&(key_val^key_old);
    key_old=key_val;

    // 冲泡控制按键处理
    if(key_down) {
        BrewingControl_KeyHandler(key_down);
    }
}

/*屏幕处理函数*/
void lcd_proc()//这里可以添加屏幕显示内容
{

}

void lock_proc()//门锁处理函数,lock_flag=0-开锁，1-关锁
{
    //lock(lock_flag);//控制门锁

}

/*温度处理函数*/
void temp_proc()
{
   uint8_t status;
   status = DS18B20_ReadRealtimeTemp(&ds18b20);
   if(status == DS18B20_OK)
   {
       printf("当前温度：%.2f 度\n", ds18b20.current_temp);
       LCD_ShowFloatNum1(1, 1, ds18b20.current_temp, 2, RED, WHITE, 16);
   }
   else
   {
       printf("温度读取错误\n");
       LCD_ShowString(1, 1, "Temp Error", RED, WHITE, 16, 0);
   }
}

void water_level_proc()
{
    if (WaterLevel_Detect())
   {
       // 检测到水位
       // 可以点亮LED或者其他操作
        water_lvel_flag=1;
   }
   else
   {
       // 未检测到水位
       // 可以熄灭LED或者其他操作
       water_lvel_flag=0;
   }
    printf("当前水位检测状态：%d\n", water_lvel_flag);
}



/*定时器3初始化函数（用于系统计时）*///这个定时器用于uwtick)
void Tim3_Init(u16 arr,u16 psc)//定时器3初始化函数，用于系统计时     这个定时器用于系统计时变量
{
    TIM_TimeBaseInitTypeDef TIM_TimeBaseInitStruct;//定时器初始化结构体
    NVIC_InitTypeDef NVIC_InitStructure;//中断NVIC结构体
    RCC_APB1PeriphClockCmd(RCC_APB1Periph_TIM3, ENABLE);//***开启timer3时钟，这个是必须的

    TIM_TimeBaseInitStruct.TIM_ClockDivision=0;//时钟分频
    TIM_TimeBaseInitStruct.TIM_CounterMode=TIM_CounterMode_Up;//向上计数模式
    TIM_TimeBaseInitStruct.TIM_Period=arr;//自动重装载值5000表示500ms
    TIM_TimeBaseInitStruct.TIM_Prescaler=psc;//预分频值

    TIM_TimeBaseInit(TIM3,&TIM_TimeBaseInitStruct);//***
    TIM_ITConfig(TIM3, TIM_IT_Update|TIM_IT_Trigger, ENABLE);//***

    NVIC_InitStructure.NVIC_IRQChannel=TIM3_IRQn;//***中断通道选择-定时器3中断
    NVIC_InitStructure.NVIC_IRQChannelPreemptionPriority=1;
    NVIC_InitStructure.NVIC_IRQChannelSubPriority=3;
    NVIC_InitStructure.NVIC_IRQChannelCmd=ENABLE;
    NVIC_Init(&NVIC_InitStructure);
    TIM_Cmd(TIM3, ENABLE);//***
}

/*定时器3中断函数，每1ms执行一次*/
void TIM3_IRQHandler(void)//中断服务函数
{
    if(TIM_GetITStatus(TIM3, TIM_IT_Update)!=RESET)
    {
      uwtick++;    //系统计时变量自增



    }
    TIM_ClearITPendingBit(TIM3, TIM_IT_Update);
}

/*任务调度器*/
typedef struct{
    void (*task_func)(void);//任务函数
    unsigned long int rate_ms;//任务执行周期
    unsigned long int last_run;//上次执行时间
}task_t;
task_t scheduler_task_t[]={
        {lcd_proc,100,0},   //屏幕处理任务，100ms执行一次，0秒开始执行
        {key_proc,10,0},   //按键处理任务，10ms执行一次，0秒开始执行
        {lock_proc,30,0},   //门锁处理任务，30ms执行一次，0秒开始执行
        {BrewingControl_Task,50,0}, //冲泡控制任务，50ms执行一次
        //{temp_proc,1000,0},//温度处理任务，1000ms=1s执行一次
        //{water_level_proc,10,0},//水位检测处理任务
};
unsigned char task_num;//任务数量变量
void scheduler_init()
{
    task_num=sizeof(scheduler_task_t)/sizeof(task_t);
}
void scheduler_run()
{
    unsigned char i;
    for(i=0;i<task_num;i++)
    {
        unsigned long int now_time=uwtick;
        if(now_time>=scheduler_task_t[i].rate_ms+scheduler_task_t[i].last_run)
        {
            scheduler_task_t[i].last_run=now_time;
            scheduler_task_t[i].task_func();
        }
    }
}


/*主函数*/
int main(void)
{
    NVIC_PriorityGroupConfig(NVIC_PriorityGroup_2);//中断优先级分组
    SystemCoreClockUpdate();//系统时钟更新

    USART_Printf_Init(115200);//串口初始化
    //printf("hello\n");//测试

//    //测试代码
//    Delay_Init();//延时函数初始化
//    Heater_Init();   // 初始化加热器驱动
//    // 测试加热器功能 1秒钟加热
//    Heater_Start();
//    Delay_Ms(10000);  // 延时  1秒，需要先调用 Delay_Init() 函数初始化
//    Heater_Stop();
//
//    //ds18b20
//    DS18B20_Init(&ds18b20, threshold);  // 初始化DS18B20
//    DS18B20_SetCallback(&ds18b20, TempAlarmHandler); // 设置回调函数
    // 初始化DS18B20，设置报警阈值为40度
//    printf("初始化中...\n");
//    if (DS18B20_Init(&ds18b20, 40.0f) != DS18B20_OK) {
//        printf("DS18B20初始化失败，请检查连接\n");
//        while(1);
//    }
//    // 设置回调函数
//    DS18B20_SetCallback(&ds18b20, TempAlarmHandler);


//    //测试
//    //这里是舵机测试代码，需要先初始化pwm功能
//    TIM2_PWM_Init();//这里初始化TIM2定时器PWM功能
//    //lock(0);//开锁测试
//    // 测试舵机正转最大速度
//    stir_360(1, 100); // 方向1=正转，速度100=最大
//    Delay_Ms(2000);   // 延时2秒
//    // 测试舵机反转中等速度
//    stir_360(2, 50);  // 方向2=反转，速度50=中等
//    Delay_Ms(2000);   // 延时2秒
//    // 停止舵机
//    stir_360(0, 0);   // 停止
//    Delay_Ms(2000);   // 延时2秒
//    // 另一种控制方式 stir函数，用于简单的开关控制
//    stir(1);//1=开启(默认正转最大速度), 0=停止
//    Delay_Ms(2000);
//    stir(0); // 停止


    // 系统初始化
    Delay_Init();                    // 延时函数初始化
    Heater_Init();                   // 加热器初始化
    WaterPump_Init();                // 水泵初始化
    WaterLevel_Init();               // 水位检测初始化
    TIM2_PWM_Init();                 // 舵机PWM初始化

    // DS18B20初始化
    printf("初始化中...\n");
    if (DS18B20_Init(&ds18b20, 40.0f) != DS18B20_OK) {
        printf("DS18B20初始化失败，请检查连接\n");
        while(1);
    }
    DS18B20_SetCallback(&ds18b20, TempAlarmHandler);

    // 冲泡控制初始化
    BrewingControl_Init();

    //界面
    LCD_Init();
    LCD_Fill(0,0,127,127,WHITE);//这里是清屏函数，清屏为白色，这个函数的参数是起始坐标和结束坐标以及颜色
    //lcd_show_chinese(35,32,"智能门锁",RED,WHITE,16,0);//显示中文
    key_init();//矩阵按键初始化
    Tim3_Init(1000,96-1);//定时器3初始化
    scheduler_init();//任务调度器初始化

    // 启动冲泡控制流程
    printf("系统初始化完成，冲泡控制系统已启动\n");
    BrewingControl_Start();

    while(1)
    {
      scheduler_run();//任务调度器运行
    }
}


// 温度报警回调函数（这里用户自定义，比如点亮LED或者其他）
void TempAlarmHandler(float temp)
{
    printf("温度报警触发，当前温度: %.2f°C\n", temp);
    // 例如：GPIO_SetBits(GPIOC, GPIO_Pin_13); // 点亮报警LED
}


