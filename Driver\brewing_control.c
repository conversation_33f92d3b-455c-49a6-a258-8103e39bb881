#include "brewing_control.h"
#include "water_pump.h"
#include "heater.h"
#include "water_level.h"
#include "timer.h"
#include "ds18b20.h"

// ??????????
BrewingControl_t g_brewing_ctrl;
extern unsigned long int uwtick; // ????????
extern DS18B20_HandleTypeDef ds18b20; // ???????????

// ???ò???
#define TEMP_RISE_TARGET    2.0f    // ??????????(1-2?????)
#define REST_DURATION       30000   // ???????30??(ms)
#define STIR_MAX_SPEED      100     // ??????????
#define WATER_TIMEOUT       60000   // ?????????60??(ms)
#define HEAT_TIMEOUT        300000  // ????????5????(ms)

/**
 * @brief ???????????
 */
void BrewingControl_Init(void)
{
    g_brewing_ctrl.current_state = BREWING_IDLE;
    g_brewing_ctrl.water_start_time = 0;
    g_brewing_ctrl.water_duration = 0;
    g_brewing_ctrl.rest_start_time = 0;
    g_brewing_ctrl.initial_temp = 0.0f;
    g_brewing_ctrl.temp_target_reached = 0;
    g_brewing_ctrl.water_level_reached = 0;
}

/**
 * @brief ???????????
 */
void BrewingControl_Start(void)
{
    if(g_brewing_ctrl.current_state == BREWING_IDLE) {
        g_brewing_ctrl.current_state = BREWING_WAIT_WATER_CONFIRM;
        printf("???????????????????1?????\n");
    }
}

/**
 * @brief ??????????
 */
void BrewingControl_Stop(void)
{
    // ???????豸
    WaterPump_Control(PUMP_1, PUMP_OFF_STATE);
    WaterPump_Control(PUMP_2, PUMP_OFF_STATE);
    Heater_Stop();
    stir(0);
    
    // ??????
    g_brewing_ctrl.current_state = BREWING_IDLE;
    printf("????????????\n");
}

/**
 * @brief ????????
 */
BrewingState_t BrewingControl_GetState(void)
{
    return g_brewing_ctrl.current_state;
}

/**
 * @brief ????????????
 * @param key_val ?????(1-4???????1-4)
 */
void BrewingControl_KeyHandler(uint8_t key_val)
{
    switch(g_brewing_ctrl.current_state) {
        case BREWING_WAIT_WATER_CONFIRM:
            if(key_val == 1) { // ????1???????
                g_brewing_ctrl.current_state = BREWING_ADDING_WATER;
                g_brewing_ctrl.water_start_time = uwtick;
                WaterPump_Control(PUMP_1, PUMP_ON_STATE);
                printf("???????????1????\n");
            }
            break;
            
        case BREWING_WAIT_HEAT_CONFIRM:
            if(key_val == 2) { // ????2????????
                g_brewing_ctrl.current_state = BREWING_HEATING;
                DS18B20_ReadRealtimeTemp(&ds18b20);
                g_brewing_ctrl.initial_temp = ds18b20.current_temp;
                Heater_Start();
                printf("??????????????: %.2f??C\n", g_brewing_ctrl.initial_temp);
            }
            break;
            
        case BREWING_WAIT_STIR_CONFIRM:
            if(key_val == 3) { // ????3???????????
                g_brewing_ctrl.current_state = BREWING_STIRRING;
                stir_360(1, STIR_MAX_SPEED); // ?????????
                printf("???????\n");
            }
            break;
            
        case BREWING_STIRRING:
            if(key_val == 4) { // ????4????????
                g_brewing_ctrl.current_state = BREWING_WAIT_STOP_STIR;
                stir(0);
                printf("????????\n");
            }
            break;
            
        default:
            break;
    }
}

/**
 * @brief ?????????????
 */
void BrewingControl_Task(void)
{
    uint32_t current_time = uwtick;
    
    switch(g_brewing_ctrl.current_state) {
        case BREWING_ADDING_WATER:
            // ????λ????
            if(WaterLevel_Detect()) {
                g_brewing_ctrl.water_duration = current_time - g_brewing_ctrl.water_start_time;
                WaterPump_Control(PUMP_1, PUMP_OFF_STATE);
                g_brewing_ctrl.current_state = BREWING_WAIT_HEAT_CONFIRM;
                printf("??????????: %lu ms?????????2??????\n", g_brewing_ctrl.water_duration);
            }
            // ??????????
            else if((current_time - g_brewing_ctrl.water_start_time) >= WATER_TIMEOUT) {
                WaterPump_Control(PUMP_1, PUMP_OFF_STATE);
                g_brewing_ctrl.current_state = BREWING_IDLE;
                printf("????????????λ\n");
            }
            break;
            
        case BREWING_HEATING:
            // ?????????????1-2?????
            DS18B20_ReadRealtimeTemp(&ds18b20);
            if((ds18b20.current_temp - g_brewing_ctrl.initial_temp) >= TEMP_RISE_TARGET) {
                Heater_Stop();
                g_brewing_ctrl.current_state = BREWING_WAIT_STIR_CONFIRM;
                printf("???????????????: %.2f??C?????????3??????\n",
                       ds18b20.current_temp - g_brewing_ctrl.initial_temp);
            }
            // ?????????
            else if((current_time - g_brewing_ctrl.water_start_time) >= HEAT_TIMEOUT) {
                Heater_Stop();
                g_brewing_ctrl.current_state = BREWING_IDLE;
                printf("???????????λ\n");
            }
            break;
            
        case BREWING_WAIT_STOP_STIR:
            // ????????
            g_brewing_ctrl.current_state = BREWING_RESTING;
            g_brewing_ctrl.rest_start_time = current_time;
            printf("???????30??\n");
            break;
            
        case BREWING_RESTING:
            // ????????????30??
            if((current_time - g_brewing_ctrl.rest_start_time) >= REST_DURATION) {
                g_brewing_ctrl.current_state = BREWING_FILTERING;
                WaterPump_Control(PUMP_2, PUMP_ON_STATE);
                printf("???????????????????2????\n");
            }
            break;
            
        case BREWING_FILTERING:
            // ???????????????????
            if((current_time - g_brewing_ctrl.rest_start_time - REST_DURATION) >= g_brewing_ctrl.water_duration) {
                WaterPump_Control(PUMP_2, PUMP_OFF_STATE);
                g_brewing_ctrl.current_state = BREWING_COMPLETE;
                printf("???????????????????\n");
            }
            break;
            
        case BREWING_COMPLETE:
            // ?????????????????
            break;
            
        default:
            break;
    }
}

/**
 * @brief ?????????
 * @param temp ??????
 */
void BrewingControl_TempCallback(float temp)
{
    // ?????????????????????
    if(g_brewing_ctrl.current_state == BREWING_HEATING) {
        printf("?????У???????: %.2f??C\n", temp);
    }
}

/**
 * @brief ?λ???????
 * @param level ?λ??(1-????0-δ??)
 */
void BrewingControl_WaterLevelCallback(uint8_t level)
{
    g_brewing_ctrl.water_level_reached = level;
}
