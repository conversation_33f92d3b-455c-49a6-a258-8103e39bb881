#include "ds18b20.h"

// 设置DQ为开漏输出模式
void DS18B20_IO_OUT(void) {
    GPIO_InitTypeDef GPIO_InitStructure;
    GPIO_InitStructure.GPIO_Pin = DS18B20_GPIO_PIN;
    GPIO_InitStructure.GPIO_Mode = GPIO_Mode_Out_OD;   // 使用开漏输出
    GPIO_InitStructure.GPIO_Speed = GPIO_Speed_50MHz;
    GPIO_Init(DS18B20_GPIO_PORT, &GPIO_InitStructure);
}

// 设置DQ为输入模式，上拉输入
void DS18B20_IO_IN(void) {
    GPIO_InitTypeDef GPIO_InitStructure;
    GPIO_InitStructure.GPIO_Pin = DS18B20_GPIO_PIN;
    GPIO_InitStructure.GPIO_Mode = GPIO_Mode_IPU;
    GPIO_Init(DS18B20_GPIO_PORT, &GPIO_InitStructure);
}

// 复位DS18B20，等待存在脉冲
void DS18B20_Reset(void) {
    DS18B20_IO_OUT();
    GPIO_ResetBits(DS18B20_GPIO_PORT, DS18B20_GPIO_PIN);
    Delay_us(480);          // 拉低至少480us
    GPIO_SetBits(DS18B20_GPIO_PORT, DS18B20_GPIO_PIN);
    Delay_us(70);           // 释放后等待70us让DS18B20回复存在脉冲
    DS18B20_IO_IN();        // 设置为输入等待存在脉冲检测
}

// 检测DS18B20存在脉冲，存在返回1，无响应返回0
uint8_t DS18B20_Check(void) {
    uint8_t retry = 0;

    while(GPIO_ReadInputDataBit(DS18B20_GPIO_PORT, DS18B20_GPIO_PIN)) {
        retry++;
        Delay_us(1);
        if(retry > 100) return 0;  // 超过100us没有拉低，未响应
    }

    retry = 0;
    while(!GPIO_ReadInputDataBit(DS18B20_GPIO_PORT, DS18B20_GPIO_PIN)) {
        retry++;
        Delay_us(1);
        if(retry > 240) return 0;  // 存在脉冲超时异常
    }

    return 1; // 正常存在脉冲
}

// 写一个bit到总线
void DS18B20_WriteBit(uint8_t bit) {
    DS18B20_IO_OUT();
    GPIO_ResetBits(DS18B20_GPIO_PORT, DS18B20_GPIO_PIN);

    if(bit) {
        Delay_us(10); // 写1拉低10us
        GPIO_SetBits(DS18B20_GPIO_PORT, DS18B20_GPIO_PIN);
        Delay_us(55);
    } else {
        Delay_us(65); // 写0拉低65us
        GPIO_SetBits(DS18B20_GPIO_PORT, DS18B20_GPIO_PIN);
        Delay_us(5);
    }
}

// 读取一个bit
uint8_t DS18B20_ReadBit(void) {
    uint8_t bit;
    DS18B20_IO_OUT();
    GPIO_ResetBits(DS18B20_GPIO_PORT, DS18B20_GPIO_PIN);
    Delay_us(3); // 拉低3us
    GPIO_SetBits(DS18B20_GPIO_PORT, DS18B20_GPIO_PIN);
    DS18B20_IO_IN();
    Delay_us(10); // 等待10us左右采样
    bit = GPIO_ReadInputDataBit(DS18B20_GPIO_PORT, DS18B20_GPIO_PIN);
    Delay_us(53); // 保证时隙结束
    return bit;
}

// 写一个字节
void DS18B20_WriteByte(uint8_t dat) {
    uint8_t i;
    for(i=0; i<8; i++) {
        DS18B20_WriteBit(dat & 0x01);
        dat >>= 1;
    }
}

// 读一个字节
uint8_t DS18B20_ReadByte(void) {
    uint8_t dat=0,i;
    for(i=0; i<8; i++) {
        dat >>= 1;
        if(DS18B20_ReadBit()) dat |= 0x80;
    }
    return dat;
}

// 初始化DS18B20
uint8_t DS18B20_Init(DS18B20_HandleTypeDef *hds, float threshold) {
    RCC_APB2PeriphClockCmd(DS18B20_GPIO_CLK, ENABLE);

    DS18B20_IO_OUT();
    GPIO_SetBits(DS18B20_GPIO_PORT, DS18B20_GPIO_PIN); // 空闲高电平
    Delay_ms(10);

    hds->threshold = threshold;
    hds->alarm_flag = 0;
    hds->TempWarningCallback = NULL;

    DS18B20_Reset();
    if(!DS18B20_Check()) {
        printf("DS18B20初始化失败，没有检测到设备\n");
        return DS18B20_ERR_NO_DEVICE;
    }
    return DS18B20_OK;
}

// 启动温度转换
uint8_t DS18B20_StartConvert(void) {
    DS18B20_Reset();
    if(!DS18B20_Check()) return DS18B20_ERR_NO_DEVICE;
    DS18B20_WriteByte(0xCC); // 跳过ROM
    DS18B20_WriteByte(0x44); // 触发转换命令
    return DS18B20_OK;
}

// 读取温度原始值
float DS18B20_ReadTempRaw(void) {
    uint8_t TL, TH;
    int16_t temp;

    if(DS18B20_StartConvert() != DS18B20_OK) return -1000.0f;
    Delay_ms(750); // 等待最大转换时间750ms

    DS18B20_Reset();
    if(!DS18B20_Check()) return -1000.0f;

    DS18B20_WriteByte(0xCC);
    DS18B20_WriteByte(0xBE); // 读取寄存器

    TL = DS18B20_ReadByte();
    TH = DS18B20_ReadByte();

    temp = (TH << 8) | TL;

    return temp * 0.0625f;
}

// 设置报警阈值
void DS18B20_SetThreshold(DS18B20_HandleTypeDef *hds, float threshold) {
    hds->threshold = threshold;
}

// 设置温度报警回调
void DS18B20_SetCallback(DS18B20_HandleTypeDef *hds, void (*cb)(float)) {
    hds->TempWarningCallback = cb;
}

// 获取报警状态
uint8_t DS18B20_GetAlarmFlag(DS18B20_HandleTypeDef *hds) {
    return hds->alarm_flag;
}

// 读取实时温度，更新结构体，带异常判断与报警回调
uint8_t DS18B20_ReadRealtimeTemp(DS18B20_HandleTypeDef *hds) {
    float temp = DS18B20_ReadTempRaw();
    if(temp <= -100.0f) return DS18B20_ERR_READ;
    hds->current_temp = temp;

    if(temp >= hds->threshold) {
        if(hds->alarm_flag == 0 && hds->TempWarningCallback) {
            hds->TempWarningCallback(temp);
        }
        hds->alarm_flag = 1;
    } else {
        hds->alarm_flag = 0;
    }
    return DS18B20_OK;
}
